function extractLogs(logs) {
  const childLogs = [];

  for (const outerArray of logs) {
    for (const innerArray of outerArray) {
      for (const item of innerArray) {
        childLogs.push(item);
      }
    }
  }

  return childLogs;
}

const TOPIC = {
  '0x0968da03e3daa1681e040ac06832dc8b74955d289deeda5814ce26119323f010':
    'RewardClaimed',
  '0xc8344f969f4a0db6aa6237d1663dee455f6b28a72f312221ef4d145d91814b48':
    'NFTVoucherRedeemed',
  '0xf9104b3284706074ad5a2ba778d3611563ddc5c3ff1da677ba09a2bcfb4930ee':
    'NFTVoucherSent',
  '0x0f4a52d8b6314b187b37f57abf3c18b407c3c10fa736754a3628867864e1ea90':
    'TokenSent',
  '0xb9478b9763e1dacb001adaa13460c5ac080b36feb60e0e34ee96a1016437d06d':
    'NFTRewardClaimed',
};

function isIncludeTopic(topics) {
  const filterTopics = [];
  for (const key in TOPIC) {
    filterTopics.push(key);
  }

  for (let index = 0; index < topics.length; index++) {
    const topic = topics[index];
    if (filterTopics.includes(topic)) {
      return true;
    }
  }
  return false;
}

function main(stream) {
  try {
    const CONTRACT_ADDRESS = '0x34e7a75dec1bcf82e96e694079e35d2848797c79';

    const filterLogs = [];
    const data = stream.data ? stream.data : stream;
    const logs = extractLogs(data);
    for (let index2 = 0; index2 < logs.length; index2++) {
      const log = logs[index2];
      if (
        log.address.toLowerCase() === CONTRACT_ADDRESS.toLowerCase() &&
        log.topics &&
        isIncludeTopic(log.topics)
      ) {
        filterLogs.push({
          eventName: TOPIC[log.topics[0]],
          transactionHash: log.transactionHash,
          logIndex: log.logIndex,
        });
      }
    }
    if (filterLogs.length === 0) {
      return null;
    }

    return filterLogs;
  } catch (error) {
    return { error: error.message };
  }
}
