{"name": "new3", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:watch": "dotenvx run -f .env.local -- nest start --watch", "start:debug": "dotenvx run -f .env.local -- nest start --debug --watch", "start:dev": "dotenvx run -f .env.dev -- node dist/main", "start:prod": "dotenvx run -f .env.prod -- node dist/main", "typeorm": "ts-node ./node_modules/typeorm/cli", "migration:run": "dotenvx run -f .env.dev -- npm run typeorm migration:run -- -d ./src/config/typeorm.ts", "migration:generate": "dotenvx run -f .env.dev -- npm run typeorm -- -d ./src/config/typeorm.ts migration:generate ./src/migrations/%npm_config_name%", "migration:create": "npm run typeorm -- migration:create ./src/migrations/%npm_config_name%", "migration:revert": "dotenvx run -f .env.dev -- npm run typeorm -- -d ./src/config/typeorm.ts migration:revert", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "dotenvx run -f .env.test -- jest --config jest.config.json --detectOpenHandles --runInBand --bail", "test:file": "dotenvx run -f .env.test -- jest src/test/%npm_config_name%.spec.ts --config jest.config.json --detectOpenHandles --runInBand", "test:watch": "dotenvx run -f .env.test -- jest --watch", "test:cov": "dotenvx run -f .env.test -- jest --coverage", "test:debug": "dotenvx run -f .env.test -- node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "dotenvx run -f .env.test -- jest --config ./test/jest-e2e.json", "prepare": "husky"}, "dependencies": {"@aptos-labs/ts-sdk": "5.1.0", "@aws-sdk/client-s3": "^3.609.0", "@aws-sdk/s3-request-presigner": "^3.609.0", "@dotenvx/dotenvx": "^0.43.2", "@kieutuan218/abi-decoder": "^2.3.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/bull": "^10.1.1", "@nestjs/cache-manager": "^2.2.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.0.2", "@nestjs/swagger": "^8.1.0", "@nestjs/typeorm": "^10.0.2", "axios": "^1.7.2", "bcrypt": "^5.1.1", "bignumber.js": "^9.1.2", "bull": "^4.12.9", "cache-manager": "^4.1.0", "cache-manager-redis-store": "^2.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.18", "dotenv": "^16.4.5", "ethers": "^6.15.0", "fast-csv": "^5.0.2", "got": "^11.8.6", "handlebars": "^4.7.8", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mysql2": "^3.10.2", "nanoid": "^3.3.11", "nestjs-typeorm-paginate": "^4.0.4", "nodemailer": "^6.10.0", "nodemailer-express-handlebars": "^7.0.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "redis": "^4.6.14", "redlock": "^5.0.0-beta.2", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "twitter-api-v2": "^1.17.2", "typeorm": "^0.3.20", "uuid": "^9.0.1", "viem": "^2.33.3", "winston": "^3.13.0", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/jsonwebtoken": "^9.0.6", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.0", "@types/xlsx": "^0.0.36", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^9.0.11", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["npm run lint", "npm run format", "git add ."]}}