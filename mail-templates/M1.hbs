<!DOCTYPE html
  PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;600&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: Poppins, Tahoma;
      font-style: normal;
      margin: 0;
    }
    .container {
      width: 600px;
      margin: 0 auto;
      text-align: center;
      padding: 24px 0 16px;
    }     
    .content {
      text-align: left;
      padding: 42px 50px 24px;
      margin : 32px 0 54px;
      background: #FFFFFF;

      font-weight: normal;
      font-size: 14px;
      line-height: 21px;
      letter-spacing: -0.003em;
      color: #4F5566;
    }
    .content .title {
      font-weight: 600;
      font-size: 32px;
      line-height: 48px;
      color: #23262F;
      margin: 0 0 14px;
    }
    .content p {
      font-weight: normal;
      font-size: 14px;
      line-height: 21px;
      letter-spacing: -0.003em;
      color: #4F5566;
    }

    .content_code {
      margin: 0 auto 16px;
      width: fit-content;
      padding: 12px 64px;
      border: 1px solid #2e303e;
      border-radius: 20px;
      font-weight: 600;
      font-size: 36px;
      line-height: 54px;
      letter-spacing: 0.1em;
      color: #2d27ff;
      margin-bottom: 16px;
    }


    .social {
      padding-bottom: 20px;
      border-bottom: 1px solid #E6E8EC;
      width: 500px;
      margin: 0 auto;
    }
    .social img {
      width: 30px;
      height: 30px;
      margin: 0 10px;
    }

    button {
      cursor: pointer;
      border: none;
      border-radius: 8px;
      background: var(--brand-colors-logo, linear-gradient(135deg, #FF0A6C 0%, #2D27FF 100%));
    }

    button a {
      text-decoration: unset;
      display: inline-flex;
      padding: 6px 10px;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 4px;
      color: #FFF !important;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 21px;
    }

    .button-confirm {
      padding: 10px 14px;
      background: linear-gradient(135deg, #FF0A6C -16.8%, #2D27FF 138.64%);
      border: none;
      border-radius: 30px;
      font-weight: 600;
      font-size: 14px;
      line-height: 21px;
      color: #FFFFFF !important;
    }
    .footer {
      color: #4F5566;
    }
    .footer-content {
      font-size: 16px;
      line-height: 26px;
    }
    .footer-copyright {
      font-weight: 500;
      font-size: 14px;
      line-height: 21px;
    }
    @media screen and (max-width: 565px) {
      .container {
        width: 100%;
      }
      .content {
        padding: 42px 16px 24px;
      }
      .content .title {
        font-size: 24px;
        line-height: 36px;
      }
      .social {
        width: 320px;
      }
    }
  </style>
</head>

<body>
  <div class='container-wrapper' style='background-color: #eff3f6fe'>
    <div class='container'>
      <div class='header'>
      </div>
      <div class="content">
        <div class="content-email">
          <p>Hello, </p>
          <p>Please use the code below to verify your email.</p>
          <p><b>{{otp}}</b></p>
          <p>Please note that the code is only valid for 30 minutes.</p>
          <p>If you have any questions, feel free to contact our customer support.</p>
          <p>Thank you!</p>
          <p>NEX3 Solution</p>
        </div>
      </div>
      <div class='footer'>
      </div>
    </div>
</body>

</html>