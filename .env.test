# Enviroment (dev/stg/prod)
ENV=test

# Log monitor (google chat)
LOG_MONITOR=

# Common config
PORT=9000
LOG_LEVEL=log,error,warn,debug,verbose
API_PREFIX=
CORS_ORIGIN=*
API_URL=https://new3-api.ekoios.net

# JWT
JWT_SECRET=ekotek@s3cret!
JWT_SECRET_MERCHANT=merchant@s3cret!
JWT_ADMIN_SECRET=ekotek@s3cret!@12sadasd124sd@_saa
JWT_BRAND_ADMIN_SECRET=ekotek@s3cret!
JWT_WEBHOOK_SECRET=ekotek@s3cret!@12sadasd124sd@_saa_webhook
JWT_EXPIRATION_TIME=90d
JWT_EXPIRATION_TIME_MERCHANT=1d

# Encrypt / Decrypt
CRYPTO_SECRET=ekotek@s3cret!

# Redis
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_TTL=86400
REDIS_PREFIX=nex3_test

# DB
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=123456
DB_NAME=nex3_test

# Feed price
COINMARKETCAP_API_KEY=161b07ec-c8ee-4a2e-95a0-8eb9d3bc94c2

# AWS
AWS_REGION=ap-southeast-1
AWS_S3_BUCKET=
AWS_S3_URL=
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=

# X Api
X_BEARER_TOKEN=AAAAAAAAAAAAAAAAAAAAAJY13QEAAAAAhPQewEYh2OmMrB5TQ2nWlWrSh2Q%3DVkIdGcI5dvtf7PIt2AtFbTAGYlPijbL4lcLaCwuf1MqXnHBvjS
X_PREFIX_URL=https://api.twitter.com/

# MAIL
MAIL_HOST=smtp.gmail.com
MAIL_USER=<EMAIL>
MAIL_PASSWORD=ipdjigiachbpiqpz
MAIL_PORT=465

# NEX3 CHAIN
CHAIN_ID=84532
TOKEN_ADDRESS=******************************************