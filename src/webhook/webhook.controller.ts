import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/guard/jwt-auth.guard';
import { JwtWebhookAuthGuard } from 'src/auth/guard/jwt-webhook-auth.guard';
import { WebhookService } from './webhook.service';

@Controller('webhook')
@UseGuards(JwtWebhookAuthGuard)
export class WebhookController {
  constructor(private readonly webhookService: WebhookService) {}

  @Post()
  process(@Body() requestData: any) {
    return this.webhookService.process(requestData);
  }
}
