import { Body, Injectable, Logger } from '@nestjs/common';
import { CommonService } from 'src/common-services/common.service';
import { ApiError } from 'src/common/api';
import { ChainName } from 'src/entities/chain.entity';
import { DataSource, Repository } from 'typeorm';
import { WebhookDataDto } from './dto/webhook-data.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { QueueService } from 'src/queue/queue.service';

@Injectable()
export class WebhookService {
  private readonly logger = new Logger(WebhookService.name);

  constructor(
    private commonService: CommonService,
    private queueService: QueueService,
    private dataSource: DataSource,
  ) {}

  async process(requestData: any) {
    this.logger.debug(
      `process(): requestData = ${JSON.stringify(requestData)}`,
    );
    if (!requestData || requestData.length === 0) {
      return;
    }
    const logs = requestData as WebhookDataDto[];
    for (let index = 0; index < logs.length; index++) {
      const log = logs[index];
      await this.queueService.addWebhookQueue(log);
    }
  }
}
