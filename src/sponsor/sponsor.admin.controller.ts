import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Post,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { JwtAdminAuthGuard } from 'src/auth/guard/jwt-admin-auth.guard';
import { CreateSponsorAssetDto } from 'src/brands/dto/create-sponsor-asset.dto';
import { UpdateSponsorDto } from './dto/update-sponsor.dto';
import { SponsorAdminService } from './sponsor.admin.service';

@Controller('admin/sponsors')
@UseGuards(JwtAdminAuthGuard)
export class SponsorAdminController {
  constructor(private readonly sponsorService: SponsorAdminService) {}

  @Post()
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update sponsor' })
  updateSponsor(@Body() requestData: UpdateSponsorDto) {
    return this.sponsorService.updateSponsor(requestData);
  }

  @Post('create-sponsor-asset')
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create sponsor asset' })
  createSponsorAsset(@Body() requestData: CreateSponsorAssetDto) {
    return this.sponsorService.createSponsorAsset(requestData);
  }
}
