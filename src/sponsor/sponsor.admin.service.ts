import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateSponsorAssetDto } from 'src/brands/dto/create-sponsor-asset.dto';
import { CommonService } from 'src/common-services/common.service';
import { SponsorAsset } from 'src/entities/sponsor-asset.entity';
import { Sponsor } from 'src/entities/sponsor.entity';
import { DataSource, Repository } from 'typeorm';
import { UpdateSponsorDto } from './dto/update-sponsor.dto';

@Injectable()
export class SponsorAdminService {
  private readonly logger = new Logger(SponsorAdminService.name);

  constructor(
    private commonService: CommonService,
    private dataSource: DataSource,
    @InjectRepository(Sponsor)
    private sponsorRepository: Repository<Sponsor>,
    @InjectRepository(SponsorAsset)
    private sponsorAssetRepository: Repository<SponsorAsset>,
  ) {}

  async updateSponsor(requestData: UpdateSponsorDto) {
    return this.sponsorRepository.upsert(requestData, [
      'name',
      'sponsorVaultAddress',
    ]);
  }

  async createSponsorAsset(requestData: CreateSponsorAssetDto) {
    const { sponsorId, assetId, totalReward } = requestData;
    await this.sponsorAssetRepository.save({
      sponsorId,
      assetId,
      totalReward,
      remainReward: totalReward,
    });

    return {
      message: 'Sponsor asset created successfully',
    };
  }
}
