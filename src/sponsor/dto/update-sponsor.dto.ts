import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class UpdateSponsorDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsOptional()
  imageUrl: string;

  @IsString()
  @IsOptional()
  description: string;

  @IsString()
  @IsOptional()
  website: string;

  @IsString()
  @IsOptional()
  email: string;

  @IsString()
  @IsNotEmpty()
  sponsorVaultAddress: string;
}
