import {
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/guard/jwt-auth.guard';
import { SponsorService } from './sponsor.service';

@Controller('/sponsors')
@UseGuards(JwtAuthGuard)
export class SponsorController {
  constructor(private readonly sponsorService: SponsorService) {}

  @Get(':id')
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: `Get sponsor's detail` })
  getDetailSponsor(@Param('id') id: number) {
    return this.sponsorService.getDetailSponsor(+id);
  }
}
