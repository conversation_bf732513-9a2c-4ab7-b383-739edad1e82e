import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CommonModule } from 'src/common-services/common.module';
import { SponsorAsset } from 'src/entities/sponsor-asset.entity';
import { Sponsor } from 'src/entities/sponsor.entity';
import { SponsorAdminController } from './sponsor.admin.controller';
import { SponsorAdminService } from './sponsor.admin.service';
import { SponsorController } from './sponsor.controller';
import { SponsorService } from './sponsor.service';

@Module({
  imports: [CommonModule, TypeOrmModule.forFeature([Sponsor, SponsorAsset])],
  providers: [SponsorAdminService, SponsorService],
  controllers: [SponsorAdminController, SponsorController],
  exports: [SponsorAdminService],
})
export class SponsorModule {}
