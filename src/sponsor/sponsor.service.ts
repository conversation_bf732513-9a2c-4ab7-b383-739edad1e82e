import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Sponsor } from 'src/entities/sponsor.entity';
import { Repository } from 'typeorm';

@Injectable()
export class SponsorService {
  private readonly logger = new Logger(SponsorService.name);

  constructor(
    @InjectRepository(Sponsor)
    private sponsorRepository: Repository<Sponsor>,
  ) {}

  async getDetailSponsor(id: number) {
    return this.sponsorRepository.findOne({
      where: { id },
      select: ['id', 'name', 'imageUrl', 'description', 'website', 'email'],
    });
  }
}
