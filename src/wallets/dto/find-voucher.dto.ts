import { IsEnum, IsOptional } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { SearchDto } from 'src/common/dto.common';

export enum VoucherFilterType {
  ACTIVE = 'active',
  USED = 'used',
  EXPIRED = 'expired',
}

export class FindVoucherDto extends SearchDto {
  @ApiPropertyOptional({ enum: VoucherFilterType })
  @IsEnum(VoucherFilterType)
  @IsOptional()
  filterType?: VoucherFilterType;
}
