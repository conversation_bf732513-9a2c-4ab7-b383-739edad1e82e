import { Module } from '@nestjs/common';
import { WalletService } from './wallet.service';
import { WalletController } from './wallet.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from 'src/entities/user.entity';
import { Asset } from 'src/entities/asset.entity';
import { CommonModule } from 'src/common-services/common.module';
import { UserAsset } from 'src/entities/user-asset.entity';

@Module({
  controllers: [WalletController],
  providers: [WalletService],
  imports: [TypeOrmModule.forFeature([User, Asset, UserAsset]), CommonModule],
})
export class WalletModule {}
