import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation } from '@nestjs/swagger';
import { WalletService } from './wallet.service';
import { User } from 'src/common/decorator/user.decorator';
import { UserPayload } from 'src/entities/user.entity';
import { JwtAuthGuard } from 'src/auth/guard/jwt-auth.guard';
import { CheckPinDto } from './dto/check-pin.dto';
import { ChangePinDto } from './dto/change-pin.dto';
import { FindVoucherDto } from './dto/find-voucher.dto';

@ApiBearerAuth()
@ApiTags('Wallets')
@UseGuards(JwtAuthGuard)
@Controller('wallets')
export class WalletController {
  constructor(private readonly walletService: WalletService) {}

  @ApiOperation({ summary: 'Get vouchers' })
  @Get('vouchers')
  async getVouchers(
    @User() currentUser: UserPayload,
    @Query() requestData: FindVoucherDto,
  ) {
    const answer = await this.walletService.getVouchers(
      currentUser,
      requestData,
    );
    return answer;
  }

  @ApiOperation({ summary: 'Get voucher detail' })
  @Get('vouchers/:id')
  async getDetailVoucher(
    @User() currentUser: UserPayload,
    @Param('id') id: number,
  ) {
    const answer = await this.walletService.getDetailVoucher(currentUser, +id);
    return answer;
  }

  @ApiOperation({ summary: 'Use a voucher' })
  @Post('vouchers/:id/use')
  async useVoucher(@Param('id') id: number, @User() currentUser: UserPayload) {
    return this.walletService.useVoucher(+id, currentUser);
  }

  @ApiOperation({ summary: 'Check wallet PIN' })
  @Post('check-pin')
  async checkPin(
    @User() currentUser: UserPayload,
    @Body() checkPinDto: CheckPinDto,
  ) {
    const answer = await this.walletService.checkPin(currentUser, checkPinDto);
    return answer;
  }

  @ApiOperation({ summary: 'Change wallet PIN' })
  @Post('change-pin')
  async changePin(
    @User() currentUser: UserPayload,
    @Body() changePinDto: ChangePinDto,
  ) {
    const answer = await this.walletService.changePin(
      currentUser,
      changePinDto,
    );
    return answer;
  }
}
