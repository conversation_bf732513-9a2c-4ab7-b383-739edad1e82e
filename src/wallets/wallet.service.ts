import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CommonService } from 'src/common-services/common.service';
import { ApiError } from 'src/common/api';
import { ErrorCode } from 'src/common/constants';
import { Utils } from 'src/common/utils';
import { Asset, AssetType, TokenType } from 'src/entities/asset.entity';
import { User, UserPayload } from 'src/entities/user.entity';
import { Repository } from 'typeorm';
import { ChangePinDto } from './dto/change-pin.dto';
import { CheckPinDto } from './dto/check-pin.dto';
import { FindVoucherDto, VoucherFilterType } from './dto/find-voucher.dto';
import { RedeemItemStatus } from 'src/entities/redeem-item.entity';
import { UserAsset, UserAssetStatus } from 'src/entities/user-asset.entity';

@Injectable()
export class WalletService {
  private readonly logger = new Logger(WalletService.name);

  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Asset)
    private assetRepository: Repository<Asset>,
    @InjectRepository(UserAsset)
    private userAssetRepository: Repository<UserAsset>,
    private readonly commonService: CommonService,
  ) {}

  async getVouchers(currentUser: UserPayload, requestData: FindVoucherDto) {
    const queryBuilder = this.userAssetRepository
      .createQueryBuilder('userAsset')
      .leftJoin('userAsset.redeemItem', 'redeemItem')
      .leftJoin('userAsset.asset', 'asset')
      .where('userAsset.userId = :userId', { userId: currentUser.id })
      .andWhere('asset.type = :type', { type: AssetType.NFT_VOUCHER })
      .select([
        'userAsset.id',
        'userAsset.status',
        'userAsset.usedAt',
        'userAsset.validTo',
        'userAsset.expiredAt',
        'asset.id',
        'asset.name',
        'asset.image',
        'redeemItem.id',
        'redeemItem.name',
        'redeemItem.description',
        'redeemItem.imageUrl',
      ]);

    switch (requestData.filterType) {
      case VoucherFilterType.ACTIVE:
        queryBuilder
          .andWhere('userAsset.status = :status', {
            status: UserAssetStatus.REDEEMED,
          })
          .orderBy('userAsset.updatedAt', 'DESC');
        break;

      case VoucherFilterType.USED:
        queryBuilder
          .andWhere('userAsset.status = :status', {
            status: UserAssetStatus.USED,
          })
          .orderBy('userAsset.usedAt', 'DESC');
        break;

      case VoucherFilterType.EXPIRED:
        queryBuilder
          .andWhere('userAsset.status = :status', {
            status: UserAssetStatus.EXPIRED,
          })
          .orderBy('userAsset.expiredAt', 'DESC');
        break;

      default:
        queryBuilder.andWhere('userAsset.status = :status', {
          status: UserAssetStatus.REDEEMED,
        });
        break;
    }

    if (requestData.keyword) {
      Utils.applySearch<UserAsset>(queryBuilder, requestData.keyword, [
        { name: 'redeemItem.name', isAbsolute: false },
      ]);
    }

    return Utils.paginate<UserAsset>(queryBuilder, requestData);
  }

  async getDetailVoucher(currentUser: UserPayload, id: number) {
    const voucher = await this.userAssetRepository
      .createQueryBuilder('userAsset')
      .leftJoin('userAsset.redeemItem', 'redeemItem')
      .leftJoin('userAsset.asset', 'asset')
      .leftJoin('redeemItem.sponsor', 'sponsor')
      .where('userAsset.userId = :userId AND userAsset.id = :voucherId', {
        userId: currentUser.id,
        voucherId: id,
      })
      .andWhere('asset.type = :type', { type: AssetType.NFT_VOUCHER })
      .select([
        'userAsset.id',
        'userAsset.code',
        'userAsset.status',
        'userAsset.usedAt',
        'userAsset.validFrom',
        'userAsset.validTo',
        'userAsset.expiredAt',
        'redeemItem.id',
        'redeemItem.name',
        'redeemItem.description',
        'redeemItem.imageUrl',
        'sponsor.id',
        'sponsor.name',
        'sponsor.imageUrl',
        'sponsor.description',
        'sponsor.website',
        'sponsor.email',
        'asset.id',
      ])
      .getOne();

    if (!voucher) {
      throw ApiError('', `Voucher doesn't exist`);
    }
    return voucher;
  }

  async useVoucher(id: number, currentUser: UserPayload) {
    const voucher = await this.userAssetRepository
      .createQueryBuilder('userAsset')
      .leftJoin('userAsset.redeemItem', 'redeemItem')
      .leftJoin('userAsset.asset', 'asset')
      .where('userAsset.id = :id AND userAsset.userId = :userId', {
        id,
        userId: currentUser.id,
      })
      .andWhere('userAsset.status = :status', {
        status: UserAssetStatus.REDEEMED,
      })
      .andWhere('asset.type = :type', { type: AssetType.NFT_VOUCHER })
      .select([
        'userAsset.id',
        'userAsset.status',
        'userAsset.code',
        'userAsset.validFrom',
        'userAsset.validTo',
        'redeemItem.id',
        'redeemItem.name',
        'redeemItem.type',
        'redeemItem.imageUrl',
        'redeemItem.validFrom',
        'redeemItem.validTo',
      ])
      .getOne();

    const now = new Date();
    if (voucher && voucher.redeemItem.validFrom > now) {
      throw ApiError('', `${voucher.redeemItem.validFrom}`);
    }
    if (!voucher || voucher.redeemItem.validTo < now) {
      throw ApiError(
        'E14',
        `Sorry, the voucher is not available. It may have expired or not be found.`,
      );
    }
    await this.userAssetRepository.update(id, {
      status: UserAssetStatus.USED,
      usedAt: new Date(),
    });
    return voucher;
  }

  async checkPin(currentUser: UserPayload, requestData: CheckPinDto) {
    try {
      const user = await this.userRepository.findOne({
        where: {
          id: currentUser.id,
          email: currentUser.email,
        },
        select: ['id', 'email', 'pinCode'],
      });

      const validPincode = await Utils.comparePincode(
        requestData.pinCode,
        user.pinCode,
      );

      if (!validPincode) {
        throw ApiError('E6', 'PIN is not correct');
      }

      return true;
    } catch (error) {
      this.logger.error(
        `Error checking PIN for user with email ${currentUser.email}: ${error.message}`,
      );
      throw error;
    }
  }

  async changePin(currentUser: UserPayload, requestData: ChangePinDto) {
    try {
      const user = await this.userRepository.findOne({
        where: {
          id: currentUser.id,
          email: currentUser.email,
        },
        select: ['id', 'email', 'pinCode'],
      });

      const samePincode = await Utils.comparePincode(
        requestData.pinCode,
        user.pinCode,
      );

      if (samePincode) {
        throw ApiError('E13', 'New PIN must be different from current PIN');
      }
      const hashedPincode = await Utils.hashPincode(requestData.pinCode);
      await this.userRepository.update(user.id, { pinCode: hashedPincode });

      this.logger.log(
        `Pin code updated successfully for user with email ${currentUser.email}`,
      );
      return true;
    } catch (error) {
      this.logger.error(
        `Error changing PIN for user with email ${currentUser.email}: ${error.message}`,
      );
      throw error;
    }
  }
}
