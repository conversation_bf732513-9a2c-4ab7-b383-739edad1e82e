import { Module } from '@nestjs/common';
import { CommonModule } from 'src/common-services/common.module';
import { TasksService } from './tasks.service';
import { FeedPricesModule } from 'src/feed-prices/feed-prices.module';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [CommonModule, FeedPricesModule, TypeOrmModule.forFeature([])],
  providers: [TasksService],
})
export class TaskModule {}
