import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { CommonService } from 'src/common-services/common.service';
import { Quest, QuestStatus } from 'src/entities/quest.entity';
import { RedeemItem, RedeemItemStatus } from 'src/entities/redeem-item.entity';
import { UserAsset, UserAssetStatus } from 'src/entities/user-asset.entity';
import { FeedPricesService } from 'src/feed-prices/feed-prices.service';
import { DataSource, Repository } from 'typeorm';

@Injectable()
export class TasksService {
  private readonly logger = new Logger(TasksService.name);

  constructor(
    private dataSource: DataSource,
    private readonly commonService: CommonService,
    private readonly feedPriceService: FeedPricesService,
  ) {}

  @Cron(CronExpression.EVERY_10_MINUTES)
  async syncPrice() {
    this.logger.debug('Sync price');
    await this.feedPriceService.syncPrice();
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleVoucherExpiration() {}

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleVoucherOutOfStock() {}

  //Job to handle the expiration of quests
  //Job is operational at midnight every day, this can be adjusted as needed
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleQuestExpiration() {
    this.logger.debug('Checking expired quests...');

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const now = new Date();

      await queryRunner.manager
        .createQueryBuilder()
        .update(Quest)
        .set({ status: QuestStatus.INACTIVE })
        .where('toDate < :now', { now })
        .andWhere('status = :status', { status: QuestStatus.ACTIVE })
        .execute();

      await queryRunner.commitTransaction();
      this.logger.log('[CRON] Updated expired quests successfully!');
    } catch (err) {
      await queryRunner.rollbackTransaction();
      this.logger.warn('[CRON] Failed to update expired quests');
    } finally {
      await queryRunner.release();
    }
  }

  //Job to handle the expiration of NFT vouchers
  //Job is operational at midnight every day, this can be adjusted as needed
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleNftVoucherExpiration() {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const now = new Date();

      await queryRunner.manager
        .createQueryBuilder()
        .update(UserAsset)
        .set({
          status: UserAssetStatus.EXPIRED,
          expiredAt: now,
        })
        .where('validTo < :now', { now })
        .andWhere('status != :expired', { expired: UserAssetStatus.EXPIRED })
        .andWhere('status != :used', { used: UserAssetStatus.USED })
        .execute();

      await queryRunner.manager
        .createQueryBuilder()
        .update(RedeemItem)
        .set({
          status: RedeemItemStatus.EXPIRED,
        })
        .where('validTo < :now', { now })
        .andWhere('status = :active', { active: RedeemItemStatus.ACTIVE })
        .execute();

      await queryRunner.commitTransaction();
      console.log(`[CRON] Updated expired vouchers successfully!`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error('[CRON] Failed to update expired vouchers:', error);
    } finally {
      await queryRunner.release();
    }
  }
}
