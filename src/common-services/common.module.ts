import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdminTransaction } from 'src/entities/admin-transaction.entity';
import { Article } from 'src/entities/article.entity';
import { Asset } from 'src/entities/asset.entity';
import { Brand } from 'src/entities/brand.entity';
import { Chain } from 'src/entities/chain.entity';
import { Config } from 'src/entities/config.entity';
import { Counter } from 'src/entities/counter.entity';
import { HotWallet } from 'src/entities/hot-wallet.entity';
import { Quest } from 'src/entities/quest.entity';
import { SponsorAsset } from 'src/entities/sponsor-asset.entity';
import { Sponsor } from 'src/entities/sponsor.entity';
import { Transaction } from 'src/entities/transaction.entity';
import { UserArticleAnswer } from 'src/entities/user-article-answer.entity';
import { UserAsset } from 'src/entities/user-asset.entity';
import { User<PERSON>hain } from 'src/entities/user-chain.entity';
import { UserQuest } from 'src/entities/user-quest.entity';
import { User } from 'src/entities/user.entity';
import { RedeemItem } from 'src/entities/redeem-item.entity';
import { Web3Module } from 'src/web3/web3.module';
import { CommonService } from './common.service';

@Module({
  imports: [
    Web3Module,
    TypeOrmModule.forFeature([
      Config,
      Counter,
      Chain,
      User,
      Article,
      UserArticleAnswer,
      Quest,
      UserQuest,
      UserChain,
      HotWallet,
      Sponsor,
      SponsorAsset,
      Transaction,
      UserAsset,
      Asset,
      Brand,
      AdminTransaction,
      RedeemItem,
    ]),
  ],
  providers: [CommonService],
  exports: [CommonService],
})
export class CommonModule {}
