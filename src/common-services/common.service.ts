import { Cache, CachingConfig } from 'cache-manager';
import { Inject, Injectable, Logger } from '@nestjs/common';
import {
  CacheSetting,
  ContractEvent,
  ErrorCode,
  SqlCacheKey,
} from 'src/common/constants';
import { Utils } from 'src/common/utils';
import { QueueService } from 'src/queue/queue.service';
import { ApiError } from 'src/common/api';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { InjectRepository } from '@nestjs/typeorm';
import { Config } from 'src/entities/config.entity';
import {
  Brackets,
  DataSource,
  EntityManager,
  IsNull,
  LessThan,
  Not,
  Repository,
  UpdateResult,
} from 'typeorm';
import { Counter } from 'src/entities/counter.entity';
import { User } from 'src/entities/user.entity';
import { Chain, ChainName, ChainType } from 'src/entities/chain.entity';
import { Redis } from 'ioredis';
import { Article } from 'src/entities/article.entity';
import {
  UserArticleAnswer,
  UserArticleAnswerStatus,
} from 'src/entities/user-article-answer.entity';
import {
  Transaction,
  TransactionStatus,
  TransactionSubType,
  TransactionType,
} from 'src/entities/transaction.entity';
import { UserChain } from 'src/entities/user-chain.entity';
import { HotWallet } from 'src/entities/hot-wallet.entity';
import {
  TransactionEvent,
  Web3Account,
  Web3Event,
  Web3Service,
  Web3Transaction,
} from 'src/web3/web3.interface';
import { SponsorAsset } from 'src/entities/sponsor-asset.entity';
import { UserQuest, UserQuestStatus } from 'src/entities/user-quest.entity';
import { UserAsset, UserAssetStatus } from 'src/entities/user-asset.entity';
import { Asset, AssetType } from 'src/entities/asset.entity';
import { Sponsor } from 'src/entities/sponsor.entity';
import Redlock from 'redlock';
import { Quest } from 'src/entities/quest.entity';
import {
  AdminTransaction,
  AdminTransactionStatus,
  AdminTransactionType,
} from 'src/entities/admin-transaction.entity';
import { Brand, BrandStatus } from 'src/entities/brand.entity';
import {
  PaidType,
  RedeemItemType,
  RedeemItem,
} from 'src/entities/redeem-item.entity';
import { Web3ServiceFactory } from 'src/web3/web3.factory.service';
import { customAlphabet } from 'nanoid';

@Injectable()
export class CommonService {
  private readonly logger = new Logger(CommonService.name);
  public readonly redisClient: Redis;
  public readonly redlock: Redlock;

  constructor(
    private web3ServiceFactory: Web3ServiceFactory,
    private dataSource: DataSource,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    @InjectRepository(Config)
    private configRepository: Repository<Config>,
    @InjectRepository(Counter)
    private counterRepository: Repository<Counter>,
    @InjectRepository(Chain)
    private chainRepository: Repository<Chain>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(UserChain)
    private userChainRepository: Repository<UserChain>,
    @InjectRepository(Article)
    private articleRepository: Repository<Article>,
    @InjectRepository(Quest)
    private questRepository: Repository<Quest>,
    @InjectRepository(UserQuest)
    private userQuestRepository: Repository<UserQuest>,
    @InjectRepository(UserArticleAnswer)
    private userArticleAnswerRepository: Repository<UserArticleAnswer>,
    @InjectRepository(HotWallet)
    private hotWalletRepository: Repository<HotWallet>,
    @InjectRepository(Sponsor)
    private sponsorRepository: Repository<Sponsor>,
    @InjectRepository(SponsorAsset)
    private sponsorAssetRepository: Repository<SponsorAsset>,
    @InjectRepository(Brand)
    private brandRepository: Repository<Brand>,
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
    @InjectRepository(UserAsset)
    private userAssetRepository: Repository<UserAsset>,
    @InjectRepository(Asset)
    private assetRepository: Repository<Asset>,
    @InjectRepository(RedeemItem)
    private redeemItemRepository: Repository<RedeemItem>,
  ) {
    this.redisClient = new Redis({
      host: process.env.REDIS_HOST,
      port: Number(process.env.REDIS_PORT),
      keyPrefix: process.env.REDIS_PREFIX,
      enableOfflineQueue: false,
    });
    this.redlock = new Redlock([this.redisClient], {
      retryCount: 15,
      retryDelay: 500,
      retryJitter: 100,
      automaticExtensionThreshold: 0,
    });
  }

  logError(error: Error) {
    this.logger.error(error.message, error.stack);
  }

  async withLock<T>(lockKeys: string[], fn: () => Promise<T>) {
    let lock: Awaited<ReturnType<typeof this.redlock.acquire>>;

    try {
      lock = await this.redlock.acquire(lockKeys, 5000); // thời gian giữ lock 5s
    } catch (error) {
      this.logger.error(
        `Failed to acquire lock for keys: ${lockKeys.join(', ')}`,
        error,
      );
      throw error;
    }

    try {
      return await fn();
    } finally {
      try {
        await lock.release();
      } catch (error) {
        this.logger.warn(
          `Failed to release lock for keys: ${lockKeys.join(', ')}`,
          // error,
        );
      }
    }
  }

  async clearSqlCache(
    ...keysWithParams: [
      key: keyof typeof SqlCacheKey,
      ...params: (string | number)[],
    ][]
  ) {
    const cacheKeys = keysWithParams.map(([key, ...params]) =>
      Utils.getSqlKey(key, ...params),
    );

    await this.dataSource.queryResultCache?.remove(cacheKeys);
  }

  async clearCacheByPrefix(prefix: string) {
    const pattern = `${prefix}:*`;
    const keys = await this.redisClient.keys(pattern);
    if (keys.length > 0) {
      await this.redisClient.del(...keys);
    }
  }

  getCacheKey(key: string) {
    return process.env.REDIS_PREFIX + ':' + key;
  }

  async setCache(key: string, data: any, options?: CachingConfig) {
    try {
      await this.cacheManager.set(this.getCacheKey(key), data, options);
    } catch (error) {
      this.logError(error);
    }
  }

  async setLockCache(key: string, ttl?: number) {
    try {
      // set key if not already exists
      const result = await this.redisClient.set(
        this.getCacheKey(key),
        'locked',
        'NX',
      );
      await this.redisClient.expire(this.getCacheKey(key), ttl || 60);

      return result === 'OK';
    } catch (err) {
      return false;
    }
  }

  getCache(key: string) {
    return this.cacheManager.get(this.getCacheKey(key)) as any;
  }

  async removeCache(key: string) {
    await this.cacheManager.del(this.getCacheKey(key));
  }

  async getConfig() {
    const config = await this.configRepository.findOne({
      cache: {
        id: Utils.getSqlKey('CONFIG_GET'),
        milliseconds: CacheSetting.SQL_TTL,
      },
    });
    return config;
  }

  async getFullConfig() {
    const config = await this.configRepository.findOne({
      cache: {
        id: Utils.getSqlKey('CONFIG_GET_FULL'),
        milliseconds: CacheSetting.SQL_TTL,
      },
    });
    return config;
  }

  async getNextIndex(name: string, step = 1) {
    let counter = await this.counterRepository.findOneBy({ name });
    if (!counter) {
      counter = await this.counterRepository.save({
        name,
        index: 0,
      });
    }

    await this.counterRepository.increment(
      {
        name,
      },
      'index',
      step,
    );
    return counter.index + step;
  }

  async getListIndex(name: string, step = 1) {
    let counter = await this.counterRepository.findOneBy({ name });
    if (!counter) {
      counter = await this.counterRepository.save({
        name,
        index: 0,
      });
    }
    await this.counterRepository.increment(
      {
        name,
      },
      'index',
      step,
    );
    const currentIndex = counter.index;
    const list = [];
    for (let index = currentIndex + 1; index <= currentIndex + step; index++) {
      list.push(index.toString());
    }
    return list;
  }

  async getChain(chainName: ChainName) {
    const chain = await this.chainRepository.findOne({
      relations: {
        hotwallets: true,
      },
      where: { chainName },
      cache: {
        id: Utils.getSqlKey('CHAIN_GET', chainName),
        milliseconds: CacheSetting.SQL_TTL,
      },
    });
    if (!chain) {
      throw ApiError(ErrorCode.NO_DATA_EXISTS, `Not found chain ${chainName}`);
    }
    return chain;
  }

  async getChainByChainType(chainType: ChainType) {
    const chain = await this.chainRepository.findOne({
      where: { chainType },
      cache: {
        id: Utils.getSqlKey('CHAIN_GET_BY_CHAIN_TYPE', chainType),
        milliseconds: CacheSetting.SQL_TTL,
      },
    });
    if (chain) {
      if (chain.privateKey) {
        chain.privateKey = Utils.decrypt(chain.privateKey);
      }
    } else {
      throw ApiError(
        ErrorCode.INVALID_DATA,
        `Not found chain type ${chainType}`,
      );
    }
    return chain;
  }

  async getChainById(chainId: number) {
    const chain = await this.chainRepository.findOne({
      relations: {
        hotwallets: true,
      },
      where: { id: chainId },
      cache: {
        id: Utils.getSqlKey('CHAIN_GET_BY_ID', chainId),
        milliseconds: CacheSetting.SQL_TTL,
      },
    });
    if (!chain) {
      throw ApiError(
        ErrorCode.NO_DATA_EXISTS,
        `Not found chain id ${chainId.toString()}`,
      );
    }
    return chain;
  }

  async getChainByAssetId(assetId: number) {
    const chain = await this.chainRepository
      .createQueryBuilder('c')
      .innerJoin('assets', 'a')
      .leftJoinAndSelect('c.hotwallets', 'hw')
      .where('a.id = :assetId', { assetId: assetId })
      .cache(
        Utils.getSqlKey('CHAIN_GET_BY_ASSET_ID', assetId),
        CacheSetting.SQL_TTL,
      )
      .getOne();
    if (!chain) {
      throw ApiError(
        ErrorCode.NO_DATA_EXISTS,
        `Not found chain for asset ${assetId.toString()}`,
      );
    }
    return chain;
  }

  async getChainBySponsorAssetId(sponsorAssetId: number) {
    const chain = await this.chainRepository
      .createQueryBuilder('c')
      .innerJoin(Asset, 'a', 'c.id = a.chainId') // Inner join with assets
      .innerJoin(SponsorAsset, 'sa', 'sa.id = :sponsorId', {
        sponsorId: sponsorAssetId,
      }) // Inner join with sponsor-assets
      .leftJoinAndSelect('c.hotwallets', 'hw')
      .select('c') // Select only the columns from the 'chains' table
      .cache(
        Utils.getSqlKey('CHAIN_GET_BY_SPONSOR_ASSET_ID', sponsorAssetId),
        CacheSetting.SQL_TTL,
      )
      .getOne();
    if (!chain) {
      throw ApiError(
        ErrorCode.NO_DATA_EXISTS,
        `Not found chain for sponsor ${sponsorAssetId.toString()}`,
      );
    }
    return chain;
  }

  async getNex3Chains() {
    const chains = await this.chainRepository.find({
      // where: { blockchainId: process.env.CHAIN_ID },
      relations: {
        hotwallets: true,
      },
      cache: {
        id: Utils.getSqlKey('NEX3_CHAIN_GET'),
        milliseconds: CacheSetting.SQL_TTL,
      },
    });
    return chains;
  }

  async getNex3Token() {
    const token = await this.assetRepository.findOne({
      where: {
        contractAddress: process.env.TOKEN_ADDRESS,
      },
      cache: {
        id: Utils.getSqlKey('NEX3_TOKEN_GET'),
        milliseconds: CacheSetting.SQL_TTL,
      },
    });
    return token;
  }

  async getSponsorAssetById(sponsorAssetId: number) {
    const sponsor = await this.sponsorAssetRepository.findOne({
      where: { id: sponsorAssetId },
      relations: {
        asset: true,
        sponsor: true,
      },
      cache: {
        id: Utils.getSqlKey('SPONSOR_ASSET_GET_BY_ID', sponsorAssetId),
        milliseconds: CacheSetting.SQL_TTL,
      },
    });
    return sponsor;
  }

  async getBrandById(brandId: number) {
    const brand = await this.brandRepository.findOne({
      where: { id: brandId },
      cache: {
        id: Utils.getSqlKey('BRAND_GET_BY_ID', brandId),
        milliseconds: CacheSetting.SQL_TTL,
      },
    });
    return brand;
  }

  async getWeb3(blockchainId: string) {
    const chain = await this.chainRepository.findOne({
      where: { blockchainId: blockchainId },
    });

    if (!chain) {
      throw ApiError(ErrorCode.INVALID_DATA, `Not found chain`);
    }

    if (chain.privateKey) {
      chain.privateKey = Utils.decrypt(chain.privateKey);
    }
    return this.web3ServiceFactory.get(chain);
  }

  async getWeb3ByChainType(chainType: ChainType) {
    const chain = await this.getChainByChainType(chainType);
    return this.web3ServiceFactory.get(chain);
  }

  async getWeb3ByChain(chain: Chain) {
    return this.web3ServiceFactory.get(chain);
  }

  async getWeb3ByChainId(chainId: number) {
    const chain = await this.getChainById(chainId);
    return this.web3ServiceFactory.get(chain);
  }

  async getWeb3ForNex3() {
    const chain = await this.getChainByChainType(ChainType.EVM);
    return this.web3ServiceFactory.get(chain);
  }

  async getHotWalletByAddress(data: {
    chainId: number;
    walletAddress: string;
  }) {
    const hotWallet = await this.hotWalletRepository.findOne({
      where: {
        walletAddress: data.walletAddress,
        chainId: data.chainId,
      },
      cache: {
        id: Utils.getSqlKey(
          'HOT_WALLET_GET_BY_ADDRESS',
          data.chainId,
          data.walletAddress,
        ),
        milliseconds: CacheSetting.SQL_TTL,
      },
    });
    return hotWallet;
  }

  async getUserWallet(data: { userId: number; chainId: number }) {
    const userChain = await this.userChainRepository.findOne({
      where: { userId: data.userId, chainId: data.chainId },
      cache: {
        id: Utils.getSqlKey('USER_WALLET_GET', data.userId, data.chainId),
        milliseconds: CacheSetting.SQL_TTL,
      },
    });
    if (!userChain) {
      throw ApiError(
        ErrorCode.NO_DATA_EXISTS,
        `Not found wallet of user ${JSON.stringify(data)}`,
      );
    }
    return userChain;
  }

  async getNex3UserAsset(userId: number) {
    const nex3Token = await this.getNex3Token();
    const userNex3 = await this.userAssetRepository.findOneBy({
      userId: userId,
      assetId: nex3Token.id,
    });
    return userNex3;
  }

  async getUserAssetTransferNft(userId: number, assetId: number, id?: number) {
    const userAsset = await this.userAssetRepository
      .createQueryBuilder('userAsset')
      .leftJoinAndSelect('userAsset.user', 'user')
      .leftJoinAndSelect('userAsset.asset', 'asset')
      .leftJoinAndSelect('asset.chain', 'chain')
      .where('userAsset.userId = :userId', { userId: userId })
      .andWhere('userAsset.assetId = :assetId', { assetId: assetId })
      .andWhere(
        new Brackets((qb) => {
          if (id) {
            qb.where('userAsset.id = :id', { id: id });
          }
        }),
      )
      .getOne();
    return userAsset;
  }

  async getRedeemItem(redeemItemId: number) {
    const redeemItem = await this.redeemItemRepository.findOne({
      where: { id: redeemItemId },
      relations: {
        brand: true,
        chain: true,
        sponsor: true,
        sponsorAsset: true,
        asset: true,
        paidAsset: true,
      },
      // cache: {
      //   id: Utils.getSqlKey('REDEEM_ITEM_GET', redeemItemId),
      //   milliseconds: CacheSetting.SQL_TTL,
      // },
    });
    return redeemItem;
  }

  async getUserAssetByWalletAddress(walletAddress: string, assetId: number) {
    const userAsset = await this.userAssetRepository
      .createQueryBuilder('userAsset')
      .innerJoinAndSelect('userAsset.asset', 'asset')
      .innerJoinAndSelect('asset.chain', 'chain')
      .where('userAsset.walletAddress = :walletAddress', {
        walletAddress: walletAddress,
      })
      .andWhere('userAsset.assetId = :assetId', { assetId: assetId })
      .getOne();
    return userAsset;
  }

  async getAssetById(assetId: number) {
    const asset = await this.assetRepository.findOne({
      where: { id: assetId },
      cache: {
        id: Utils.getSqlKey('ASSET_GET_BY_ID', assetId),
        milliseconds: CacheSetting.SQL_TTL,
      },
    });
    if (!asset) {
      throw ApiError(ErrorCode.NO_DATA_EXISTS, `Not found asset ${assetId}`);
    }
    return asset;
  }

  async getTransactionWithLock(manager: EntityManager, transactionId: number) {
    const transaction = await manager
      .getRepository(Transaction)
      .createQueryBuilder()
      .where('id = :id', { id: transactionId })
      .setLock('pessimistic_write')
      .getOne();
    return transaction;
  }

  async getAdminTransactionWithLock(
    manager: EntityManager,
    transactionId: number,
  ) {
    const transaction = await manager
      .getRepository(AdminTransaction)
      .createQueryBuilder()
      .where('id = :id', { id: transactionId })
      .setLock('pessimistic_write')
      .getOne();
    return transaction;
  }

  async createUserChain(data: { userId: number; chain: Chain }) {
    const { userId, chain } = data;
    const userChain = await this.userChainRepository.findOneBy({
      userId: userId,
      chainId: chain.id,
    });
    if (userChain) return;

    let isCreated = false;
    if (chain.chainType === ChainType.EVM) {
      const userChainEvm = await this.userChainRepository
        .createQueryBuilder('uc')
        .innerJoin('uc.chain', 'c')
        .where('c.chainType = :type', { type: 'evm' })
        .andWhere('uc.userId = :userId', { userId: userId })
        .getOne();

      if (userChainEvm) {
        await this.userChainRepository.insert({
          userId: userId,
          chainId: chain.id,
          walletAddress: userChainEvm.walletAddress,
          privateKey: userChainEvm.privateKey,
        });
        isCreated = true;
      }
    }
    if (!isCreated) {
      const web3 = await this.getWeb3(chain.blockchainId);
      const account = web3.createAccount();
      await this.userChainRepository.insert({
        userId: userId,
        chainId: chain.id,
        walletAddress: account.address,
        privateKey: Utils.encrypt(account.privateKey),
      });
    }
  }

  generateCodeRef() {
    const alphabet =
      '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    const nanoid = customAlphabet(alphabet, 8);
    return nanoid();
  }

  async updateTransaction(transactionId: number, assetType: AssetType) {
    this.logger.log(`updateTransaction(): transactionId = ${transactionId}`);

    const transaction = await this.transactionRepository.findOne({
      where: { id: transactionId },
      relations: {
        chain: true,
        hotWallet: true,
      },
    });
    this.logger.log(
      `updateTransaction(): transaction = ${JSON.stringify(transaction)}`,
    );
    if (!transaction) {
      this.logger.warn(`Not found transaction ${transactionId}`);
      return;
    }

    const web3 = await this.getWeb3ByChainId(transaction.chainId);
    const web3Transaction = await web3.getTransactionEvent(
      transaction.txHash,
      assetType,
    );

    this.logger.log(
      `updateTransaction(): web3Transaction = ${JSON.stringify(
        web3Transaction,
      )}`,
    );

    switch (transaction.type) {
      case TransactionType.REDEEM:
        await this.updateTransactionRedeem(transaction, web3Transaction);
        break;
      case TransactionType.TRANSFER:
        await this.updateTransactionSendAsset(transaction, web3Transaction);
        break;
    }
  }

  async updateGasUsed(data: {
    manager: EntityManager;
    transaction: Transaction;
    web3Transaction: TransactionEvent;
    hotWallet: HotWallet;
  }) {
    const { manager, transaction, web3Transaction, hotWallet } = data;
    const web3 = await this.getWeb3ByChainId(transaction.chainId);

    // Update hotwallet balance
    await manager.update(
      HotWallet,
      { id: hotWallet.id },
      {
        totalTransaction: () => `totalTransaction + 1`,
        balance: () => `balance - ${web3Transaction.gasUsed.toString()}`,
        gasUsed: () => `gasUsed + ${web3Transaction.gasUsed.toString()}`,
      },
    );

    if (web3Transaction.status) {
      // Update transaction status to success
      await manager.update(
        Transaction,
        { id: transaction.id },
        {
          hotWalletId: hotWallet.id,
          status: TransactionStatus.SUCCESS,
          gasUsed: web3Transaction.gasUsed.toString(),
        },
      );
    } else {
      // Update transaction status to failed
      await manager.update(
        Transaction,
        { id: transaction.id },
        {
          hotWalletId: hotWallet.id,
          status: TransactionStatus.FAILED,
          gasUsed: web3Transaction.gasUsed.toString(),
        },
      );
    }
  }

  async updateTransactionRedeem(
    transaction: Transaction,
    web3Transaction: TransactionEvent,
  ) {
    await this.dataSource.transaction(async (manager) => {
      const redeemItem = await this.getRedeemItem(transaction.redeemItemId);
      const redeemedAsset = redeemItem.asset;

      const hotWallet = await manager.findOneBy(HotWallet, {
        id: transaction.hotWalletId,
      });
      const lockKeys = [
        `transaction-${transaction.id}`,
        `user-${transaction.fromUserId}`,
        `redeem-item-${redeemItem.id}`,
        `hot-wallet-${hotWallet.id}`,
      ];
      if (redeemItem.type === RedeemItemType.ASSET) {
        lockKeys.push(`user-asset-${transaction.fromUserId}`);
      }

      await this.withLock(lockKeys, async () => {
        // Update transaction gas used
        await this.updateGasUsed({
          manager,
          transaction,
          web3Transaction,
          hotWallet,
        });

        // Transaction success
        if (web3Transaction.status) {
          if (
            redeemedAsset.type === AssetType.NFT ||
            redeemedAsset.type === AssetType.NFT_VOUCHER
          ) {
            const updateData: Partial<UserAsset> = {
              status: UserAssetStatus.REDEEMED,
              userId: transaction.toUserId,
              redeemItemId: redeemItem.id,
              redeemedAt: new Date(),
              nftContractAddress: web3Transaction.nftContractAddress,
            };

            if (web3Transaction.tokenId) {
              updateData.tokenId = web3Transaction.tokenId;
            }
            await manager.update(
              UserAsset,
              { id: transaction.nftId },
              updateData,
            );
          }
        } else {
          // Revert transaction redeem
          await this.rollbackTransactionRedeem({
            manager,
            transaction,
            redeemItem,
            error: new Error('Onchain transaction failed'),
          });
        }
      });
    });
  }

  async rollbackPaidUserAsset(data: {
    manager: EntityManager;
    transaction: Transaction;
    redeemItem: RedeemItem;
  }) {
    const { manager, transaction, redeemItem } = data;
    if (redeemItem.paidType === PaidType.NEX3_XP) {
      this.logger.log(
        `rollbackPaidUserAsset(): Add ${transaction.point} point to user ${transaction.fromUserId}`,
      );
      await manager.update(
        User,
        { id: transaction.fromUserId },
        { point: () => `point + ${transaction.point}` },
      );
    } else {
      const paidUserAsset = await manager.findOneBy(UserAsset, {
        userId: transaction.fromUserId,
        assetId: redeemItem.paidAssetId,
      });
      this.logger.log(
        `rollbackPaidUserAsset(): Add ${transaction.point} of asset ${redeemItem.paidAssetId} to user ${transaction.fromUserId}`,
      );
      await manager.update(
        UserAsset,
        { id: paidUserAsset.id },
        { balance: () => `balance + ${transaction.point}` },
      );
    }
  }

  async rollbackRedeemedUserAsset(data: {
    manager: EntityManager;
    transaction: Transaction;
    redeemItem: RedeemItem;
  }) {
    const { manager, transaction, redeemItem } = data;
    const redeemedAsset = redeemItem.asset;

    if (redeemedAsset.type === AssetType.TOKEN) {
      const redeemedUserAsset = await manager.findOneBy(UserAsset, {
        userId: transaction.toUserId,
        assetId: redeemedAsset.id,
      });
      this.logger.log(
        `rollbackRedeemedUserAsset(): Subtract ${redeemItem.redeemedAmount} of asset ${redeemedAsset.id} from user ${transaction.toUserId}`,
      );
      await manager.update(
        UserAsset,
        { id: redeemedUserAsset.id },
        { balance: () => `balance - ${redeemItem.redeemedAmount}` },
      );
    } else if (redeemedAsset.type === AssetType.NFT) {
      this.logger.log(
        `rollbackRedeemedUserAsset(): Update nft ${transaction.nftId} to unredeemed`,
      );
      await manager.update(
        UserAsset,
        { id: transaction.nftId },
        { status: UserAssetStatus.UNREDEEMED },
      );
    }
  }

  async rollbackTransactionRedeem(data: {
    manager: EntityManager;
    transaction: Transaction;
    redeemItem: RedeemItem;
    error: Error;
  }) {
    const { manager, transaction, redeemItem, error } = data;

    // Update transaction status to failed
    await manager.update(
      Transaction,
      { id: transaction.id },
      {
        status: TransactionStatus.FAILED,
        error: Utils.serializeError(error),
      },
    );

    // Revert paid user asset
    await this.rollbackPaidUserAsset({
      manager,
      transaction,
      redeemItem,
    });

    // Revert redeem item
    await manager.update(
      RedeemItem,
      { id: transaction.redeemItemId },
      { remainReward: () => `remainReward + ${transaction.redeemedAmount}` },
    );

    // Revert sponsor asset
    await manager.update(
      SponsorAsset,
      { id: transaction.sponsorAssetId },
      { remainReward: () => `remainReward + ${transaction.redeemedAmount}` },
    );

    // Revert redeemed user asset
    await this.rollbackRedeemedUserAsset({
      manager,
      transaction,
      redeemItem,
    });
  }

  async updateTransactionSendAsset(
    transaction: Transaction,
    web3Transaction: TransactionEvent,
  ) {
    await this.dataSource.transaction(async (manager) => {
      const senderUserAsset = await manager.findOne(UserAsset, {
        where: { id: transaction.nftId },
        relations: { asset: true },
      });
      const asset = senderUserAsset.asset;
      const hotWallet = await manager.findOneBy(HotWallet, {
        chainId: transaction.chainId,
        walletAddress: transaction.hotWallet.walletAddress,
      });

      const lockKeys = [
        `transaction-${transaction.id}`,
        `user-asset-${transaction.fromUserId}`,
        `user-asset-${transaction.toUserId}`,
        `hot-wallet-${hotWallet.id}`,
      ];
      await this.withLock(lockKeys, async () => {
        // Update transaction gas used
        await this.updateGasUsed({
          manager,
          transaction,
          web3Transaction,
          hotWallet,
        });

        // Transaction success
        if (web3Transaction.status) {
          if (
            asset.type === AssetType.NFT ||
            asset.type === AssetType.NFT_VOUCHER
          ) {
            await manager.update(
              UserAsset,
              { id: senderUserAsset.id },
              {
                userId: transaction.toUserId,
                status: UserAssetStatus.REDEEMED,
              },
            );
          }
        } else {
          // Revert transaction transfer
          await this.rollbackTransactionSendAsset({
            manager,
            transaction,
          });
        }
      });
    });
  }

  async rollbackTransactionSendAsset(data: {
    manager: EntityManager;
    transaction: Transaction;
    error?: Error;
  }) {
    const { manager, transaction, error } = data;
    const userAsset = await manager.findOne(UserAsset, {
      where: { id: transaction.nftId },
      relations: { asset: true },
    });
    const senderUserAsset = await manager.findOneBy(UserAsset, {
      userId: transaction.fromUserId,
      assetId: transaction.assetId,
    });
    const asset = userAsset.asset;
    const receiverUserAsset = await manager.findOneBy(UserAsset, {
      userId: transaction.toUserId,
      assetId: transaction.assetId,
    });

    // Update transaction status to failed
    await manager.update(
      Transaction,
      { id: transaction.id },
      { status: TransactionStatus.FAILED, error: Utils.serializeError(error) },
    );

    // Revert sender user asset status
    if (asset.type === AssetType.TOKEN) {
      // Revert sender user asset balance
      await manager.update(
        UserAsset,
        { id: senderUserAsset.id },
        { balance: () => `balance + ${transaction.point}` },
      );

      // Revert receiver user asset balance
      await manager.update(
        UserAsset,
        { id: receiverUserAsset.id },
        { balance: () => `balance - ${transaction.point}` },
      );
    } else {
      await manager.update(
        UserAsset,
        { id: userAsset.id },
        {
          status: UserAssetStatus.REDEEMED,
          redeemedAt: null,
          userId: transaction.fromUserId,
        },
      );
    }
  }
}
