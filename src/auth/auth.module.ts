import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CommonModule } from 'src/common-services/common.module';
import { Admin } from 'src/entities/admin.entity';
import { UserBrand } from 'src/entities/user-branch.entity';
import { UserChain } from 'src/entities/user-chain.entity';
import { User } from 'src/entities/user.entity';
import { QueueModule } from 'src/queue/queue.module';
import { TwitterModule } from 'src/twitter/twitter.module';
import { AuthAdminController } from './auth.admin.controller';
import { AuthAdminService } from './auth.admin.service';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtAdminStrategy } from './strategy/jwt-admin.strategy';
import { JwtBrandAdminStrategy } from './strategy/jwt-brand-admin.strategy';
import { JwtWebhookStrategy } from './strategy/jwt-webhook.strategy';
import { JwtStrategy } from './strategy/jwt.strategy';

@Module({
  imports: [
    PassportModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET,
      signOptions: { expiresIn: process.env.JWT_EXPIRATION_TIME },
    }),
    CommonModule,
    QueueModule,
    TypeOrmModule.forFeature([User, UserBrand, UserChain, Admin]),
    TwitterModule,
  ],
  controllers: [AuthController, AuthAdminController],
  providers: [
    AuthService,
    AuthAdminService,
    JwtStrategy,
    JwtAdminStrategy,
    JwtBrandAdminStrategy,
    JwtWebhookStrategy,
  ],
})
export class AuthModule {}
