import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { AuthAdminService } from './auth.admin.service';
import { RegisterAdminDto } from './dto/admin/register.admin.dto';
import { LoginAdminDto } from './dto/admin/login.admin.dto';
import { ForgotPasswordAdminDto } from './dto/admin/forgot-password.admin.dto';
import { ResetPasswordAdminDto } from './dto/admin/reset-password.admin.dto';
import { UpdatePasswordAdminDto } from './dto/admin/update-password.admin.dto';
import { ResendOtpAdminDto } from './dto/admin/resend-otp.admin,dto';
import { AdminPayload } from 'src/entities/admin.entity';
import { JwtAdminAuthGuard } from './guard/jwt-admin-auth.guard';
import { Admin } from 'src/common/decorator/admin.decorator';

@Controller('admin/auth')
export class AuthAdminController {
  constructor(private readonly authAdminService: AuthAdminService) {}

  @Post('register')
  async register(@Body() requestData: RegisterAdminDto) {
    const answer = await this.authAdminService.register(requestData);
    return answer;
  }

  @Post('login')
  async login(@Body() requestData: LoginAdminDto) {
    const answer = await this.authAdminService.login(requestData);
    return answer;
  }

  @Post('forgot-password')
  async forgotPassword(@Body() requestData: ForgotPasswordAdminDto) {
    const answer = await this.authAdminService.forgotPassword(requestData);
    return answer;
  }

  @Post('reset-password')
  async resetPassword(@Body() requestData: ResetPasswordAdminDto) {
    const answer = await this.authAdminService.resetPassword(requestData);
    return answer;
  }

  @Post('resend-otp')
  async resendOtp(@Body() requestData: ResendOtpAdminDto) {
    const answer = await this.authAdminService.resendOtp(requestData);
    return answer;
  }

  @UseGuards(JwtAdminAuthGuard)
  @Post('update-password')
  updatePassword(
    @Admin() currentAdmin: AdminPayload,
    @Body() updatePinCode: UpdatePasswordAdminDto,
  ) {
    return this.authAdminService.updatePassword(updatePinCode, currentAdmin);
  }
}
