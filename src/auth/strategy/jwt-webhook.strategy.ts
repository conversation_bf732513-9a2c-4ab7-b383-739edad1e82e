import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';

@Injectable()
export class JwtWebhookStrategy extends PassportStrategy(
  Strategy,
  'jwt-webhook',
) {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: true,
      secretOrKey: process.env.JWT_WEBHOOK_SECRET,
    });
  }

  async validate(payload: any) {
    return payload;
  }
}
