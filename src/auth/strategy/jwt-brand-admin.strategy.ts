import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';

@Injectable()
export class JwtBrandAdminStrategy extends PassportStrategy(
  Strategy,
  'jwt-brand-admin',
) {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: true,
      secretOrKey: process.env.JWT_BRAND_ADMIN_SECRET,
    });
  }

  async validate(payload: any) {
    return payload;
  }
}
