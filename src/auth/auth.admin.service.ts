import {
  BadRequestException,
  HttpStatus,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { CommonService } from 'src/common-services/common.service';
import { Utils } from 'src/common/utils';
import { Repository } from 'typeorm';
import { ResendOtpDto } from './dto/resend-otp.dto';
import { QueueService } from 'src/queue/queue.service';
import { MAIL_DETAIL, OTP_LIFE_SPAN } from 'src/common/constants';
import { ApiError } from 'src/common/api';
import { Admin, AdminPayload } from 'src/entities/admin.entity';
import { RegisterAdminDto } from './dto/admin/register.admin.dto';
import { LoginAdminDto } from './dto/admin/login.admin.dto';
import { ResetPasswordAdminDto } from './dto/admin/reset-password.admin.dto';
import { UpdatePasswordAdminDto } from './dto/admin/update-password.admin.dto';
import { ResendOtpAdminDto } from './dto/admin/resend-otp.admin,dto';

@Injectable()
export class AuthAdminService {
  private readonly logger = new Logger(AuthAdminService.name);

  constructor(
    private jwtService: JwtService,
    private commonService: CommonService,
    private queueService: QueueService,
    @InjectRepository(Admin)
    private adminRepository: Repository<Admin>,
  ) {}

  async register(requestData: RegisterAdminDto) {
    const { email, password } = requestData;

    const existAccount = await this.adminRepository.findOneBy({ email });
    if (existAccount) {
      throw ApiError('', 'Admin email is already in used');
    }

    const hashedPassword = await Utils.hashPincode(password);
    await this.adminRepository.save(
      this.adminRepository.create({ email, password: hashedPassword }),
    );

    return true;
  }

  async login(requestData: LoginAdminDto) {
    const { email, password } = requestData;
    const admin = await this.adminRepository.findOneBy({ email });

    if (!admin) {
      throw ApiError('E4', 'Email is not registered');
    }

    const validPassword = await Utils.comparePincode(password, admin.password);
    if (!validPassword) {
      throw ApiError('E8', `Password is incorrect!`);
    }

    const payload = {
      id: admin.id,
      email: admin.email,
    };

    return {
      accessToken: this.jwtService.sign(payload, {
        secret: process.env.JWT_ADMIN_SECRET,
      }),
    };
  }

  async forgotPassword(requestData: ResendOtpDto) {
    const { email } = requestData;
    const admin = await this.adminRepository.findOneBy({
      email,
    });

    if (!admin) {
      throw ApiError('E4', 'Email is not registered');
    }

    const newOtp = Utils.generateOTP();
    const expirationTime = new Date();
    expirationTime.setSeconds(expirationTime.getSeconds() + OTP_LIFE_SPAN);

    await this.adminRepository.update(admin.id, {
      otp: newOtp,
      otpExpiryTime: expirationTime,
    });

    this.queueService.addMailToQueue({
      to: email,
      subject: MAIL_DETAIL.RESET_PASSWORD.TITLE,
      template: MAIL_DETAIL.RESET_PASSWORD.TEMPLATE,
      context: { otp: newOtp },
    });

    return true;
  }

  async resetPassword(requestData: ResetPasswordAdminDto) {
    const { otp, password } = requestData;
    const admin = await this.adminRepository.findOneBy({ otp });

    if (admin.otpExpiryTime <= new Date()) {
      throw ApiError('E7', 'Verification code is invalid');
    }

    const hashedPassword = await Utils.hashPincode(password);
    await this.adminRepository.update(admin.id, { password: hashedPassword });

    return true;
  }

  async resendOtp(requestData: ResendOtpAdminDto) {
    const { email } = requestData;
    const admin = await this.adminRepository.findOneBy({ email });

    if (!admin) {
      throw ApiError('E4', 'Email is not registered');
    }

    const newOtp = Utils.generateOTP();
    const expirationTime = new Date();
    expirationTime.setSeconds(expirationTime.getSeconds() + OTP_LIFE_SPAN);

    await this.adminRepository.update(admin.id, {
      otp: newOtp,
      otpExpiryTime: expirationTime,
    });

    this.queueService.addMailToQueue({
      to: email,
      subject: MAIL_DETAIL.RESET_PASSWORD.TITLE,
      template: MAIL_DETAIL.RESET_PASSWORD.TEMPLATE,
      context: { otp: newOtp },
    });

    return true;
  }

  async updatePassword(
    updatePinCode: UpdatePasswordAdminDto,
    currentAdmin: AdminPayload,
  ) {
    const { currentPassword, newPassword } = updatePinCode;
    const admin = await this.adminRepository.findOneBy({ id: currentAdmin.id });

    const validPassword = await Utils.comparePincode(
      currentPassword,
      admin.password,
    );
    if (!validPassword) {
      throw ApiError('E8', `Password is incorrect!`);
    }

    const comparedPassword = await Utils.comparePincode(
      newPassword,
      admin.password,
    );
    if (comparedPassword) {
      throw ApiError(
        'E9',
        `New password must be different from current password`,
      );
    }

    const hashedPassword = await Utils.hashPincode(newPassword);
    await this.adminRepository.update(admin.id, { password: hashedPassword });

    return true;
  }
}
