import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { User } from 'src/common/decorator/user.decorator';
import { UserPayload } from 'src/entities/user.entity';
import { AuthService } from './auth.service';
import { AddReferralCodeDto } from './dto/add-referral-code.dto';
import { CheckPincodeDto } from './dto/check-pincode.dto';
import { ForgotPinDto } from './dto/forgot-pin.dto';
import { LinkTwitterDto } from './dto/link-twitter.dto';
import { LoginDto } from './dto/login.dto';
import { PreLoginDto } from './dto/pre-login';
import { ResendOtpDto } from './dto/resend-otp.dto';
import { ResetPinDto } from './dto/reset-pin.dto';
import { UpdatePinCodeDto } from './dto/update-pincode.dto';
import { VerifyDto } from './dto/verify.dto';
import { JwtAuthGuard } from './guard/jwt-auth.guard';
import { GenHashCodeDto } from '../brands/dto/gen-hash-code.dto';
import { Utils } from 'src/common/utils';
import { Request } from 'express';

@ApiTags('Auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('pre-login')
  @ApiOperation({ summary: 'Send OTP to user' })
  async preLogin(@Req() req: Request, @Body() requestData: PreLoginDto) {
    const token = Utils.getTokenFromHeader(req);
    const answer = await this.authService.preLogin(requestData, token);
    return answer;
  }

  @Post('login')
  @ApiOperation({ summary: 'Authenticate user with OTP' })
  async login(@Req() req: Request, @Body() requestData: LoginDto) {
    const token = Utils.getTokenFromHeader(req);
    const answer = await this.authService.login(requestData, token);
    return answer;
  }

  @Post('verify')
  @ApiOperation({ summary: 'Verify OTP for user' })
  async verify(@Body() requestData: VerifyDto) {
    const answer = await this.authService.verify(requestData);
    return answer;
  }

  @Post('resend-otp')
  @ApiOperation({ summary: 'Send OTP again to user' })
  async resendOtp(@Body() requestData: ResendOtpDto) {
    const answer = await this.authService.resendOtp(requestData);
    return answer;
  }

  @Post('forgot-pin')
  @ApiOperation({ summary: 'Initiate PIN reset process' })
  async forgotPin(@Body() requestData: ForgotPinDto) {
    const answer = await this.authService.forgotPin(requestData);
    return answer;
  }

  @Post('reset-pin')
  @ApiOperation({ summary: 'Reset user PIN' })
  async resetPin(@Body() requestData: ResetPinDto) {
    const answer = await this.authService.resetPin(requestData);
    return answer;
  }

  @Post('update-pincode')
  @ApiOperation({ summary: 'Change user PIN code' })
  updatePinCode(@Body() updatePinCode: UpdatePinCodeDto) {
    return this.authService.updatePinCode(updatePinCode);
  }

  @Post('check-pincode')
  @ApiOperation({ summary: 'Validate user PIN code' })
  checkPinCode(@Body() checkPinCode: CheckPincodeDto) {
    return this.authService.checkPinCode(checkPinCode);
  }

  @Post('add-referral-code')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Add referral code for user' })
  addReferralCode(@Body() addReferralCode: AddReferralCodeDto) {
    return this.authService.addReferralCode(addReferralCode);
  }

  @Post('link-twitter')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Link user Twitter account' })
  linkTwitter(
    @Body() linkTwitter: LinkTwitterDto,
    @User() currentUser: UserPayload,
  ) {
    return this.authService.linkTwitter(linkTwitter, currentUser);
  }
}
