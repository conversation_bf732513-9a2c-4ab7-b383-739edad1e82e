import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Length,
} from 'class-validator';

export class LoginDto {
  @IsEmail()
  @IsNotEmpty()
  @ApiProperty({
    required: true,
    example: '<EMAIL>',
    type: String,
  })
  email: string;

  @IsString()
  @IsOptional()
  @Length(6, 6, { message: 'Pin code needs exactly 6 digits' })
  @ApiProperty({
    required: true,
    example: '123456',
    type: String,
    minLength: 6,
    maxLength: 6,
  })
  pinCode: string;
}
