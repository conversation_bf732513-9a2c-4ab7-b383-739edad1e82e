import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString, Length } from 'class-validator';

export class ResetPinDto {
  @IsEmail()
  @IsNotEmpty()
  @ApiProperty({
    required: true,
    type: String,
    example: '<EMAIL>',
  })
  email: string;

  @IsString()
  @IsNotEmpty()
  @Length(6, 6, { message: 'Pin code needs exactly 6 digits' })
  @ApiProperty({
    required: true,
    type: String,
    example: '123456',
  })
  pinCode: string;
}
