import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  Length,
} from 'class-validator';

export class UpdatePinCodeDto {
  @IsEmail()
  @IsNotEmpty()
  @ApiProperty({
    required: true,
    example: '<EMAIL>',
  })
  email: string;

  @IsString()
  @IsOptional()
  @Length(6, 6, { message: 'Pin code needs exactly 6 digits' })
  @ApiProperty({
    required: true,
    example: '123456',
  })
  pinCode: string;
}
