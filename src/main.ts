import { LogLevel } from '@nestjs/common';
import { HttpAdapterHost, NestFactory } from '@nestjs/core';
import { AllExceptionsFilter } from 'src/common/exceptions/all-exceptions.filter';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import { AppModule } from './app.module';
import { WinstonLogger } from './common/loggers/winston-logger.service';
import { ValidationPipe } from './common/pipes/validation.pipe';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as dotenv from 'dotenv';

// Load environment variables from .env file if it exists
dotenv.config();

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    logger: process.env.LOG_LEVEL.split(',') as LogLevel[],
    bufferLogs: true,
  });
  app.useLogger(app.get(WinstonLogger));

  const httpAdapterHost = app.get(HttpAdapterHost);
  app.useGlobalFilters(new AllExceptionsFilter(httpAdapterHost));
  app.useGlobalPipes(new ValidationPipe());
  app.useGlobalInterceptors(new LoggingInterceptor(app.get(WinstonLogger)));

  app.setGlobalPrefix(process.env.API_PREFIX);
  app.enableCors({
    origin:
      process.env.CORS_ORIGIN.split(',').length === 1
        ? process.env.CORS_ORIGIN.split(',').toString()
        : process.env.CORS_ORIGIN.split(','),
  });

  if (process.env.ENV !== 'prod') {
    const options = new DocumentBuilder()
      .setTitle('NEX3 API')
      .setDescription('NEX3 API')
      .setVersion('1.0')
      .addBearerAuth({ in: 'header', type: 'http' })
      .addBasicAuth()
      .build();
    const document = SwaggerModule.createDocument(app, options);

    SwaggerModule.setup('docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
      },
    });
  }

  await app.listen(process.env.PORT);
}
bootstrap();
