import { Module } from '@nestjs/common';
import { ArticleService } from './articles.service';
import { ArticleController } from './articles.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Article } from 'src/entities/article.entity';
import { ArticleQuestion } from 'src/entities/article-question.entity';
import { ArticleQuestionAnswer } from 'src/entities/article-question-answer.entity';
import { UserArticleAnswer } from 'src/entities/user-article-answer.entity';
import { UserAsset } from 'src/entities/user-asset.entity';
import { QueueModule } from 'src/queue/queue.module';
import { Transaction } from 'src/entities/transaction.entity';
import { CommonModule } from 'src/common-services/common.module';
import { Sponsor } from 'src/entities/sponsor.entity';
import { SponsorAsset } from 'src/entities/sponsor-asset.entity';
import { TransactionModule } from 'src/transactions/transaction.module';
import { ArticleAdminController } from './articles.admin.controller';
import { ArticleAdminService } from './articles.admin.service';

@Module({
  controllers: [ArticleController, ArticleAdminController],
  providers: [ArticleService, ArticleAdminService],
  imports: [
    CommonModule,
    QueueModule,
    TransactionModule,
    TypeOrmModule.forFeature([
      Article,
      ArticleQuestion,
      ArticleQuestionAnswer,
      UserArticleAnswer,
      Sponsor,
      SponsorAsset,
      UserAsset,
      Transaction,
    ]),
  ],
})
export class ArticleModule {}
