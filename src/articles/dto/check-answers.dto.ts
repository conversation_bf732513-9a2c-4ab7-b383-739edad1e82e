import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMaxSize,
  IsArray,
  IsNumber,
  ValidateNested,
} from 'class-validator';

export class UserAnswerDto {
  @ApiProperty({ example: 1, description: 'Question ID' })
  @IsNumber()
  questionId: number;

  @ApiProperty({ example: [2], description: 'Answers ID' })
  @IsArray()
  @IsNumber({}, { each: true })
  @ArrayMaxSize(1, { message: 'Only one answer is allowed.' })
  answers: number[];
}

export class CheckAnswersDto {
  @ApiProperty({
    type: [UserAnswerDto],
    example: [
      {
        questionId: 2,
        answers: [4],
      },
    ],
    description: 'List of user answers for each question',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UserAnswerDto)
  answers: UserAnswerDto[];
}
