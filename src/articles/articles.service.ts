import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CommonService } from 'src/common-services/common.service';
import { ApiError } from 'src/common/api';
import { QuestionAnswerMap } from 'src/common/type.common';
import { ArticleQuestionAnswer } from 'src/entities/article-question-answer.entity';
import { ArticleQuestion } from 'src/entities/article-question.entity';
import { Article, ArticleStatus } from 'src/entities/article.entity';
import { Sponsor } from 'src/entities/sponsor.entity';
import {
  TransactionSubType,
  TransactionType,
} from 'src/entities/transaction.entity';
import {
  UserArticleAnswer,
  UserArticleAnswerStatus,
} from 'src/entities/user-article-answer.entity';
import { UserAsset } from 'src/entities/user-asset.entity';
import { UserPayload } from 'src/entities/user.entity';
import { QueueService } from 'src/queue/queue.service';
import { CreateTransactionDto } from 'src/transactions/dto/create-transaction.dto';
import { TransactionService } from 'src/transactions/transaction.service';
import { DataSource, Repository } from 'typeorm';
import { CheckAnswersDto } from './dto/check-answers.dto';
import { Utils } from 'src/common/utils';
import { FindArticlesDto } from './dto/find-articles.dto';

@Injectable()
export class ArticleService {
  private readonly logger = new Logger(ArticleService.name);

  constructor(
    private dataSource: DataSource,
    private queueService: QueueService,
    private commonService: CommonService,
    private transactionService: TransactionService,
    @InjectRepository(Article)
    private articleRepository: Repository<Article>,
    @InjectRepository(ArticleQuestion)
    private articleQuestionRepository: Repository<ArticleQuestion>,
    @InjectRepository(ArticleQuestionAnswer)
    private articleQuestionAnswerRepository: Repository<ArticleQuestionAnswer>,
    @InjectRepository(UserArticleAnswer)
    private userArticleAnswerRepository: Repository<UserArticleAnswer>,
    @InjectRepository(Sponsor)
    private sponsorRepository: Repository<Sponsor>,
    @InjectRepository(UserAsset)
    private userAssetRepository: Repository<UserAsset>,
  ) {}

  async getAll(currentUser: UserPayload, requestData: FindArticlesDto) {
    const articles = this.articleRepository
      .createQueryBuilder('article')
      .leftJoin(
        'user-article-answers',
        'uaa',
        'uaa.articleId = article.id AND uaa.userId = :userId',
        { userId: currentUser.id },
      )
      .select([
        'article.id AS articleId',
        'article.point AS articlePoint',
        'article.title AS articleTitle',
        'article.category AS articleCategory',
        'article.imageUrl AS articleImageUrl',
        'article.readTime AS articleReadTime',
        'article.createdAt AS articleCreatedAt',
        'COALESCE(uaa.status, \'not_attempted\') AS "status"',
      ])
      .orderBy('article.createdAt', 'DESC')
      .where('article.status = :status', { status: ArticleStatus.ACTIVE });

    return Utils.paginate<any>(articles, requestData, true);
  }

  async detail(currentUser: UserPayload, id: number) {
    const article = await this.articleRepository
      .createQueryBuilder('article')
      .leftJoin('article.sponsor', 'sponsor')
      .leftJoin(
        'user-article-answers',
        'uaa',
        'uaa.articleId = article.id AND uaa.userId = :userId',
        { userId: currentUser.id },
      )
      .select([
        'article.id AS articleId',
        'article.title AS articleTitle',
        'article.point AS articlePoint',
        'article.category AS articleCategory',
        'article.imageUrl AS articleImageUrl',
        'article.content AS articleContent',
        'article.readTime AS articleReadTime',
        'sponsor.id AS sponsorId',
        'sponsor.name AS sponsorName',
        'sponsor.imageUrl AS sponsorImageUrl',
        "COALESCE(uaa.status, 'not_attempted') AS articleStatus",
      ])
      .andWhere('article.id = :articleId', { articleId: id })
      .getRawOne();

    if (!article) {
      throw ApiError('', `Article with ID ${id} not found`);
    }
    return article;
  }

  async detailQuestions(currentUser: UserPayload, id: number) {
    const [article, questions] = await Promise.all([
      this.articleRepository
        .createQueryBuilder('article')
        .leftJoin('article.sponsor', 'sponsor')
        .leftJoin(
          'user-article-answers',
          'uaa',
          'uaa.articleId = article.id AND uaa.userId = :userId',
          { userId: currentUser.id },
        )
        .andWhere('article.id = :articleId', { articleId: id })
        .select([
          'article.id AS "articleId"',
          'article.title AS articleTitle',
          'article.status AS articleStatus',
          'article.point AS articlePoint',
          'sponsor.id AS sponsorId',
          'sponsor.name AS sponsorName',
          'sponsor.imageUrl AS sponsorImageUrl',
          "COALESCE(uaa.status, 'not_attempted') AS claimStatus",
        ])
        .getRawOne(),

      this.articleQuestionRepository
        .createQueryBuilder('question')
        .leftJoinAndSelect('question.articleQuestionAnswers', 'answers')
        .select([
          'question.id',
          'question.question',
          'question.type',
          'question.orderIndex',
          'answers.id',
          'answers.answer',
          'answers.orderIndex',
        ])
        .where('question.articleId = :articleId', { articleId: id })
        .orderBy('question.orderIndex', 'ASC')
        .addOrderBy('answers.orderIndex', 'ASC')
        .getMany(),
    ]);

    if (!article) {
      throw ApiError('', `Article ${id} not found`);
    }

    if (!questions.length) {
      throw ApiError('', `No questions found for article ${id}`);
    }

    return { article, questions };
  }

  async checkAnswers(
    currentUser: UserPayload,
    articleId: number,
    requestData: CheckAnswersDto,
  ) {
    const correctMap = await this.getCorrectAnswers(articleId);
    const correctCount = this.calculateCorrectCount(requestData, correctMap);

    const checkArticle = await this.articleRepository.count({
      where: { id: articleId, status: ArticleStatus.ACTIVE },
    });

    if (checkArticle === 0) {
      throw ApiError('', `Article ${articleId} not found`);
    }

    let transaction = null;
    let userArticleAnswer: UserArticleAnswer =
      await this.userArticleAnswerRepository.findOne({
        where: { userId: currentUser.id, articleId: articleId },
      });

    if (
      correctCount !== correctMap.size &&
      (!userArticleAnswer ||
        (userArticleAnswer &&
          userArticleAnswer.status === UserArticleAnswerStatus.UNCLAIMED))
    ) {
      return {
        totalQuestions: correctMap.size,
        correctCount,
        transaction: null,
      };
    } else if (
      (correctCount !== correctMap.size &&
        userArticleAnswer &&
        userArticleAnswer.status === UserArticleAnswerStatus.CLAIMED) ||
      (correctCount === correctMap.size &&
        userArticleAnswer &&
        userArticleAnswer.status === UserArticleAnswerStatus.CLAIMED)
    ) {
      return {
        totalQuestions: correctMap.size,
        correctCount,
        isAlreadyClaimed: true,
      };
    }

    await this.dataSource.transaction(async (manager) => {
      userArticleAnswer = await manager.findOne(UserArticleAnswer, {
        where: { userId: currentUser.id, articleId: articleId },
      });

      if (correctCount === correctMap.size && !userArticleAnswer) {
        userArticleAnswer = new UserArticleAnswer();
        userArticleAnswer.userId = currentUser.id;
        userArticleAnswer.articleId = articleId;
        userArticleAnswer.status = UserArticleAnswerStatus.UNCLAIMED;
        await manager.save(userArticleAnswer);
      }
    });

    transaction = await this.transactionService.createTransaction(currentUser, {
      type: TransactionType.EARN,
      subType: TransactionSubType.LEARN,
      articleId,
    } as CreateTransactionDto);

    return {
      totalQuestions: correctMap.size,
      correctCount,
      transaction: transaction || null,
    };
  }

  private async getCorrectAnswers(
    articleId: number,
  ): Promise<QuestionAnswerMap> {
    const correctAnswers = await this.articleQuestionAnswerRepository
      .createQueryBuilder('answer')
      .innerJoin('answer.articleQuestion', 'question')
      .where('question.articleId = :articleId', { articleId })
      .andWhere('answer.isCorrect = true')
      .select([
        'question.id AS questionId',
        'answer.orderIndex AS correctAnswerIndex',
        'question.type AS questionType',
      ])
      .getRawMany();

    const correctMap = new Map<
      number,
      { type: string; answers: Set<number> }
    >();
    correctAnswers.forEach((a) => {
      if (!correctMap.has(a.questionId)) {
        correctMap.set(a.questionId, {
          type: a.questionType,
          answers: new Set(),
        });
      }
      correctMap.get(a.questionId)?.answers.add(a.correctAnswerIndex);
    });

    return correctMap;
  }

  private calculateCorrectCount(
    requestData: CheckAnswersDto,
    correctMap: QuestionAnswerMap,
  ) {
    let correctCount = 0;
    requestData.answers.forEach(({ questionId, answers }) => {
      const correctData = correctMap.get(questionId);
      if (!correctData) return;

      const userAnswers = new Set(answers.map(Number));
      const correctAnswers = correctData.answers;
      if (
        userAnswers.size === correctAnswers.size &&
        [...userAnswers].every((ans) => correctAnswers.has(ans))
      ) {
        correctCount++;
      }
    });

    return correctCount;
  }
}
