import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ArticleService } from './articles.service';
import { JwtAuthGuard } from 'src/auth/guard/jwt-auth.guard';
import { CheckAnswersDto } from './dto/check-answers.dto';
import { User } from 'src/common/decorator/user.decorator';
import { UserPayload } from 'src/entities/user.entity';
import { ApiBearerAuth, ApiTags, ApiOperation } from '@nestjs/swagger';
import { FindArticlesDto } from './dto/find-articles.dto';

@ApiTags('Articles')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('articles')
export class ArticleController {
  constructor(private readonly articleService: ArticleService) {}

  @Get('')
  @ApiOperation({ summary: 'Get all articles' })
  async getAll(
    @User() currentUser: UserPayload,
    @Query() requestData: FindArticlesDto,
  ) {
    const answer = await this.articleService.getAll(currentUser, requestData);
    return answer;
  }

  @Get('/:id')
  @ApiOperation({ summary: 'Get article detail by ID' })
  async detail(@User() currentUser: UserPayload, @Param('id') id: number) {
    const answer = await this.articleService.detail(currentUser, +id);
    return answer;
  }

  @Get('/:id/questions')
  @ApiOperation({ summary: 'Get questions for an article by ID' })
  async detailQuestion(
    @User() currentUser: UserPayload,
    @Param('id') id: number,
  ) {
    const answer = await this.articleService.detailQuestions(currentUser, +id);
    return answer;
  }

  @Post('/:id/check-answers')
  @ApiOperation({ summary: 'Check answers for an article' })
  async checkAnswers(
    @User() currentUser: UserPayload,
    @Param('id') id: string,
    @Body() requestData: CheckAnswersDto,
  ) {
    const answer = await this.articleService.checkAnswers(
      currentUser,
      Number(id),
      requestData,
    );
    return answer;
  }
}
