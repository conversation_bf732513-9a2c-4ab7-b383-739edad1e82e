import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/guard/jwt-auth.guard';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { ArticleAdminService } from './articles.admin.service';
import { FileInterceptor } from '@nestjs/platform-express';

@ApiTags('Articles')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('admin/articles')
export class ArticleAdminController {
  constructor(private readonly articleAdminService: ArticleAdminService) {}

  @Post('import-articles')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Import articles CSV' })
  @UseInterceptors(FileInterceptor('file'))
  async importArticlesCSV(@UploadedFile() file: Express.Multer.File) {
    return this.articleAdminService.importArticlesCSV(file);
  }

  @Post('import-articles-questions')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Import articles questions CSV' })
  @UseInterceptors(FileInterceptor('file'))
  async importArticlesQuestionsCSV(@UploadedFile() file: Express.Multer.File) {
    return this.articleAdminService.importArticlesQuestionsCSV(file);
  }

  @Post('import-articles-questions-answers')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Import articles questions answers CSV' })
  @UseInterceptors(FileInterceptor('file'))
  async importArticlesQuestionsAnswersCSV(
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.articleAdminService.importArticlesQuestionsAnswersCSV(file);
  }
}
