import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QueueModule } from 'src/queue/queue.module';
import { CommonModule } from 'src/common-services/common.module';
import { User } from 'src/entities/user.entity';
import { User<PERSON>hain } from 'src/entities/user-chain.entity';
import { UserAdminController } from './user.admin.controller';
import { UserAdminService } from './user.admin.service';
import { Transaction } from 'src/entities/transaction.entity';
import { UserService } from './user.service';
import { UserController } from './user.controller';

@Module({
  controllers: [UserAdminController, UserController],
  providers: [UserAdminService, UserService],
  imports: [
    CommonModule,
    QueueModule,
    TypeOrmModule.forFeature([User, UserChain, Transaction]),
  ],
  exports: [UserService],
})
export class UserModule {}
