import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository, Brackets, SelectQueryBuilder } from 'typeorm';
import { ApiError } from 'src/common/api';
import { Utils } from 'src/common/utils';
import { Admin, AdminPayload } from 'src/entities/admin.entity';
import { CommonService } from 'src/common-services/common.service';
import { QueueService } from 'src/queue/queue.service';
import { User, UserStatus } from 'src/entities/user.entity';
import { User<PERSON>hain } from 'src/entities/user-chain.entity';
import { FindUsersAdminDto } from './dto/admin/find-users.admin.dto';
import {
  DetailUserAdminDto,
  DetailUserFilterType,
} from './dto/admin/detail-user.admin.dto';
import {
  Transaction,
  TransactionSubType,
  TransactionType,
} from 'src/entities/transaction.entity';

@Injectable()
export class UserAdminService {
  private readonly logger = new Logger(UserAdminService.name);

  constructor(
    private queueService: QueueService,
    private commonService: CommonService,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
  ) {}

  async getAll(requestData: FindUsersAdminDto) {
    let queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.userChains', 'userChain')
      .leftJoinAndSelect('userChain.chain', 'chain')
      .select([
        'user.id',
        'user.createdAt',
        'user.email',
        'user.status',
        'userChain.walletAddress',
      ])
      .where('chain.blockchainId = :blockchainId', {
        blockchainId: process.env.CHAIN_ID,
      });

    if (requestData.keyword) {
      Utils.applySearch(queryBuilder, requestData.keyword, [
        { name: 'user.email', isAbsolute: false },
        { name: 'userChain.walletAddress', isAbsolute: false },
      ]);
    }

    queryBuilder = Utils.filterByConditions(queryBuilder, requestData);
    return Utils.paginate<User>(queryBuilder, requestData);
  }

  async detail(id: number, requestData: DetailUserAdminDto) {
    const userPromise = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.userChains', 'userChain')
      .leftJoinAndSelect('userChain.chain', 'chain')
      .select([
        'user.id',
        'user.createdAt',
        'user.email',
        'user.status',
        'userChain.walletAddress',
        'chain.imageUrl',
      ])
      .where('user.id = :id', { id })
      .getMany();

    const userBalancePromise = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.userAssets', 'userAsset')
      .leftJoinAndSelect('userAsset.asset', 'asset')
      .select(['user.id', 'userAsset.balance'])
      .where('user.id = :id', { id })
      .andWhere('asset.contractAddress = :contractAddress', {
        contractAddress: process.env.TOKEN_ADDRESS,
      })
      .getOne();

    let queryBuilder = this.transactionRepository
      .createQueryBuilder('transaction')
      .leftJoinAndSelect('transaction.brand', 'brand')
      .leftJoinAndSelect('transaction.userFrom', 'userFrom')
      .leftJoinAndSelect('userFrom.userChains', 'userFromChain')
      .leftJoinAndSelect('userFromChain.chain', 'fromChain')
      .leftJoinAndSelect('transaction.userTo', 'userTo')
      .leftJoinAndSelect('userTo.userChains', 'userToChain')
      .leftJoinAndSelect('userToChain.chain', 'toChain')
      .leftJoinAndSelect('transaction.voucherCode', 'voucherCode')
      .leftJoinAndSelect('voucherCode.voucher', 'voucher')
      .leftJoinAndSelect('transaction.article', 'article')
      .leftJoinAndSelect('transaction.quest', 'quest')
      .leftJoinAndSelect('transaction.asset', 'asset')
      .where('(transaction.fromUserId = :id OR transaction.toUserId = :id)', {
        id,
      })
      .andWhere('fromChain.blockchainId = :chainId', {
        chainId: process.env.CHAIN_ID,
      })
      .andWhere('toChain.blockchainId = :chainId', {
        chainId: process.env.CHAIN_ID,
      })
      .select([
        'transaction.id',
        'transaction.createdAt',
        'brand.id',
        'brand.name',
        'transaction.type',
        'transaction.subType',
        'transaction.point',
        'transaction.txHash',
        'transaction.status',
        'userFrom.id',
        'userFromChain.walletAddress',
        'fromChain.imageUrl',
        'userTo.id',
        'userToChain.walletAddress',
        'toChain.imageUrl',
        'voucherCode.id',
        'voucher.id',
        'voucher.name',
        'voucher.imageUrl',
        'article.title',
        'quest.title',
        'asset.name',
        'asset.symbol',
        'asset.image',
      ]);

    if (
      !requestData.filterType ||
      (requestData.filterType &&
        requestData.filterType === DetailUserFilterType.POINTS)
    ) {
      queryBuilder.andWhere(
        `(
          transaction.subType IN (:...pointSubTypes)
          OR (transaction.subType = :voucherSubType AND transaction.type = :redeemType)
        )`,
        {
          pointSubTypes: [
            TransactionSubType.LEARN,
            TransactionSubType.QUEST,
            TransactionSubType.REDEEM_ITEM,
          ],
          voucherSubType: TransactionSubType.ASSET,
          redeemType: TransactionType.REDEEM,
        },
      );
    } else if (
      requestData.filterType &&
      requestData.filterType === DetailUserFilterType.VOUCHERS
    ) {
      queryBuilder.andWhere(
        `transaction.subType = :voucherSubType AND transaction.type IN (:...voucherTypes)`,
        {
          voucherSubType: TransactionSubType.ASSET,
          voucherTypes: [TransactionType.REDEEM, TransactionType.TRANSFER],
        },
      );
    }

    if (requestData.keyword) {
      Utils.applySearch(queryBuilder, requestData.keyword, [
        { name: 'transaction.txHash', isAbsolute: false },
        { name: 'voucher.name', isAbsolute: false },
      ]);
    }

    if (requestData.brand) {
      queryBuilder.andWhere(`brand.id = :brandId`, {
        brandId: requestData.brand,
      });
    }

    queryBuilder = await Utils.filterByConditions(queryBuilder, requestData);
    const [user, userBalance, transactions] = await Promise.all([
      userPromise,
      userBalancePromise,
      Utils.paginate<Transaction>(queryBuilder, requestData),
    ]);

    const processedItems = transactions.items.map((tx) => ({
      ...tx,
      isSent: this.determineIsSent(tx, id, requestData.filterType),
    }));

    return {
      user,
      userBalance,
      transactions: {
        ...transactions,
        items: processedItems,
      },
    };
  }

  private determineIsSent(tx: Transaction, id: number, filterType: string) {
    if (
      [TransactionSubType.LEARN, TransactionSubType.QUEST].includes(tx.subType)
    ) {
      return false;
    }

    if (tx.type === TransactionType.REDEEM) {
      if (filterType === DetailUserFilterType.POINTS) {
        return true;
      } else if (filterType === DetailUserFilterType.VOUCHERS) {
        return false;
      }
    }

    if (tx.type === TransactionType.TRANSFER) {
      if (
        [TransactionSubType.ASSET, TransactionSubType.REDEEM_ITEM].includes(
          tx.subType,
        )
      ) {
        return tx.userFrom.id === id;
      }
    }

    return false;
  }

  async changeUserStatus(id: number) {
    const user = await this.userRepository.findOneBy({ id });
    if (!user) {
      throw ApiError('', 'User does not exist');
    }

    const newStatus =
      user.status === UserStatus.ACTIVE ? UserStatus.BANNED : UserStatus.ACTIVE;

    await this.userRepository.update(id, { status: newStatus });

    return true;
  }
}
