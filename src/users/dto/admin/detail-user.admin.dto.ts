import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsDate, IsEnum, IsOptional, IsString } from 'class-validator';
import { SearchDto } from 'src/common/dto.common';
import { TransactionStatus } from 'src/entities/transaction.entity';
import { UserStatus } from 'src/entities/user.entity';

export enum DetailUserFilterType {
  POINTS = 'points',
  VOUCHERS = 'vouchers',
}

export class DetailUserAdminDto extends SearchDto {
  @IsEnum(DetailUserFilterType)
  @IsOptional()
  @ApiProperty({
    enum: DetailUserFilterType,
    default: DetailUserFilterType.POINTS,
  })
  filterType: DetailUserFilterType = DetailUserFilterType.POINTS;

  @IsDate()
  @IsOptional()
  @Type(() => Date)
  @ApiProperty({ required: false })
  dateFrom: Date;

  @IsDate()
  @IsOptional()
  @Type(() => Date)
  @ApiProperty({ required: false })
  dateTo: Date;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
  })
  brand: string;

  @IsEnum(TransactionStatus)
  @IsOptional()
  @ApiProperty({
    required: false,
  })
  status: TransactionStatus;
}
