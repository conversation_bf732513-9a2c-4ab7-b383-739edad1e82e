import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsDate, IsEnum, IsOptional } from 'class-validator';
import { SearchDto } from 'src/common/dto.common';
import { UserStatus } from 'src/entities/user.entity';

export class FindUsersAdminDto extends SearchDto {
  @IsDate()
  @IsOptional()
  @Type(() => Date)
  @ApiProperty({ required: false })
  dateFrom: Date;

  @IsDate()
  @IsOptional()
  @Type(() => Date)
  @ApiProperty({ required: false })
  dateTo: Date;

  @IsEnum(UserStatus)
  @IsOptional()
  @ApiProperty({
    required: false,
  })
  status: UserStatus;
}
