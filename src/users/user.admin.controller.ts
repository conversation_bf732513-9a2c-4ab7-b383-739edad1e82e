import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { JwtAdminAuthGuard } from 'src/auth/guard/jwt-admin-auth.guard';
import { UserAdminService } from './user.admin.service';
import { FindUsersAdminDto } from './dto/admin/find-users.admin.dto';
import { AdminPayload } from 'src/entities/admin.entity';
import { Admin } from 'src/common/decorator/admin.decorator';
import { DetailUserAdminDto } from './dto/admin/detail-user.admin.dto';

@UseGuards(JwtAdminAuthGuard)
@Controller('admin/users')
export class UserAdminController {
  constructor(private readonly userAdminService: UserAdminService) {}

  @Get('')
  async getAll(@Query() requestData: FindUsersAdminDto) {
    const answer = await this.userAdminService.getAll(requestData);
    return answer;
  }

  @Get('/:id')
  async detail(
    @Param('id') id: number,
    @Query() requestData: DetailUserAdminDto,
  ) {
    const answer = await this.userAdminService.detail(+id, requestData);
    return answer;
  }

  @Put('/:id')
  async changeUserStatus(@Param('id') id: number) {
    const answer = await this.userAdminService.changeUserStatus(+id);
    return answer;
  }
}
