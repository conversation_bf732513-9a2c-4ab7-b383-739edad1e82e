import { Controller, Get, UseGuards } from '@nestjs/common';
import { User } from 'src/common/decorator/user.decorator';
import { UserPayload } from 'src/entities/user.entity';
import { UserService } from './user.service';
import { JwtAuthGuard } from 'src/auth/guard/jwt-auth.guard';

@Controller('users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @UseGuards(JwtAuthGuard)
  @Get('referral-history')
  async referralHistory(@User() currentUser: UserPayload) {
    const result = await this.userService.referralHistory(currentUser);
    return result;
  }
}
