import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { User, UserPayload } from 'src/entities/user.entity';
import { Repository } from 'typeorm';

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async referralHistory(currentUser: UserPayload) {
    const user = await this.userRepository
      .createQueryBuilder('user')
      .leftJoin('user.bonusHistoriesAsReferrer', 'bonusHistories')
      .leftJoin('bonusHistories.referred', 'referred')
      .leftJoin('user.referredByUser', 'referredByUser')
      .select([
        'user.id',
        'user.refCode',
        'referredByUser.id',
        'referredByUser.email',
        'referredByUser.refCode',
        'bonusHistories.id',
        'bonusHistories.point',
        'referred.id',
        'referred.email',
      ])
      .where('user.id = :id', { id: currentUser.id })
      .orderBy('referred.createdAt', 'DESC')
      .getOne();

    return user;
  }
}
