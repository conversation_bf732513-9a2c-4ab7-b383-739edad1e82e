import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CommonService } from 'src/common-services/common.service';
import { ApiError } from 'src/common/api';
import { ErrorCode, MIN_BALANCE } from 'src/common/constants';
import { Utils } from 'src/common/utils';
import { Asset, AssetType, TokenType } from 'src/entities/asset.entity';
import { User, UserPayload } from 'src/entities/user.entity';
import { Repository } from 'typeorm';
import { RedeemItem, RedeemItemStatus } from 'src/entities/redeem-item.entity';
import { UserAsset, UserAssetStatus } from 'src/entities/user-asset.entity';
import { getMyInventoryDto } from './dto/my-inventory.dto';
import { GetMyAssetDto } from './dto/my-asset.dto';
import { truncate } from 'node:fs/promises';

export interface BadgeInfo {
  id: number;
  name: string;
  image: string;
  rank: string;
}

export interface UserProfile {
  userId: number;
  email: string;
  point: string;
  twitterId: string;
  twitterUsername: string;
  walletAddress: string;
  totalBalance: string;
  badges: BadgeInfo[];
}

@Injectable()
export class HomeService {
  private readonly logger = new Logger(HomeService.name);

  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Asset)
    private assetRepository: Repository<Asset>,
    @InjectRepository(RedeemItem)
    private redeemItemRepository: Repository<RedeemItem>,
    @InjectRepository(UserAsset)
    private userAssetRepository: Repository<UserAsset>,
    private readonly commonService: CommonService,
  ) {}

  async profile(currentUser: UserPayload) {
    const userProfile = await this.userRepository
      .createQueryBuilder('user')
      .leftJoin('user.userChains', 'userChain')
      .leftJoin('user.userAssets', 'userAsset')
      .leftJoin('userAsset.asset', 'asset')
      .leftJoin('user.badge1', 'badge1')
      .leftJoin('user.badge2', 'badge2')
      .leftJoin('user.badge3', 'badge3')
      .select([
        'user.id AS userId',
        'user.email AS email',
        'user.point AS point',
        'user.twitterId AS twitterId',
        'user.twitterUsername AS twitterUsername',
        'userChain.walletAddress AS walletAddress',
        'badge1.id AS badge1Id',
        'badge1.name AS badge1Name',
        'badge1.image AS badge1Image',
        'badge1.rank AS badge1Rank',
        'badge2.id AS badge2Id',
        'badge2.name AS badge2Name',
        'badge2.image AS badge2Image',
        'badge2.rank AS badge2Rank',
        'badge3.id AS badge3Id',
        'badge3.name AS badge3Name',
        'badge3.image AS badge3Image',
        'badge3.rank AS badge3Rank',
        `COALESCE(SUM(CASE
            WHEN asset.type = '${AssetType.TOKEN}'
            THEN userAsset.balance * asset.priceUsd ELSE 0 END), 0) AS totalBalance`,
      ])
      .where('user.id = :userId', {
        userId: currentUser.id,
      })
      .groupBy('user.id, user.email, userChain.walletAddress')
      .getRawOne();

    const badges: BadgeInfo[] = [];
    if (userProfile.badge1Id) {
      badges.push({
        id: userProfile.badge1Id,
        name: userProfile.badge1Name,
        image: userProfile.badge1Image,
        rank: userProfile.badge1Rank,
      });
    }
    if (userProfile.badge2Id) {
      badges.push({
        id: userProfile.badge2Id,
        name: userProfile.badge2Name,
        image: userProfile.badge2Image,
        rank: userProfile.badge2Rank,
      });
    }
    if (userProfile.badge3Id) {
      badges.push({
        id: userProfile.badge3Id,
        name: userProfile.badge3Name,
        image: userProfile.badge3Image,
        rank: userProfile.badge3Rank,
      });
    }

    const profile: UserProfile = {
      userId: userProfile.userId,
      email: userProfile.email,
      point: userProfile.point,
      twitterId: userProfile.twitterId,
      twitterUsername: userProfile.twitterUsername,
      walletAddress: userProfile.walletAddress,
      totalBalance: userProfile.totalBalance,
      badges,
    };
    return profile;
  }

  async getMyAsset(currentUser: UserPayload, requestData: GetMyAssetDto) {
    const assets = this.userAssetRepository
      .createQueryBuilder('userAsset')
      .leftJoin('userAsset.asset', 'asset')
      .select([
        'asset.id AS assetId',
        'asset.name AS name',
        'asset.symbol AS symbol',
        'asset.image AS image',
        'asset.type AS type',
        'asset.priceUsd AS priceUsd',
        'userAsset.balance AS balance',
        `userAsset.balance * asset.priceUsd AS totalUsd`,
      ])
      .where('userAsset.userId = :userId', { userId: currentUser.id })
      .andWhere('userAsset.status = :status', {
        status: UserAssetStatus.REDEEMED,
      })
      .andWhere('asset.type = :type', { type: AssetType.TOKEN })
      .andWhere('userAsset.balance >= :minBalance', {
        minBalance: MIN_BALANCE,
      });

    return Utils.paginate<any>(assets, requestData, true);
  }

  async getMyInventory(
    currentUser: UserPayload,
    requestData: getMyInventoryDto,
  ) {
    const inventories = this.userAssetRepository
      .createQueryBuilder('userAsset')
      .leftJoin('userAsset.asset', 'asset')
      .leftJoin('userAsset.redeemItem', 'redeemItem')
      .select([
        'asset.symbol AS symbol',
        'redeemItem.id AS id',
        'redeemItem.name AS name',
        'redeemItem.type AS type',
        'redeemItem.imageUrl AS image',
      ])
      .where('userAsset.userId = :userId', { userId: currentUser.id })
      .andWhere('userAsset.status = :status', {
        status: UserAssetStatus.REDEEMED,
      })
      .andWhere('asset.type = :type', { type: AssetType.NFT })
      .orderBy('userAsset.redeemedAt', 'DESC');

    return Utils.paginate<any>(inventories, requestData, true);
  }

  async getMyDetailInventory(currentUser: UserPayload, id: number) {
    const detailNft = await this.redeemItemRepository
      .createQueryBuilder('redeemItem')
      .leftJoinAndSelect('redeemItem.chain', 'chain')
      .leftJoinAndSelect(
        'redeemItem.userAsset',
        'userAsset',
        'userAsset.redeemItemId = redeemItem.id AND userAsset.userId = :userId',
        { userId: currentUser.id },
      )
      .leftJoinAndSelect('userAsset.asset', 'asset')
      .leftJoinAndSelect('redeemItem.sponsorAsset', 'sponsorAsset')
      .leftJoinAndSelect('sponsorAsset.sponsor', 'sponsor')
      .where('redeemItem.id = :id', { id })
      .andWhere('asset.type = :type', { type: AssetType.NFT })
      .select([
        'redeemItem.id',
        'redeemItem.name',
        'redeemItem.imageUrl',
        'redeemItem.description',
        'asset.id',
        'asset.type',
        'asset.symbol',
        'asset.ntfType',
        'chain.id',
        'chain.chainName',
        'chain.chainType',
        'chain.imageUrl',
        'userAsset.id',
        'userAsset.tokenId',
        'sponsorAsset.id',
        'sponsor.id',
        'sponsor.name',
        'sponsor.imageUrl',
      ])
      .getOne();
    if (!detailNft) {
      throw ApiError(ErrorCode.NO_DATA_EXISTS, `NFT doesn't exist`);
    }
    return detailNft;
  }
}
