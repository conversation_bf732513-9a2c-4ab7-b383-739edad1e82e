import { Modu<PERSON> } from '@nestjs/common';
import { HomeService } from './home.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from 'src/entities/user.entity';
import { Asset } from 'src/entities/asset.entity';
import { CommonModule } from 'src/common-services/common.module';
import { UserAsset } from 'src/entities/user-asset.entity';
import { HomeController } from './home.controller';
import { RedeemItem } from 'src/entities/redeem-item.entity';

@Module({
  controllers: [HomeController],
  providers: [HomeService],
  imports: [
    TypeOrmModule.forFeature([User, Asset, RedeemItem, UserAsset]),
    CommonModule,
  ],
})
export class HomeModule {}
