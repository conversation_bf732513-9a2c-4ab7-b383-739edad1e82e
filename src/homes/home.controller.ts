import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation } from '@nestjs/swagger';
import { HomeService } from './home.service';
import { User } from 'src/common/decorator/user.decorator';
import { UserPayload } from 'src/entities/user.entity';
import { JwtAuthGuard } from 'src/auth/guard/jwt-auth.guard';
import { GetMyAssetDto } from './dto/my-asset.dto';
import { getMyInventoryDto } from './dto/my-inventory.dto';

@ApiBearerAuth()
@ApiTags('Homes')
@UseGuards(JwtAuthGuard)
@Controller('homes')
export class HomeController {
  constructor(private readonly homeService: HomeService) {}

  @ApiOperation({ summary: 'Get profile' })
  @Get('profile')
  async detail(@User() currentUser: UserPayload) {
    const answer = await this.homeService.profile(currentUser);
    return answer;
  }

  @ApiOperation({ summary: `Get user's assets` })
  @Get('my-asset')
  async getMyAsset(
    @User() currentUser: UserPayload,
    @Query() requestData: GetMyAssetDto,
  ) {
    const answer = await this.homeService.getMyAsset(currentUser, requestData);
    return answer;
  }

  @ApiOperation({ summary: `Get user's inventories` })
  @Get('my-inventory')
  async getMyInventory(
    @User() currentUser: UserPayload,
    @Query() requestData: getMyInventoryDto,
  ) {
    const answer = await this.homeService.getMyInventory(
      currentUser,
      requestData,
    );
    return answer;
  }

  @ApiOperation({ summary: `Get detail user's inventory` })
  @Get('my-inventory/:id')
  async getMyDetailInventory(
    @User() currentUser: UserPayload,
    @Param('id') id: number,
  ) {
    const answer = await this.homeService.getMyDetailInventory(
      currentUser,
      +id,
    );
    return answer;
  }
}
