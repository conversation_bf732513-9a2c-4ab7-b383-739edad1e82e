import { Column, Entity, ManyToOne, OneToMany, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { Brand } from './brand.entity';
import TimestampEntity from './timestamp.entity';
import { QuestTask } from './quest-task.entity';
import { UserQuest } from './user-quest.entity';
import { Sponsor } from './sponsor.entity';

export enum QuestLevel {
  EASY = 'easy',
  MEDIUM = 'medium',
  HARD = 'hard',
}

export enum QuestStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export enum QuestStatusByDate {
  ON_GOING = 'on_going',
  COMING_SOON = 'coming_soon',
}

@Entity({ name: 'quests' })
export class Quest extends TimestampEntity {
  @OneToMany(() => QuestTask, (questTask) => questTask.quest)
  questTasks: QuestTask[];

  @OneToMany(() => UserQuest, (userQuest) => userQuest.quest)
  userQuests: UserQuest[];

  @Column()
  sponsorId: number;

  @ManyToOne(() => Sponsor)
  @JoinColumn({ name: 'sponsorId' })
  sponsor: Sponsor;

  @Column({ type: 'decimal', precision: 38, scale: 18, default: 0 })
  point: string;

  @Column({ type: 'decimal', precision: 38, scale: 18, default: 0 })
  claimedPoint: string;

  @Column()
  title: string;

  @Column()
  imageUrl: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column()
  startDate: Date;

  @Column()
  toDate: Date;

  @Column({
    type: 'enum',
    enum: QuestLevel,
    default: QuestLevel.EASY,
  })
  level: QuestLevel;

  @Column({ default: 0 })
  totalParticipants: number;

  @Column({
    type: 'enum',
    enum: QuestStatus,
    default: QuestStatus.INACTIVE,
  })
  status: QuestStatus;

  @Column()
  brandId: number;

  @ManyToOne(() => Brand)
  @JoinColumn({ name: 'brandId' })
  brand: Brand;
}
