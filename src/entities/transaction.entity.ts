import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { Brand } from './brand.entity';
import TimestampEntity from './timestamp.entity';
import { User } from './user.entity';
import { Article } from './article.entity';
import { UserArticleAnswer } from './user-article-answer.entity';
import { Quest } from './quest.entity';
import { UserQuest } from './user-quest.entity';
import { Chain } from './chain.entity';
import { Asset } from './asset.entity';
import { HotWallet } from './hot-wallet.entity';
import { Sponsor } from './sponsor.entity';
import { SponsorAsset } from './sponsor-asset.entity';
import { PaidType, RedeemItem } from './redeem-item.entity';
import { UserAsset } from './user-asset.entity';

export enum TransactionType {
  EARN = 'earn',
  REDEEM = 'redeem',
  TRANSFER = 'transfer',
}

export enum TransactionSubType {
  LEARN = 'learn', // Earn
  QUEST = 'quest', // Earn
  REDEEM_ITEM = 'redeem-item', // Redeem
  ASSET = 'asset', // Transfer
}

export enum TransactionStatus {
  DRAFT = 'draft',
  PROCESSING = 'processing',
  SUCCESS = 'success',
  FAILED = 'failed',
}

export enum TransactionDisplayType {
  POINT_SPENT = 'point_spent',
  VOUCHER_RECEIVED = 'voucher_received',
}

export enum SendOption {
  TO_EMAIL = 'to_email',
  TO_NEX3_WALLET = 'to_nex3_wallet',
}

@Entity({ name: 'transactions' })
export class Transaction extends TimestampEntity {
  @Column()
  brandId: number;

  @ManyToOne(() => Brand)
  @JoinColumn({ name: 'brandId' })
  brand: Brand;

  @Column()
  fromUserId: number;

  @Column({ nullable: true })
  toUserId: number;

  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'fromUserId' })
  userFrom: User;

  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'toUserId' })
  userTo: User;

  @Column({ type: 'enum', enum: TransactionType })
  type: TransactionType;

  @Column({ type: 'enum', enum: TransactionSubType })
  subType: TransactionSubType;

  @Column({ nullable: true })
  articleId: number;

  @ManyToOne(() => Article)
  @JoinColumn({ name: 'articleId' })
  article: Article;

  @Column({ nullable: true })
  userArticleAnswerId: number;

  @ManyToOne(() => UserArticleAnswer)
  @JoinColumn({ name: 'userArticleAnswerId' })
  userArticleAnswer: UserArticleAnswer;

  @Column({ nullable: true })
  questId: number;

  @ManyToOne(() => Quest)
  @JoinColumn({ name: 'questId' })
  quest: Quest;

  @Column({ nullable: true })
  userQuestId: number;

  @ManyToOne(() => UserQuest)
  @JoinColumn({ name: 'userQuestId' })
  userQuest: UserQuest;

  @Column({ nullable: true })
  redeemItemId: number;

  @ManyToOne(() => RedeemItem)
  @JoinColumn({ name: 'redeemItemId' })
  redeemItem: RedeemItem;

  @Column({ nullable: true })
  chainId: number;

  @ManyToOne(() => Chain)
  @JoinColumn({ name: 'chainId' })
  chain: Chain;

  @Column({ nullable: true })
  sponsorId: number;

  @ManyToOne(() => Sponsor)
  @JoinColumn({ name: 'sponsorId' })
  sponsor: Sponsor;

  @Column({ nullable: true })
  sponsorAssetId: number;

  @ManyToOne(() => SponsorAsset)
  @JoinColumn({ name: 'sponsorAssetId' })
  sponsorAsset: SponsorAsset;

  @Column({ nullable: true })
  assetId: number;

  @ManyToOne(() => Asset)
  @JoinColumn({ name: 'assetId' })
  asset: Asset;

  @Column({ type: 'decimal', precision: 38, scale: 18, nullable: true })
  point: string;

  @Column({ type: 'enum', enum: PaidType, nullable: true })
  paidType: PaidType;

  @Column({ nullable: true })
  paidAssetId: number;

  @ManyToOne(() => Asset)
  @JoinColumn({ name: 'paidAssetId' })
  paidAsset: Asset;

  @Column({ type: 'decimal', precision: 38, scale: 18, nullable: true })
  redeemedAmount: string;

  @Column({ nullable: true })
  nftId: number;

  @Column({ nullable: true })
  hotWalletId: number;

  @ManyToOne(() => HotWallet)
  @JoinColumn({ name: 'hotWalletId' })
  hotWallet: HotWallet;

  @Column({ nullable: true })
  txHash: string;

  @Column({ nullable: true })
  gasUsed: string;

  @Column({ type: 'enum', enum: TransactionStatus })
  status: TransactionStatus;

  @Column({ type: 'json', nullable: true })
  error: Record<string, any>;

  @Column({
    nullable: true,
    type: 'enum',
    enum: SendOption,
    default: SendOption.TO_EMAIL,
  })
  sendOption: SendOption;
}
