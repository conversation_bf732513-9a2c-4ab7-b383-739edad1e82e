import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { Brand } from './brand.entity';
import TimestampEntity from './timestamp.entity';
import { User } from './user.entity';
import { Article } from './article.entity';
import { UserArticleAnswer } from './user-article-answer.entity';
import { Quest } from './quest.entity';
import { UserQuest } from './user-quest.entity';
import { Chain } from './chain.entity';
import { Asset } from './asset.entity';
import { HotWallet } from './hot-wallet.entity';
import { Sponsor } from './sponsor.entity';
import { SponsorAsset } from './sponsor-asset.entity';

export enum AdminTransactionType {
  CREATE_COLLECTION = 'create-collection',
}

export enum AdminTransactionStatus {
  DRAFT = 'draft',
  PROCESSING = 'processing',
  SUCCESS = 'success',
  FAIL = 'fail',
}

@Entity({ name: 'admin-transactions' })
export class AdminTransaction extends TimestampEntity {
  @Column({ type: 'enum', enum: AdminTransactionType })
  type: AdminTransactionType;

  @Column({ nullable: true })
  brandId: number;

  @ManyToOne(() => Brand)
  @JoinColumn({ name: 'brandId' })
  brand: Brand;

  @Column({ nullable: true })
  hotWalletId: number;

  @ManyToOne(() => HotWallet)
  @JoinColumn({ name: 'hotWalletId' })
  hotWallet: HotWallet;

  @Column({ nullable: true })
  txHash: string;

  @Column({ nullable: true })
  gasUsed: string;

  @Column({ type: 'enum', enum: AdminTransactionStatus })
  status: AdminTransactionStatus;

  @Column({ type: 'json', nullable: true })
  error: any;
}
