import { <PERSON>umn, <PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, Unique, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { Quest } from './quest.entity';
import TimestampEntity from './timestamp.entity';
import { User } from './user.entity';

export enum UserQuestStatus {
  UNCLAIMED = 'unclaimed',
  CLAIMED = 'claimed',
}

@Entity({ name: 'user-quests' })
@Unique(['userId', 'questId'])
export class UserQuest extends TimestampEntity {
  @Column()
  userId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  questId: number;

  @ManyToOne(() => Quest)
  @JoinColumn({ name: 'questId' })
  quest: Quest;

  @Column({
    type: 'enum',
    enum: UserQuestStatus,
    default: UserQuestStatus.UNCLAIMED,
  })
  status: UserQuestStatus;

  @Column({ nullable: true })
  claimedAt: Date;
}
