import { Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn, ManyToOne, OneToMany } from 'typeorm';
import { Asset } from './asset.entity';
import { Brand } from './brand.entity';
import { Chain } from './chain.entity';
import { SponsorAsset } from './sponsor-asset.entity';
import { Sponsor } from './sponsor.entity';
import TimestampEntity from './timestamp.entity';
import { UserAsset } from './user-asset.entity';

export enum RedeemItemStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  OUT_OF_STOCK = 'out_of_stock',
  EXPIRED = 'expired',
  DELETED = 'deleted',
}

export enum RedeemItemType {
  ASSET = 'asset',
  LOTTERY = 'lottery',
}

export enum PaidType {
  NEX3_XP = 'nex3-xp',
  TOKEN = 'token',
}

export type RedeemCondition = {
  quantity: number;
  limitPerUser: number;
};

@Entity({ name: 'redeem-items' })
export class RedeemItem extends TimestampEntity {
  @Column({ nullable: true })
  sponsorId: number;

  @ManyToOne(() => Sponsor)
  @JoinColumn({ name: 'sponsorId' })
  sponsor: Sponsor;

  @Column({ nullable: true })
  brandId: number;

  @ManyToOne(() => Brand)
  @JoinColumn({ name: 'brandId' })
  brand: Brand;

  @Column({ nullable: true })
  chainId: number;

  @ManyToOne(() => Chain)
  @JoinColumn({ name: 'chainId' })
  chain: Chain;

  @Column({ nullable: true })
  sponsorAssetId: number;

  @ManyToOne(() => SponsorAsset)
  @JoinColumn({ name: 'sponsorAssetId' })
  sponsorAsset: SponsorAsset;

  @Column({ nullable: true })
  assetId: number;

  @ManyToOne(() => Asset)
  @JoinColumn({ name: 'assetId' })
  asset: Asset;

  @Column()
  name: string;

  @Column({ nullable: true })
  imageUrl: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'decimal', precision: 38, scale: 18, default: 0 })
  point: string;

  @Column({ type: 'decimal', precision: 38, scale: 18, default: 1 })
  totalReward: string;

  @Column({ type: 'decimal', precision: 38, scale: 18, default: 10 })
  redeemableQuantity: number;

  @Column({ type: 'decimal', precision: 38, scale: 18, default: 10 })
  remainReward: string;

  @Column({ type: 'timestamp', nullable: true })
  validFrom: Date;

  @Column({ type: 'timestamp', nullable: true })
  validTo: Date;

  @Column({
    type: 'enum',
    enum: RedeemItemStatus,
    default: RedeemItemStatus.INACTIVE,
  })
  status: RedeemItemStatus;

  @Column({ type: 'enum', enum: RedeemItemType })
  type: RedeemItemType;

  @Column({ type: 'decimal', precision: 38, scale: 18, default: 0 })
  redeemedAmount: string;

  @Column({ type: 'enum', enum: PaidType })
  paidType: PaidType;

  @Column({ nullable: true })
  paidAssetId: number;

  @ManyToOne(() => Asset)
  @JoinColumn({ name: 'paidAssetId' })
  paidAsset: Asset;

  @Column({ type: 'json', nullable: true })
  redeemCondition: RedeemCondition;

  @OneToMany(() => UserAsset, (userAsset) => userAsset.redeemItem)
  userAsset: UserAsset[];
}
