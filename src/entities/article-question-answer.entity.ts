import {
  BaseEntity,
  Column,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  JoinColumn,
} from 'typeorm';
import { ArticleQuestion } from './article-question.entity';
import { Brand } from './brand.entity';
import TimestampEntity from './timestamp.entity';

@Entity({ name: 'article-question-answers' })
export class ArticleQuestionAnswer extends TimestampEntity {
  @Column()
  articleQuestionId: number;

  @ManyToOne(() => ArticleQuestion)
  @JoinColumn({ name: 'articleQuestionId' })
  articleQuestion: ArticleQuestion;

  @Column()
  answer: string;

  @Column({ type: 'bool', default: false })
  isCorrect: boolean;

  @Column()
  orderIndex: number;
}
