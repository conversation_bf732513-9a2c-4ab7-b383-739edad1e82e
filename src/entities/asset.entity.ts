import {
  BaseEntity,
  Column,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  JoinColumn,
} from 'typeorm';
import TimestampEntity from './timestamp.entity';
import { Chain } from './chain.entity';
import { UserAsset } from './user-asset.entity';
import { SponsorAsset } from './sponsor-asset.entity';

export enum AssetType {
  TOKEN = 'token',
  NFT = 'nft',
  NFT_VOUCHER = 'nft-voucher',
}

export const AssetTypeLabel = {
  [AssetType.TOKEN]: 'Token',
  [AssetType.NFT]: 'NFT',
  [AssetType.NFT_VOUCHER]: 'NFT Voucher',
};

export enum TokenType {
  STABLE_TOKEN = 'stable-token',
  UNSTABLE_TOKEN = 'unstable-token',
  APTOS_COIN = 'aptos-coin',
  APTOS_FA = 'aptos-fa',
}

export enum NftType {
  ERC_721 = 'ERC-721',
  ERC_1155 = 'ERC-1155',
}

export type PriceSource = {
  coingeckoApiId: string;
  coinmarketcapApiId: string;
  gateIoCurrencyPair: string;
};

@Entity({ name: 'assets' })
export class Asset extends TimestampEntity {
  @Column()
  chainId: number;

  @ManyToOne(() => Chain)
  @JoinColumn({ name: 'chainId' })
  chain: Chain;

  @OneToMany(() => SponsorAsset, (sponsor) => sponsor.asset)
  sponsorAsset: SponsorAsset[];

  @OneToMany(() => UserAsset, (userAsset) => userAsset.asset)
  userAsset: UserAsset[];

  @Column({ type: 'enum', enum: AssetType, default: AssetType.TOKEN })
  type: string;

  @Column({ type: 'enum', enum: TokenType, nullable: true, default: null })
  subType: TokenType | null;

  @Column()
  name: string;

  @Column()
  symbol: string;

  @Column()
  image: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'enum', enum: NftType, nullable: true, default: null })
  ntfType: NftType | null;

  @Column()
  contractAddress: string;

  @Column({ nullable: true })
  decimals: number;

  @Column({ default: false })
  isStableToken: boolean;

  @Column({ default: false })
  isNativeToken: boolean;

  @Column({ default: '0' })
  priceUsd: string;

  @Column({ default: '0' })
  priceVnd: string;

  @Column({ nullable: true, type: 'json' })
  priceSource: PriceSource;

  @Column({ nullable: true })
  moduleName: string;

  @Column({ nullable: true })
  functionName: string;
}
