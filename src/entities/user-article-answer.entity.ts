import {
  BaseEntity,
  Column,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  Unique,
  JoinColumn,
} from 'typeorm';
import { ArticleQuestion } from './article-question.entity';
import { Article } from './article.entity';
import { Brand } from './brand.entity';
import TimestampEntity from './timestamp.entity';
import { User } from './user.entity';

export enum UserArticleAnswerStatus {
  UNCLAIMED = 'unclaimed',
  CLAIMED = 'claimed',
}

@Entity({ name: 'user-article-answers' })
@Unique(['userId', 'articleId'])
export class UserArticleAnswer extends TimestampEntity {
  @Column()
  userId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  articleId: number;

  @ManyToOne(() => Article)
  @JoinColumn({ name: 'articleId' })
  article: Article;

  @Column({ type: 'enum', enum: UserArticleAnswerStatus })
  status: UserArticleAnswerStatus;

  @Column({ nullable: true })
  claimedAt: Date;

  @Column({ nullable: true })
  txHash: string;
}
