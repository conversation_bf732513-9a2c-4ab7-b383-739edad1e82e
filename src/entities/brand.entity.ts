import { Column, Entity, OneToMany } from 'typeorm';
import { Article } from './article.entity';
import { Quest } from './quest.entity';
import TimestampEntity from './timestamp.entity';

export type BrandTheme = {
  primaryColor: string;
  secondaryColor: string;
  fontFamily: string;
};

export enum BrandStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  DISABLED = 'disabled',
}

@Entity({ name: 'brands' })
export class Brand extends TimestampEntity {
  @Column()
  name: string;

  @Column()
  code: string;

  @Column({ nullable: true })
  owner: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column()
  logoUrl: string;

  @Column({ nullable: true })
  website: string;

  @Column({ nullable: true })
  iosAppUrl: string;

  @Column({ nullable: true })
  androidAppUrl: string;

  @Column({ type: 'json' })
  theme: BrandTheme;

  @Column({ nullable: true })
  contractAddress: string;

  @Column({ type: 'enum', enum: BrandStatus, default: BrandStatus.ACTIVE })
  status: BrandStatus;

  @OneToMany(() => Article, (article) => article.brand)
  articleBrands: Article[];

  @OneToMany(() => Quest, (quest) => quest.brand)
  questBrands: Quest[];

  @Column({ type: 'boolean', default: false })
  isDeleted: boolean;
}
