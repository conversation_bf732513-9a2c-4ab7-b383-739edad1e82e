import { QuestTask } from './quest-task.entity';
import { Colum<PERSON>, ManyTo<PERSON>ne, Unique, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { Entity } from 'typeorm';
import { User } from './user.entity';
import TimestampEntity from './timestamp.entity';

@Entity({ name: 'quest-tasks-users' })
@Unique(['userId', 'questTaskId'])
export class QuestTaskUser extends TimestampEntity {
  @Column()
  questTaskId: number;

  @ManyToOne(() => QuestTask)
  @JoinColumn({ name: 'questTaskId' })
  questTask: QuestTask;

  @Column()
  userId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;
}
