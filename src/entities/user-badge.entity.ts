import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { Badge } from './badge.entity';
import TimestampEntity from './timestamp.entity';
import { User } from './user.entity';

export enum UserBadgeStatus {
  UNAVAILABLE = 'unavailable',
  NOT_CLAIM = 'not-claim',
  CLAIMED = 'claimed',
}

@Entity({ name: 'user-badges' })
export class UserBadge extends TimestampEntity {
  @Column()
  badgeId: number;

  @ManyToOne((type) => Badge)
  @JoinColumn({ name: 'badgeId' })
  badge: Badge;

  @Column()
  userId: number;

  @ManyToOne((type) => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({
    type: 'enum',
    enum: UserBadgeStatus,
    default: UserBadgeStatus.NOT_CLAIM,
  })
  status: UserBadgeStatus;
}
