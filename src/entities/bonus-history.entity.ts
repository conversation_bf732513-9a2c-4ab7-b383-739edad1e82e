import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { User } from './user.entity';

@Entity({ name: 'bonus_history' })
export class BonusHistory {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'decimal', precision: 38, scale: 18, default: 0 })
  point: string;

  @Column()
  referrerId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'referrerId' })
  referrer: User;

  @Column()
  referredId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'referredId' })
  referred: User;
}
