import {
  BaseEntity,
  Column,
  Entity,
  Index,
  PrimaryGeneratedColumn,
} from 'typeorm';
import TimestampEntity from './timestamp.entity';

export enum WebhookLogStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  SUCCESS_SKIP = 'success-skip',
  SKIP = 'skip',
}

@Entity({ name: 'webhook-logs' })
@Index(['transactionHash', 'logIndex'], { unique: true })
export class WebhookLog extends TimestampEntity {
  @Column()
  eventName: string;

  @Column()
  contractAddress: string;

  @Column()
  transactionHash: string;

  @Column()
  logIndex: string;

  @Column({ type: 'json', nullable: true })
  dataDecode: string;

  @Column({
    default: WebhookLogStatus.PENDING,
    type: 'enum',
    enum: WebhookLogStatus,
  })
  status: WebhookLogStatus;
}
