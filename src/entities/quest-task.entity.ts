import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>T<PERSON><PERSON><PERSON>, <PERSON>To<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { QuestTaskUser } from './quest-task-user.entity';
import { Quest } from './quest.entity';
import TimestampEntity from './timestamp.entity';

export enum QuestTaskType {
  FOLLOW_TIKTOK = 'follow-tiktok',
  VISIT_WEBSITE = 'visit-website',
  FOLLOW_X = 'follow-x',
  RETWEET_X = 'retweet-x',
  LIKE_X = 'like-x',
  FOLLOW_FB = 'follow-fb',
  LIKE_FB = 'like-fb',
  SUBSCRIBE_YT = 'subscribe-yt',
}

@Entity({ name: 'quest-tasks' })
export class QuestTask extends TimestampEntity {
  @Column()
  questId: number;

  @ManyToOne(() => Quest)
  @JoinColumn({ name: 'questId' })
  quest: Quest;

  @OneToMany(() => QuestTaskUser, (questTaskUser) => questTaskUser.questTask)
  questTaskUsers: QuestTaskUser[];

  @Column({ type: 'tinytext' })
  description: string;

  @Column({
    type: 'enum',
    enum: QuestTaskType,
    default: QuestTaskType.VISIT_WEBSITE,
  })
  type: QuestTaskType;

  @Column({ type: 'json' })
  data: object;

  @Column()
  orderIndex: number;
}
