import { Column, <PERSON>tity, OneToMany, Unique } from 'typeorm';
import TimestampEntity from './timestamp.entity';

@Entity({ name: 'sponsors' })
@Unique(['name', 'sponsorVaultAddress'])
export class Sponsor extends TimestampEntity {
  @Column()
  name: string;

  @Column({ nullable: true })
  imageUrl: string;

  @Column({ nullable: true, type: 'mediumtext' })
  description: string;

  @Column({ nullable: true })
  website: string;

  @Column({ nullable: true })
  email: string;

  @OneToMany(() => Sponsor, (sponsor) => sponsor.sponsorAssets)
  sponsorAssets: Sponsor[];

  // Vault for sponsor
  @Column({ nullable: true })
  sponsorVaultAddress: string;
}
