import { BaseEntity, Column, <PERSON>tity, PrimaryGeneratedColumn } from 'typeorm';
import TimestampEntity from './timestamp.entity';

export class AdminPayload {
  id: number;
  email: string;
}

@Entity({ name: 'admins' })
export class Admin extends TimestampEntity {
  @Column({ unique: true })
  email: string;

  @Column()
  password: string;

  @Column({ nullable: true })
  otp: string;

  @Column({ nullable: true })
  otpExpiryTime: Date;
}
