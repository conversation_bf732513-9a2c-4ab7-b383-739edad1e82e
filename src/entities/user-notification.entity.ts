import { Column, Entity } from 'typeorm';
import TimestampEntity from './timestamp.entity';
import { NotificationStatus, NotificationType } from './notification.entity';

@Entity({ name: 'user-notifications' })
export class UserNotification extends TimestampEntity {
  @Column()
  notificationId: number;

  @Column({ type: 'varchar', nullable: true })
  title: string;

  @Column({ type: 'text', nullable: true })
  content: string;

  @Column({
    type: 'enum',
    enum: NotificationType,
    default: NotificationType.ADMIN,
  })
  type: NotificationType;

  @Column({
    type: 'enum',
    enum: NotificationStatus,
    default: NotificationStatus.PENDING,
  })
  status: number;

  @Column({ type: 'timestamp', nullable: true })
  sendTime: Date;

  @Column({ type: 'boolean', default: false })
  isRead: boolean;
}
