import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';
import TimestampEntity from './timestamp.entity';
import { Chain } from './chain.entity';

@Entity('user-chains')
export class UserChain extends TimestampEntity {
  @Column()
  userId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  chainId: number;

  @ManyToOne(() => Chain)
  @JoinColumn({ name: 'chainId' })
  chain: Chain;

  @Column()
  walletAddress: string;

  @Column()
  privateKey: string;
}
