import {
  BaseEntity,
  Column,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Brand } from './brand.entity';
import TimestampEntity from './timestamp.entity';

@Entity({ name: 'brand-admins' })
export class BrandAdmin extends TimestampEntity {
  @Column()
  brandId: number;

  @ManyToOne(() => Brand)
  brand: Brand;

  @Column()
  email: string;

  @Column()
  password: string;

  @Column({ nullable: true })
  otp: string;

  @Column({ nullable: true })
  otpExpiryTime: Date;

  @Column({ type: 'boolean', default: false })
  isDeleted: boolean;
}
