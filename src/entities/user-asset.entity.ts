import {
  BaseEntity,
  Column,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  JoinColumn,
  Unique,
} from 'typeorm';
import { Asset } from './asset.entity';
import TimestampEntity from './timestamp.entity';
import { User } from './user.entity';
import { SponsorAsset } from './sponsor-asset.entity';
import { Sponsor } from './sponsor.entity';
import { UserChain } from './user-chain.entity';
import { RedeemItem } from './redeem-item.entity';

export enum UserAssetStatus {
  UNREDEEMED = 'unredeemed',
  REDEEMED = 'redeemed',
  PROCESSING = 'processing',
  EXPIRED = 'expired',
  USED = 'used',
}

@Entity({ name: 'user-assets' })
@Unique(['userId', 'assetId', 'tokenId', 'code'])
export class UserAsset extends TimestampEntity {
  @Column({ nullable: true })
  userId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ nullable: true })
  sponsorId: number;

  @ManyToOne(() => Sponsor)
  @JoinColumn({ name: 'sponsorId' })
  sponsor: Sponsor;

  @Column({ nullable: true })
  sponsorAssetId: number;

  @ManyToOne(() => SponsorAsset)
  @JoinColumn({ name: 'sponsorAssetId' })
  sponsorAsset: SponsorAsset;

  @Column({ nullable: true })
  redeemItemId: number;

  @ManyToOne(() => RedeemItem)
  @JoinColumn({ name: 'redeemItemId' })
  redeemItem: RedeemItem;

  @Column()
  assetId: number;

  @ManyToOne(() => Asset)
  @JoinColumn({ name: 'assetId' })
  asset: Asset;

  @Column({ nullable: true })
  nftContractAddress: string;

  @Column({ type: 'decimal', precision: 38, scale: 18 })
  balance: string;

  @Column({ nullable: true })
  tokenId: string;

  @Column({ nullable: true })
  code: string; // for nft voucher

  @Column({ type: 'timestamp', nullable: true })
  validFrom: Date; // for nft voucher

  @Column({ type: 'timestamp', nullable: true })
  validTo: Date; // for nft voucher

  @Column({ type: 'timestamp', nullable: true })
  usedAt: Date; // for nft voucher

  @Column({ type: 'timestamp', nullable: true })
  expiredAt: Date; // for nft voucher

  @Column({ type: 'timestamp', nullable: true })
  redeemedAt: Date;

  @Column({
    type: 'enum',
    enum: UserAssetStatus,
    default: UserAssetStatus.REDEEMED,
  })
  status: UserAssetStatus;
}
