import {
  BaseEntity,
  Column,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  JoinColumn,
} from 'typeorm';
import { Article } from './article.entity';
import { Brand } from './brand.entity';
import TimestampEntity from './timestamp.entity';
import { ArticleQuestionAnswer } from './article-question-answer.entity';

export enum ArticleQuestionType {
  SINGLE_CHOICE = 'single-choice',
  MULTIPLE_CHOICE = 'multiple-choice',
}

@Entity({ name: 'article-questions' })
export class ArticleQuestion extends TimestampEntity {
  @Column()
  articleId: number;

  @ManyToOne(() => Article)
  @JoinColumn({ name: 'articleId' })
  article: Article;

  @Column()
  question: string;

  @Column({
    type: 'enum',
    enum: ArticleQuestionType,
    default: ArticleQuestionType.SINGLE_CHOICE,
  })
  type: ArticleQuestionType;

  @Column()
  orderIndex: number;

  @OneToMany(() => ArticleQuestionAnswer, (answer) => answer.articleQuestion)
  articleQuestionAnswers: ArticleQuestionAnswer[];
}
