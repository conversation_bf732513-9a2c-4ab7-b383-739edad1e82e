import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { Asset } from './asset.entity';
import { Sponsor } from './sponsor.entity';
import TimestampEntity from './timestamp.entity';

@Entity({ name: 'sponsor-assets' })
export class SponsorAsset extends TimestampEntity {
  @Column()
  assetId: number;

  @ManyToOne(() => Asset)
  @JoinColumn({ name: 'assetId' })
  asset: Asset;

  @Column({ type: 'decimal', precision: 38, scale: 18 })
  totalReward: string;

  @Column({ type: 'decimal', precision: 38, scale: 18 })
  remainReward: string;

  @Column({ nullable: false })
  sponsorId: number;

  @ManyToOne(() => Sponsor, (sponsor) => sponsor.sponsorAssets, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'sponsorId' })
  sponsor: Sponsor;
}
