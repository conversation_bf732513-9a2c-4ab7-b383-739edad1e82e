import {
  BaseEntity,
  Column,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  JoinColumn,
} from 'typeorm';
import { Chain } from './chain.entity';
import TimestampEntity from './timestamp.entity';

@Entity({ name: 'hot-wallets' })
export class HotWallet extends TimestampEntity {
  @Column()
  chainId: number;

  @ManyToOne(() => Chain)
  @JoinColumn({ name: 'chainId' })
  chain: Chain;

  @Column({ nullable: true })
  walletAddress: string;

  @Column({ nullable: true })
  privateKey: string;

  @Column({
    type: 'decimal',
    precision: 38,
    scale: 18,
    default: '0',
  })
  balance: string;

  @Column({ default: 0 })
  totalTransaction: number;

  @Column({
    type: 'decimal',
    precision: 38,
    scale: 18,
    default: '0',
  })
  gasUsed: string;
}
