import { BaseEntity, Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import TimestampEntity from './timestamp.entity';

export enum CounterName {
  TRANSACTION_ID = 'transaction-id',
}

@Entity({ name: 'counters' })
export class Counter extends TimestampEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  name: string;

  @Column({ default: 1 })
  index: number;
}
