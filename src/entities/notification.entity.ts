import { Column, Entity, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import TimestampEntity from './timestamp.entity';
import { User } from './user.entity';

export enum NotificationType {
  SYSTEM = 'system',
  ADMIN = 'admin',
}

export enum NotificationStatus {
  SUCCESS = 'success',
  PENDING = 'pending',
  FAILED = 'failed',
}

@Entity({ name: 'notifications' })
export class Notification extends TimestampEntity {
  @Column({ type: 'varchar', nullable: true })
  title: string;

  @Column({ type: 'text', nullable: true })
  content: string;

  @Column({
    type: 'enum',
    enum: NotificationType,
    default: NotificationType.ADMIN,
  })
  type: NotificationType;

  @Column({
    type: 'enum',
    enum: NotificationStatus,
    default: NotificationStatus.PENDING,
  })
  status: number;

  @Column({ type: 'timestamp', nullable: true })
  sendTime: Date;

  @Column({ type: 'boolean', default: false })
  isSendAll: boolean;

  @Column({ type: 'boolean', default: false })
  now: boolean;

  @Column()
  userId: number;

  @ManyToOne((type) => User)
  @JoinColumn({ name: 'userId' })
  user: User;
}
