import {
  IsBoolean,
  IsEnum,
  IsJSON,
  IsNotEmpty,
  IsNumber,
  isNumber,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';
import { AssetType, PriceSource } from 'src/entities/asset.entity';

export class CreateAssetDto {
  @IsNumber()
  @IsNotEmpty()
  chainId: number;

  @IsEnum(AssetType)
  @IsNotEmpty()
  type: AssetType;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  symbol: string;

  @IsString()
  @IsNotEmpty()
  image: string;

  @IsString()
  @IsOptional()
  description: string;

  @IsString()
  @IsNotEmpty()
  contractAddress: string;

  @IsNumber()
  @IsOptional()
  decimals: number;

  @IsBoolean()
  @IsOptional()
  isStableToken: boolean;

  @IsBoolean()
  @IsOptional()
  isNativeToken: boolean;

  @IsObject()
  @IsOptional()
  priceSource: PriceSource;
}
