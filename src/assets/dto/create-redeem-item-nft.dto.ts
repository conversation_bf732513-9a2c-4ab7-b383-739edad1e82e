// src/assets/dto/create-asset.dto.ts

import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';
import {
  PaidType,
  RedeemCondition,
  RedeemItemStatus,
} from 'src/entities/redeem-item.entity';

export class CreateRedeemItemNftDto {
  @IsNotEmpty()
  @IsNumber()
  sponsorId: number;

  @IsNotEmpty()
  @IsNumber()
  brandId: number;

  @IsNotEmpty()
  @IsNumber()
  chainId: number;

  @IsNotEmpty()
  @IsNumber()
  sponsorAssetId: number;

  @IsNotEmpty()
  @IsNumber()
  assetId: number;

  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsString()
  imageUrl: string;

  @IsOptional()
  @IsString()
  description?: string; // Can be null

  @IsNotEmpty()
  @IsString()
  point: string;

  @IsNotEmpty()
  @IsString()
  totalReward: string;

  @IsNotEmpty()
  @IsString()
  redeemedAmount: string;

  @IsNotEmpty()
  @IsEnum(RedeemItemStatus)
  status: RedeemItemStatus;

  @IsNotEmpty()
  @IsEnum(PaidType)
  paidType: PaidType;

  @IsOptional()
  @IsNumber()
  paidAssetId?: number | null; // Can be null

  @IsDate()
  @IsOptional()
  @Type(() => Date)
  @ApiProperty({ required: false })
  validFrom?: Date | null;

  @IsDate()
  @IsOptional()
  @Type(() => Date)
  @ApiProperty({ required: false })
  validTo?: Date | null;

  @IsObject()
  @IsOptional()
  redeemCondition?: RedeemCondition;
}
