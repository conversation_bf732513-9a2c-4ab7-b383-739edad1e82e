import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
} from '@nestjs/swagger';
import { JwtAdminAuthGuard } from 'src/auth/guard/jwt-admin-auth.guard';
import { AssetAdminService } from './asset.admin.service';
import { CreateAssetDto } from './dto/create-asset.dto';
import { CreateRedeemItemNftDto } from './dto/create-redeem-item-nft.dto';
import { UpdateAssetDto } from './dto/update-asset.dto';

@Controller('admin/assets')
@UseGuards(JwtAdminAuthGuard)
export class AssetAdminController {
  constructor(private readonly assetService: AssetAdminService) {}

  @Post()
  create(@Body() createCurrencyDto: CreateAssetDto) {
    return this.assetService.create(createCurrencyDto);
  }

  @Get()
  findAll() {
    return this.assetService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.assetService.findOne(+id);
  }

  @Post(':id')
  update(@Param('id') id: string, @Body() updateCurrencyDto: UpdateAssetDto) {
    return this.assetService.update(+id, updateCurrencyDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.assetService.remove(+id);
  }

  @Post('redeem-item/create-redeem-item-nft')
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create redeem item' })
  createRedeemItem(@Body() createRedeemItemDto: CreateRedeemItemNftDto) {
    return this.assetService.createRedeemItem(createRedeemItemDto);
  }

  @Post('import/nft')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Import nft redeem item CSV' })
  @UseInterceptors(FileInterceptor('file'))
  async importNFTCSV(@UploadedFile() file: Express.Multer.File) {
    return this.assetService.importNFTCSV(file);
  }
}
