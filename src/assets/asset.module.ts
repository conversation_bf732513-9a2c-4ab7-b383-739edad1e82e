import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CommonModule } from 'src/common-services/common.module';
import { Asset } from 'src/entities/asset.entity';
import { Brand } from 'src/entities/brand.entity';
import { Chain } from 'src/entities/chain.entity';
import { RedeemItem } from 'src/entities/redeem-item.entity';
import { SponsorAsset } from 'src/entities/sponsor-asset.entity';
import { Sponsor } from 'src/entities/sponsor.entity';
import { UserAsset } from 'src/entities/user-asset.entity';
import { AssetAdminController } from './asset.admin.controller';
import { AssetAdminService } from './asset.admin.service';

@Module({
  imports: [
    CommonModule,
    TypeOrmModule.forFeature([
      Asset,
      RedeemItem,
      UserAsset,
      Sponsor,
      Brand,
      Chain,
      SponsorAsset,
    ]),
  ],
  controllers: [AssetAdminController],
  providers: [AssetAdminService],
  exports: [AssetAdminService],
})
export class AssetModule {}
