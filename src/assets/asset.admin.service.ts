import { Injectable, Logger } from '@nestjs/common';
import { CreateAssetDto } from './dto/create-asset.dto';
import { UpdateAssetDto } from './dto/update-asset.dto';
import { CommonService } from 'src/common-services/common.service';
import { DataSource, DeepPartial, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Asset } from 'src/entities/asset.entity';
import { Utils } from 'src/common/utils';
import { RedeemItem, RedeemItemType } from 'src/entities/redeem-item.entity';
import { UserAsset, UserAssetStatus } from 'src/entities/user-asset.entity';
import { CreateRedeemItemNftDto } from './dto/create-redeem-item-nft.dto';
import { Chain } from 'src/entities/chain.entity';
import { Brand } from 'src/entities/brand.entity';
import { Sponsor } from 'src/entities/sponsor.entity';
import { SponsorAsset } from 'src/entities/sponsor-asset.entity';
import { ApiError } from 'src/common/api';

@Injectable()
export class AssetAdminService {
  private readonly logger = new Logger(AssetAdminService.name);

  constructor(
    private commonService: CommonService,
    private dataSource: DataSource,
    @InjectRepository(Asset)
    private assetRepository: Repository<Asset>,
    @InjectRepository(RedeemItem)
    private redeemItemRepository: Repository<RedeemItem>,
    @InjectRepository(Sponsor)
    private sponsorRepository: Repository<Sponsor>,
    @InjectRepository(Brand)
    private brandRepository: Repository<Brand>,
    @InjectRepository(Chain)
    private chainRepository: Repository<Chain>,
    @InjectRepository(SponsorAsset)
    private sponsorAssetRepository: Repository<SponsorAsset>,
  ) {}

  async create(requestData: CreateAssetDto) {
    return this.assetRepository.save(requestData);
  }

  findAll() {
    return this.assetRepository.find();
  }

  findOne(id: number) {
    return this.assetRepository.findOneBy({ id });
  }

  update(id: number, requestData: UpdateAssetDto) {
    return this.assetRepository.update({ id }, requestData);
  }

  remove(id: number) {
    return this.assetRepository.delete({ id });
  }

  async createRedeemItem(createRedeemItemDto: CreateRedeemItemNftDto) {
    const [sponsor, brand, chain, sponsorAsset, asset] = await Promise.all([
      this.sponsorRepository.count({
        where: { id: createRedeemItemDto.sponsorId },
      }),
      this.brandRepository.count({
        where: { id: createRedeemItemDto.brandId },
      }),
      this.chainRepository.count({
        where: { id: createRedeemItemDto.chainId },
      }),
      this.sponsorAssetRepository.count({
        where: { id: createRedeemItemDto.sponsorAssetId },
      }),
      this.assetRepository.count({
        where: { id: createRedeemItemDto.assetId },
      }),
    ]);

    if (
      sponsor === 0 ||
      brand === 0 ||
      chain === 0 ||
      sponsorAsset === 0 ||
      asset === 0
    ) {
      throw ApiError(
        '',
        'Sponsor, brand, chain, sponsorAsset, or asset not found',
      );
    }

    const {
      sponsorId,
      brandId,
      chainId,
      sponsorAssetId,
      assetId,
      name,
      imageUrl,
      description,
      point,
      totalReward,
      status,
      paidType,
      paidAssetId,
      redeemedAmount,
      validFrom,
      validTo,
      redeemCondition,
    } = createRedeemItemDto;

    const redeemItem: DeepPartial<RedeemItem> = {
      sponsorId,
      brandId,
      chainId,
      sponsorAssetId,
      assetId,
      name,
      imageUrl,
      description,
      point,
      totalReward,
      redeemableQuantity: Utils.toBigNumber(totalReward).toNumber(),
      remainReward: totalReward,
      redeemedAmount,
      type: RedeemItemType.ASSET,
      status,
      paidType,
      paidAssetId,
      validFrom,
      validTo,
      redeemCondition,
    };

    const result = await this.redeemItemRepository.save(redeemItem);

    return result;
  }

  async importNFTCSV(file: Express.Multer.File) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const rows = (await Utils.readCsv(file)) as any[];

      // Step 1: Create UserAssets using the RedeemItem IDs
      const userAssets: Partial<UserAsset>[] = rows.map((row, index) => {
        return {
          // userId: row['userId'],
          sponsorId: row['sponsorId'],
          sponsorAssetId: row['sponsorAssetId'],
          redeemItemId: row['redeemItemId'],
          assetId: row['assetId'],
          balance: row['totalReward'] || '0',
          tokenId: row['tokenId'],
          code: row['code'],
          validFrom: row['validFrom'],
          validTo: row['validTo'],
          status: UserAssetStatus.UNREDEEMED,
        };
      });

      // Insert UserAssets
      await queryRunner.manager.save(UserAsset, userAssets);

      // Commit transaction
      await queryRunner.commitTransaction();
      return true;
    } catch (error) {
      // Rollback transaction on error
      await queryRunner.rollbackTransaction();
      this.logger.error(error);
      throw error;
    } finally {
      // Release query runner
      await queryRunner.release();
    }
  }
}
