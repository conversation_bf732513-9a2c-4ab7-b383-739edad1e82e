import { HttpException, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CommonService } from 'src/common-services/common.service';
import { ApiError } from 'src/common/api';
import { ErrorCode } from 'src/common/constants';
import {
  AdminTransaction,
  AdminTransactionStatus,
  AdminTransactionType,
} from 'src/entities/admin-transaction.entity';
import { AdminPayload } from 'src/entities/admin.entity';
import { Brand } from 'src/entities/brand.entity';
import {
  Transaction,
  TransactionStatus,
  TransactionSubType,
  TransactionType,
} from 'src/entities/transaction.entity';
import { DataSource, Repository } from 'typeorm';
import { CreateTransactionDto } from './dto/create-transaction.dto';
import { CreateAdminTransactionDto } from './dto/create-admin-transaction.dto';
import {
  GetTransactionAdminDto,
  TransactionAdminFilterType,
} from './dto/admin/get-transaction.admin.dto';
import { Utils } from 'src/common/utils';
import { Asset, AssetType } from 'src/entities/asset.entity';

@Injectable()
export class AdminTransactionService {
  private readonly logger = new Logger(AdminTransactionService.name);

  constructor(
    private commonService: CommonService,
    private dataSource: DataSource,
    @InjectRepository(AdminTransaction)
    private transactionRepository: Repository<AdminTransaction>,
    @InjectRepository(Brand)
    private brandRepository: Repository<Brand>,
    @InjectRepository(Transaction)
    private transactionRepo: Repository<Transaction>,
    @InjectRepository(Asset)
    private assetRepository: Repository<Asset>,
  ) {}

  async createTransaction(
    currentUser: AdminPayload,
    requestData: CreateAdminTransactionDto,
  ) {
    const lockKeys = [`transaction-admin-${currentUser.id}`];
    return await this.commonService.withLock(lockKeys, async () => {
      const transaction = await this.transactionRepository.save({
        type: requestData.type,
        status: AdminTransactionStatus.DRAFT,
        createdBy: currentUser.id,
      });
      try {
        switch (transaction.type) {
          case AdminTransactionType.CREATE_COLLECTION:
            await this.createCollection(requestData, transaction);
            break;
          default:
            throw ApiError(
              ErrorCode.INVALID_DATA,
              `Unsupported transaction ${transaction.type}`,
            );
        }
      } catch (error) {
        await this.transactionRepository.update(
          { id: transaction.id },
          {
            status: AdminTransactionStatus.FAIL,
            error: {
              message: error?.message,
              stack: error?.stack,
              ...error,
            },
          },
        );
        throw error;
      }
      const updatedTransaction = await this.transactionRepository.findOneBy({
        id: transaction.id,
      });
      return {
        id: updatedTransaction.id,
        type: updatedTransaction.type,
        txHash: updatedTransaction.txHash,
        status: updatedTransaction.status,
      };
    });
  }

  async createCollection(
    requestData: CreateAdminTransactionDto,
    transaction: AdminTransaction,
  ) {
    await this.transactionRepository.update(
      { id: transaction.id },
      {
        brandId: requestData.brandId,
      },
    );
  }

  async getTransactions(requestData: GetTransactionAdminDto) {
    let queryBuilder = this.transactionRepo
      .createQueryBuilder('tx')
      .leftJoinAndSelect('tx.brand', 'brand')
      .leftJoinAndSelect('tx.userFrom', 'userFrom')
      .leftJoinAndSelect('userFrom.userChains', 'userFromChain')
      .leftJoinAndSelect('userFromChain.chain', 'fromChain')
      .leftJoinAndSelect('tx.voucherCode', 'voucherCode')
      .leftJoinAndSelect('voucherCode.voucher', 'voucher')
      .leftJoinAndSelect('tx.article', 'article')
      .leftJoinAndSelect('tx.quest', 'quest')
      .leftJoinAndSelect('tx.asset', 'asset')
      .where('fromChain.blockchainId = :chainId', {
        chainId: process.env.CHAIN_ID,
      })
      .select([
        'tx.id',
        'tx.fromUserId',
        'tx.type',
        'tx.subType',
        'tx.point',
        'tx.txHash',
        'tx.status',
        'tx.createdAt',
        'brand.id',
        'brand.name',
        'userFrom.id',
        'userFrom.email',
        'userFromChain.walletAddress',
        'fromChain.imageUrl',
        'voucherCode.id',
        'voucher.id',
        'voucher.name',
        'voucher.imageUrl',
        'article.id',
        'article.title',
        'article.imageUrl',
        'quest.id',
        'quest.imageUrl',
        'quest.title',
        'asset.name',
        'asset.image',
      ]);

    if (requestData.keyword) {
      Utils.applySearch(queryBuilder, requestData.keyword, [
        { name: 'userFrom.email', isAbsolute: false },
        { name: 'userFromChain.walletAddress', isAbsolute: false },
        { name: 'voucher.name', isAbsolute: false },
      ]);
    }

    if (
      !requestData.filterType ||
      requestData.filterType === TransactionAdminFilterType.EARN
    ) {
      queryBuilder.andWhere(
        `(tx.subType IN (:...pointSubTypes) AND tx.type = :earnType)`,
        {
          pointSubTypes: [TransactionSubType.LEARN, TransactionSubType.QUEST],
          earnType: TransactionType.EARN,
        },
      );
    } else if (requestData.filterType === TransactionAdminFilterType.REDEEM) {
      queryBuilder.andWhere(
        `tx.subType = :voucherSubType AND tx.type = :voucherType`,
        {
          voucherSubType: TransactionSubType.ASSET,
          voucherType: TransactionType.REDEEM,
        },
      );
    }

    queryBuilder = Utils.filterByConditions(queryBuilder, requestData);

    if (requestData.activity) {
      queryBuilder.andWhere(`tx.subType = :subType`, {
        subType: requestData.activity,
      });
    }

    if (requestData.brand) {
      queryBuilder.andWhere(`brand.id = :brandId`, {
        brandId: requestData.brand,
      });
    }

    if (requestData.token) {
      queryBuilder.andWhere(`asset.name = :token`, {
        token: requestData.token,
      });
    }

    return Utils.paginate<Transaction>(queryBuilder, requestData);
  }

  async getTokens() {
    const token = await this.assetRepository
      .createQueryBuilder('asset')
      .where('asset.type = :type', { type: AssetType.TOKEN })
      .select(['asset.id', 'asset.name', 'asset.symbol'])
      .getMany();
    return token;
  }
}
