import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEmail,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { SearchDto } from 'src/common/dto.common';
import {
  SendOption,
  TransactionSubType,
  TransactionType,
} from 'src/entities/transaction.entity';

export class CreateTransactionDto {
  @ApiProperty({ enum: TransactionType })
  @IsEnum(TransactionType)
  type: TransactionType;

  @ApiProperty({ enum: TransactionSubType })
  @IsEnum(TransactionSubType)
  subType: TransactionSubType;

  @ApiPropertyOptional({ type: Number })
  @IsOptional()
  @IsNumber()
  questId: number;

  @ApiPropertyOptional({ type: Number })
  @IsOptional()
  @IsNumber()
  articleId: number;

  @ApiPropertyOptional({ type: Number })
  @IsOptional()
  @IsNumber()
  userAssetId: number;

  @ApiPropertyOptional({ type: Number })
  @IsOptional()
  @IsNumber()
  assetId: number;

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  @IsNumber()
  redeemItemId: number;

  @IsOptional()
  @IsEmail()
  receiverEmail: string;

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  @IsString()
  receiveNex3Wallet: string;

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  @IsString()
  amount: string;
}
