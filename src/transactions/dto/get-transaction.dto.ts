import { IsEnum, IsOptional } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { SearchDto } from 'src/common/dto.common';

export enum TransactionFilterType {
  POINT = 'points',
  VOUCHER = 'vouchers',
}

export class GetTransactionDto extends SearchDto {
  @ApiPropertyOptional({ enum: TransactionFilterType })
  @IsEnum(TransactionFilterType)
  @IsOptional()
  filterType?: TransactionFilterType;
}
