import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsDate, IsEnum, IsOptional, IsString } from 'class-validator';
import { SearchDto } from 'src/common/dto.common';
import { TransactionStatus } from 'src/entities/transaction.entity';

export enum TransactionAdminFilterType {
  EARN = 'earn',
  REDEEM = 'redeem',
}

export class GetTransactionAdminDto extends SearchDto {
  @IsEnum(TransactionAdminFilterType)
  @IsOptional()
  @ApiProperty({
    enum: TransactionAdminFilterType,
    default: TransactionAdminFilterType.EARN,
  })
  filterType?: TransactionAdminFilterType.EARN;

  @IsDate()
  @IsOptional()
  @Type(() => Date)
  @ApiProperty({ required: false })
  dateFrom: Date;

  @IsDate()
  @IsOptional()
  @Type(() => Date)
  @ApiProperty({ required: false })
  dateTo: Date;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
  })
  activity: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
  })
  brand: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
  })
  token: string;

  @IsEnum(TransactionStatus)
  @IsOptional()
  @ApiProperty({
    required: false,
  })
  status: TransactionStatus;
}
