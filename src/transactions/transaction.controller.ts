import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation } from '@nestjs/swagger';
import { TransactionService } from './transaction.service';
import { JwtAuthGuard } from 'src/auth/guard/jwt-auth.guard';
import { UserPayload } from 'src/entities/user.entity';
import { User } from 'src/common/decorator/user.decorator';
import { GetTransactionDto } from './dto/get-transaction.dto';
import { CreateTransactionDto } from './dto/create-transaction.dto';

@ApiBearerAuth()
@ApiTags('Transactions')
@UseGuards(JwtAuthGuard)
@Controller('transactions')
export class TransactionController {
  constructor(private readonly transactionService: TransactionService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new transaction' })
  async create(
    @User() currentUser: UserPayload,
    @Body() requestData: CreateTransactionDto,
  ) {
    return this.transactionService.createTransaction(currentUser, requestData);
  }

  @Get('')
  @ApiOperation({ summary: 'Get list of transactions' })
  async getTransactions(
    @User() currentUser: UserPayload,
    @Query() requestData: GetTransactionDto,
  ) {
    const answer = await this.transactionService.getTransactions(
      currentUser,
      requestData,
    );
    return answer;
  }

  @Get('/:id')
  @ApiOperation({ summary: 'Get transaction detail by ID' })
  async getDetailTransaction(
    @User() currentUser: UserPayload,
    @Param('id') id: number,
  ) {
    const answer = await this.transactionService.getDetailTransaction(
      currentUser,
      +id,
    );
    return answer;
  }

  @Get('/:id/check-status')
  @ApiOperation({ summary: 'Check transaction status by ID' })
  async checkTransactionStatus(
    @User() currentUser: UserPayload,
    @Param('id') id: number,
  ) {
    const answer = await this.transactionService.checkTransactionStatus(
      currentUser,
      +id,
    );
    return answer;
  }

  @Get('/:id/get-transaction-event')
  @ApiOperation({ summary: 'Get transaction event by ID' })
  async getTransactionEvent(
    @User() currentUser: UserPayload,
    @Param('id') id: number,
  ) {
    const answer = await this.transactionService.getTransactionEvent(
      currentUser,
      +id,
    );
    return answer;
  }
}
