import { HttpException, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Utils } from 'src/common/utils';
import { Article } from 'src/entities/article.entity';
import { Quest } from 'src/entities/quest.entity';
import {
  SendOption,
  Transaction,
  TransactionDisplayType,
  TransactionStatus,
  TransactionSubType,
  TransactionType,
} from 'src/entities/transaction.entity';
import { User, UserPayload } from 'src/entities/user.entity';
import {
  DataSource,
  EntityManager,
  MoreThanOrEqual,
  Repository,
} from 'typeorm';
import {
  GetTransactionDto,
  TransactionFilterType,
} from './dto/get-transaction.dto';
import { Asset, AssetType, AssetTypeLabel } from 'src/entities/asset.entity';
import { CreateTransactionDto } from './dto/create-transaction.dto';
import { UserQuest, UserQuestStatus } from 'src/entities/user-quest.entity';
import { ApiError } from 'src/common/api';
import { ErrorCode } from 'src/common/constants';
import { CommonService } from 'src/common-services/common.service';
import { UserChain } from 'src/entities/user-chain.entity';
import { UserAsset, UserAssetStatus } from 'src/entities/user-asset.entity';
import {
  UserArticleAnswer,
  UserArticleAnswerStatus,
} from 'src/entities/user-article-answer.entity';
import { Brand } from 'src/entities/brand.entity';
import {
  PaidType,
  RedeemItemType,
  RedeemItem,
} from 'src/entities/redeem-item.entity';
import { Web3ServiceFactory } from 'src/web3/web3.factory.service';
import { Chain, ChainType } from 'src/entities/chain.entity';
import { QueueService } from 'src/queue/queue.service';
import { SponsorAsset } from 'src/entities/sponsor-asset.entity';
import { Web3SignedTransaction } from 'src/web3/web3.interface';

@Injectable()
export class TransactionService {
  private readonly logger = new Logger(TransactionService.name);

  constructor(
    private commonService: CommonService,
    private queueService: QueueService,
    private dataSource: DataSource,
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
    @InjectRepository(Asset)
    private assetRepository: Repository<Asset>,
    @InjectRepository(UserQuest)
    private userQuestRepository: Repository<UserQuest>,
    @InjectRepository(Quest)
    private questRepository: Repository<Quest>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(UserChain)
    private userChainRepository: Repository<UserChain>,
    @InjectRepository(UserAsset)
    private userAssetRepository: Repository<UserAsset>,
    @InjectRepository(UserArticleAnswer)
    private userArticleAnswerRepository: Repository<UserArticleAnswer>,
    @InjectRepository(Article)
    private articleRepository: Repository<Article>,
    @InjectRepository(Brand)
    private brandRepository: Repository<Brand>,
    @InjectRepository(RedeemItem)
    private redeemItemRepository: Repository<RedeemItem>,
    @InjectRepository(SponsorAsset)
    private sponsorAssetRepository: Repository<SponsorAsset>,
  ) {}

  async createTransaction(
    currentUser: UserPayload,
    requestData: CreateTransactionDto,
  ) {
    const lockKeys = [`transaction-user-${currentUser.id}`];
    if (requestData.type === TransactionType.REDEEM) {
      lockKeys.push(`redeem-item-${requestData.redeemItemId}`);
    }
    const transaction = await this.commonService.withLock(
      lockKeys,
      async () => {
        switch (`${requestData.type}_${requestData.subType}`) {
          case `${TransactionType.EARN}_${TransactionSubType.QUEST}`:
            return await this.claimQuestReward(currentUser, requestData);
          case `${TransactionType.EARN}_${TransactionSubType.LEARN}`:
            return await this.claimArticleReward(currentUser, requestData);
          case `${TransactionType.REDEEM}_${TransactionSubType.REDEEM_ITEM}`:
            return await this.redeem(currentUser, requestData);
          case `${TransactionType.TRANSFER}_${TransactionSubType.ASSET}`:
            return await this.sendAsset(currentUser, requestData);
          default:
            throw ApiError(
              ErrorCode.INVALID_DATA,
              `Unsupported transaction ${requestData.type}_${requestData.subType}`,
            );
        }
      },
    );
    const updatedTransaction = await this.transactionRepository.findOneBy({
      id: transaction.id,
    });
    return {
      id: updatedTransaction.id,
      type: updatedTransaction.type,
      subType: updatedTransaction.subType,
      point: updatedTransaction.point,
      redeemedAmount: updatedTransaction.redeemedAmount,
      txHash: updatedTransaction.txHash,
      status: updatedTransaction.status,
    } as Partial<Transaction>;
  }

  private async sendAsset(
    currentUser: UserPayload,
    requestData: CreateTransactionDto,
  ) {
    const senderUserAsset = await this.commonService.getUserAssetTransferNft(
      currentUser.id,
      requestData.assetId,
      requestData?.userAssetId,
    );

    if (!senderUserAsset) {
      throw ApiError('E20', 'Asset not found');
    }

    if (
      senderUserAsset &&
      senderUserAsset.validTo &&
      senderUserAsset.validTo < new Date()
    ) {
      throw ApiError(
        'E14',
        'Asset is expired. Please check the validity period of the asset.',
      );
    }

    if (
      Utils.toBigNumber(senderUserAsset?.balance).lt(
        Utils.toBigNumber(requestData.amount),
      )
    ) {
      throw ApiError(
        'E12',
        '[sendAsset] Insufficient balance. Please top up your balance to proceed.',
      );
    }

    // Get asset
    const asset = senderUserAsset.asset;
    const chain = asset.chain;
    const receiverUser = await this.getReceiverUser(currentUser, requestData);

    // Send asset
    const web3 = await this.commonService.getWeb3ByChainId(chain.id);
    let signedTx: Web3SignedTransaction;
    const transaction = await this.dataSource.transaction(async (manager) => {
      const lockKeys = [
        `user-asset-${currentUser.id}`,
        `user-asset-${receiverUser.id}`,
      ];
      return await this.commonService.withLock<Transaction>(
        lockKeys,
        async () => {
          // Create transaction
          let transaction = await manager.create(Transaction, {
            type: requestData.type,
            subType: requestData.subType,
            brandId: currentUser.brandId,
            fromUserId: currentUser.id,
            toUserId: receiverUser.id,
            point: asset.type === AssetType.TOKEN ? requestData.amount : '1',
            chainId: chain.id,
            assetId: requestData.assetId,
            nftId: senderUserAsset.id,
            redeemItemId:
              asset.type !== AssetType.TOKEN
                ? senderUserAsset?.redeemItemId
                : null,
            status: TransactionStatus.PROCESSING,
            sendOption: requestData.receiveNex3Wallet
              ? SendOption.TO_NEX3_WALLET
              : SendOption.TO_EMAIL,
          });
          transaction = await manager.save(transaction);
          signedTx = await web3.createSignedTransaction(transaction);
          if (chain.chainType === ChainType.MOVEVM) {
            const response = await web3.sendSignedTransaction(signedTx);
            transaction.txHash = response.hash;
          } else {
            transaction.txHash = signedTx.txHash;
          }

          if (!transaction.txHash) {
            throw ApiError('', 'Failed to create signed transaction');
          }

          transaction.hotWalletId = signedTx.hotwallet.id;
          transaction = await manager.save(transaction);
          // await manager.save(transaction);

          // Update sender user asset balance
          await manager.update(
            UserAsset,
            { id: senderUserAsset.id },
            { balance: () => `balance - ${requestData.amount}` },
          );

          // Update receiver user asset balance
          if (asset.type === AssetType.TOKEN) {
            const receiverUserAsset = await manager.findOneBy(UserAsset, {
              userId: receiverUser.id,
              assetId: requestData.assetId,
              id: requestData.userAssetId,
            });
            if (!receiverUserAsset) {
              await manager.save(UserAsset, {
                userId: receiverUser.id,
                assetId: requestData.assetId,
                balance: requestData.amount,
                status: UserAssetStatus.REDEEMED,
              });
            } else {
              await manager.update(
                UserAsset,
                { id: receiverUserAsset.id },
                { balance: () => `balance + ${requestData.amount}` },
              );
            }
          } else {
            await manager.update(
              UserAsset,
              { id: senderUserAsset.id },
              {
                status: UserAssetStatus.PROCESSING,
                balance: () => `balance + ${requestData.amount}`,
              },
            );
          }
          return transaction;
        },
      );
    });

    // Add to queue to confirm transaction
    try {
      if (chain.chainType === ChainType.EVM) {
        await web3.sendSignedTransaction(signedTx);
      }

      await this.queueService.addTransactionQueue({
        transactionId: transaction.id,
        assetType: asset.type as AssetType,
      });
    } catch (error) {
      await this.dataSource.transaction(async (manager) => {
        await this.commonService.rollbackTransactionSendAsset({
          manager,
          transaction,
          error,
        });
      });
      throw error;
    }

    return transaction;
  }

  private async getReceiverUser(
    currentUser: UserPayload,
    requestData: CreateTransactionDto,
  ) {
    let receiverUser: User;

    if (requestData.receiverEmail) {
      receiverUser = await this.userRepository.findOneBy({
        email: requestData.receiverEmail,
      });

      if (!receiverUser) {
        throw ApiError('E17', 'Email does not exist in the system');
      }

      if (receiverUser.id === currentUser.id) {
        throw ApiError(
          'E18',
          'You cannot send points to your own account. Please enter a different recipient.',
        );
      }
    }

    if (requestData.receiveNex3Wallet) {
      const targetWallet = Utils.formatEVMAddress(
        requestData.receiveNex3Wallet,
      );

      const walletExists = await this.userChainRepository.findOneBy({
        walletAddress: targetWallet,
      });

      if (!walletExists) {
        throw ApiError('E19', 'Wallet address does not exist in the system');
      }

      const isSelfWallet = await this.userChainRepository.findOneBy({
        userId: currentUser.id,
        walletAddress: targetWallet,
      });

      if (isSelfWallet) {
        throw ApiError(
          'E18',
          'You cannot send points to your own account. Please enter a different recipient.',
        );
      }

      const userChainWithUser = await this.userChainRepository
        .createQueryBuilder('userChain')
        .leftJoinAndSelect('userChain.user', 'user')
        .where('userChain.walletAddress = :wallet', { wallet: targetWallet })
        .getOne();

      receiverUser = userChainWithUser?.user;
    }
    return receiverUser;
  }

  private async claimQuestReward(
    currentUser: UserPayload,
    requestData: CreateTransactionDto,
  ) {
    // Validate
    const userQuest = await this.userQuestRepository
      .createQueryBuilder('userQuest')
      .innerJoinAndSelect('userQuest.quest', 'quest')
      .where('userQuest.status = :status', {
        status: UserQuestStatus.UNCLAIMED,
      })
      .andWhere('userQuest.questId = :questId', {
        questId: requestData.questId,
      })
      .andWhere('userQuest.userId = :userId', {
        userId: currentUser.id,
      })
      .getOne();
    if (!userQuest) {
      throw ApiError(
        ErrorCode.NO_DATA_EXISTS,
        'User quest not found or already claimed',
      );
    }
    const quest = userQuest.quest;
    if (quest.toDate < new Date()) {
      throw ApiError(
        'E10',
        'You cannot claim the reward because the quest is no longer available. It may have ended or been fully claimed.',
      );
    }

    // Claim reward
    return await this.dataSource.transaction(async (manager) => {
      return await this.commonService.withLock(
        [
          `user-${currentUser.id}`,
          `quest-${quest.id}`,
          `user-quest-${userQuest.id}`,
        ],
        async () => {
          // Create transaction
          const transaction = await manager.save(Transaction, {
            type: requestData.type,
            subType: requestData.subType,
            brandId: currentUser.brandId,
            fromUserId: currentUser.id,
            toUserId: currentUser.id,
            questId: quest.id,
            userQuestId: userQuest.id,
            point: quest.point,
            status: TransactionStatus.SUCCESS,
          });

          // Update user point
          await manager.update(
            User,
            { id: transaction.fromUserId },
            {
              point: () => `point + ${transaction.point}`,
            },
          );

          // Update quest
          await manager.update(
            Quest,
            { id: transaction.questId },
            {
              claimedPoint: () => `claimedPoint + ${transaction.point}`,
            },
          );

          // Update user quest
          await manager.update(
            UserQuest,
            {
              id: transaction.userQuestId,
              status: UserQuestStatus.UNCLAIMED,
            },
            {
              status: UserQuestStatus.CLAIMED,
              claimedAt: new Date(),
            },
          );

          return transaction;
        },
      );
    });
  }

  private async claimArticleReward(
    currentUser: UserPayload,
    requestData: CreateTransactionDto,
  ) {
    // Validate
    const userArticleAnswer = await this.userArticleAnswerRepository
      .createQueryBuilder('userArticleAnswer')
      .innerJoinAndSelect('userArticleAnswer.article', 'article')
      .where('userArticleAnswer.status = :status', {
        status: UserArticleAnswerStatus.UNCLAIMED,
      })
      .andWhere('userArticleAnswer.articleId = :articleId', {
        articleId: requestData.articleId,
      })
      .andWhere('userArticleAnswer.userId = :userId', {
        userId: currentUser.id,
      })
      .getOne();
    if (!userArticleAnswer) {
      throw ApiError(
        '',
        'User not do this article or this article already claimed',
      );
    }
    const article = userArticleAnswer.article;

    // Claim reward
    return await this.dataSource.transaction(async (manager) => {
      return await this.commonService.withLock(
        [
          `user-${currentUser.id}`,
          `article-${article.id}`,
          `user-article-answer-${userArticleAnswer.id}`,
        ],
        async () => {
          // Create transaction
          const transaction = await manager.save(Transaction, {
            type: requestData.type,
            subType: requestData.subType,
            brandId: currentUser.brandId,
            fromUserId: currentUser.id,
            toUserId: currentUser.id,
            articleId: article.id,
            userArticleAnswerId: userArticleAnswer.id,
            point: article.point,
            status: TransactionStatus.SUCCESS,
          });

          // Update user point
          await manager.update(
            User,
            { id: transaction.fromUserId },
            {
              point: () => `point + ${transaction.point}`,
            },
          );

          // Update article
          await manager.update(
            Article,
            {
              id: transaction.articleId,
            },
            {
              claimedPoint: () => `claimedPoint + ${transaction.point}`,
            },
          );

          // Update user article answer
          await manager.update(
            UserArticleAnswer,
            {
              id: transaction.userArticleAnswerId,
              status: UserArticleAnswerStatus.UNCLAIMED,
            },
            {
              status: UserArticleAnswerStatus.CLAIMED,
              claimedAt: new Date(),
            },
          );

          return transaction;
        },
      );
    });
  }

  private async getRedeemedNFT(redeemItem: RedeemItem) {
    if (redeemItem.asset.type === AssetType.TOKEN) {
      return;
    }
    const nft = await this.dataSource
      .getRepository(UserAsset)
      .createQueryBuilder()
      .where('assetId = :assetId', {
        assetId: redeemItem.assetId,
      })
      .andWhere('redeemItemId = :redeemItemId', {
        redeemItemId: redeemItem.id,
      })
      .andWhere('status = :status', { status: UserAssetStatus.UNREDEEMED })
      .orderBy('RAND()')
      .limit(1)
      .getOne();

    if (!nft) {
      throw ApiError(
        'E11',
        '[getRedeemedNFT] Sorry, the NFT is not available. It may have expired or been fully redeemed.',
      );
    }

    return nft;
  }

  private async validateRedeem(
    currentUser: UserPayload,
    requestData: CreateTransactionDto,
  ) {
    const redeemItem = await this.commonService.getRedeemItem(
      requestData.redeemItemId,
    );
    if (!redeemItem) {
      throw ApiError('E11', 'Redeem item not found');
    }

    if (
      Utils.toBigNumber(redeemItem.remainReward).lt(
        Utils.toBigNumber(redeemItem.redeemedAmount),
      )
    ) {
      throw ApiError(
        'E11',
        '[validateRedeem] Sorry, the redeem item is not available. It may have expired or been fully redeemed.',
      );
    }

    // Validate balance
    let balance = Utils.toBigNumber(0);
    let paidUserAsset: UserAsset = null;
    // Redeem with NEX3 XP
    if (redeemItem.paidType === PaidType.NEX3_XP) {
      const user = await this.userRepository.findOneBy({
        id: currentUser.id,
      });
      balance = Utils.toBigNumber(user.point);
    }
    // Redeem with asset
    else {
      paidUserAsset = await this.userAssetRepository.findOneBy({
        // redeemItemId: redeemItem.id,
        userId: currentUser.id,
        assetId: redeemItem.paidAssetId,
      });
      balance = Utils.toBigNumber(paidUserAsset?.balance ?? 0);
    }
    if (balance.lt(Utils.toBigNumber(redeemItem.point))) {
      throw ApiError(
        'E12',
        '[validateRedeem] Insufficient balance. Please top up your balance to proceed. 1',
      );
    }

    const isRedeemLimit = await this.checkRedeemLimit(
      currentUser.id,
      redeemItem,
    );
    if (!isRedeemLimit) {
      throw ApiError(
        'E12',
        'You have reached the limit of redeeming this item.',
      );
    }
    return redeemItem;
  }

  private async checkRedeemLimit(
    userId: number,
    redeemItem: RedeemItem,
  ): Promise<boolean> {
    // Fetch the redeem item to get the redeemCondition

    // If redeemCondition is not defined, allow unlimited redemption
    if (
      !redeemItem ||
      !redeemItem.redeemCondition ||
      !redeemItem.redeemCondition.limitPerUser
    ) {
      return true; // Unlimited redemption
    }

    // Count the number of transactions for this user and redeem item
    const transactionCount = await this.transactionRepository.count({
      where: {
        fromUserId: userId,
        redeemItemId: redeemItem.id,
      },
    });

    // Check if the user has reached the limit
    return transactionCount < redeemItem.redeemCondition.limitPerUser;
  }

  private async updatePaidUserAsset(data: {
    manager: EntityManager;
    transaction: Transaction;
    redeemItem: RedeemItem;
  }) {
    const { manager, transaction, redeemItem } = data;

    // Paid with NEX3 XP
    if (redeemItem.paidType === PaidType.NEX3_XP) {
      this.logger.log(
        `updatePaidUserAsset(): Subtract ${transaction.point} point from user ${transaction.fromUserId}`,
      );
      const user = await manager.findOneBy(User, {
        id: transaction.fromUserId,
      });
      if (
        Utils.toBigNumber(user.point).lt(Utils.toBigNumber(transaction.point))
      ) {
        throw ApiError(
          'E12',
          '[updatePaidUserAsset - Paid with NEX3 XP] Insufficient balance. Please top up your balance to proceed.',
        );
      }
      await manager.update(
        User,
        {
          id: transaction.fromUserId,
        },
        {
          point: () => `point - ${transaction.point}`,
        },
      );
    }
    // Paid with asset
    else {
      this.logger.log(
        `updatePaidUserAsset(): Subtract ${transaction.point} of asset ${redeemItem.paidAssetId} from user ${transaction.fromUserId}`,
      );
      const paidUserAsset = await manager.findOneBy(UserAsset, {
        userId: transaction.fromUserId,
        assetId: redeemItem.paidAssetId,
      });
      if (
        Utils.toBigNumber(paidUserAsset.balance).lt(
          Utils.toBigNumber(transaction.point),
        )
      ) {
        throw ApiError(
          'E12',
          '[updatePaidUserAsset - Paid with asset] Insufficient balance. Please top up your balance to proceed.',
        );
      }
      await manager.update(
        UserAsset,
        { id: paidUserAsset.id },
        {
          balance: () => `balance - ${transaction.point}`,
        },
      );
    }
  }

  private async updateSponsorAsset(data: {
    manager: EntityManager;
    transaction: Transaction;
    redeemItem: RedeemItem;
  }) {
    const { manager, transaction, redeemItem } = data;
    const sponsorAsset = await manager.findOneBy(SponsorAsset, {
      id: redeemItem.sponsorAssetId,
    });
    if (
      Utils.toBigNumber(sponsorAsset.remainReward).lt(redeemItem.redeemedAmount)
    ) {
      throw ApiError(
        'E11',
        'Sorry, the sponsor asset is not available. It may have expired or been fully redeemed.',
      );
    }
    await manager.update(
      SponsorAsset,
      { id: sponsorAsset.id },
      {
        remainReward: () => `remainReward - ${transaction.redeemedAmount}`,
      },
    );
  }

  private async updateRedeemedUserAsset(data: {
    manager: EntityManager;
    transaction: Transaction;
    redeemItem: RedeemItem;
    nft: UserAsset;
  }) {
    const { manager, transaction, redeemItem, nft } = data;
    const redeemedAsset = redeemItem.asset;

    if (redeemedAsset.type === AssetType.TOKEN) {
      this.logger.log(
        `updateRedeemedUserAsset(): Add balance ${redeemItem.redeemedAmount} of asset ${redeemedAsset.id} to user ${transaction.toUserId}`,
      );
      const redeemedUserAsset = await manager.findOneBy(UserAsset, {
        userId: transaction.toUserId,
        assetId: redeemedAsset.id,
      });
      if (redeemedUserAsset) {
        await manager.update(
          UserAsset,
          { id: redeemedUserAsset.id },
          { balance: () => `balance + ${redeemItem.redeemedAmount}` },
        );
      } else {
        await manager.save(UserAsset, {
          userId: transaction.toUserId,
          assetId: redeemedAsset.id,
          balance: redeemItem.redeemedAmount.toString(),
          status: UserAssetStatus.REDEEMED,
        });
      }
    } else {
      this.logger.log(
        `updateRedeemedUserAsset(): Update nft ${nft.id} to processing`,
      );
      const result = await manager.update(
        UserAsset,
        { id: nft.id, status: UserAssetStatus.UNREDEEMED },
        {
          status: UserAssetStatus.PROCESSING,
        },
      );
      if (result.affected === 0) {
        throw ApiError(
          'E13',
          '[updateRedeemedUserAsset] Sorry, the NFT is not available. It may have expired or been fully redeemed.',
        );
      }
    }
  }

  private async redeem(
    currentUser: UserPayload,
    requestData: CreateTransactionDto,
  ) {
    const redeemItem = await this.validateRedeem(currentUser, requestData);
    const chain = redeemItem.chain;
    const redeemedAsset = redeemItem.asset;
    // Redeem
    const web3 = await this.commonService.getWeb3ByChain(chain);
    const userChain = await this.userChainRepository.findOneBy({
      userId: currentUser.id,
      chainId: chain.id,
    });

    if (chain.chainType === ChainType.EVM) {
      const delegate = await web3.setUpDelegation(
        userChain.walletAddress,
        Utils.decrypt(userChain.privateKey),
        chain,
      );

      if (!delegate) {
        throw ApiError(
          'E14',
          'You are not delegated to the gas sponsor contract',
        );
      }
    }

    let signedTx: Web3SignedTransaction;
    const transaction = await this.dataSource.transaction(async (manager) => {
      const lockKeys = [
        `user-${currentUser.id}`,
        `user-asset-${currentUser.id}`,
        `sponsor-asset-${redeemItem.sponsorAssetId}`,
      ];
      this.logger.log(`redeem(): lock keys: ${lockKeys.join(', ')}`);
      return await this.commonService.withLock<Transaction>(
        lockKeys,
        async () => {
          const nft = await this.getRedeemedNFT(redeemItem);

          // Create transaction
          let transaction = await manager.create(Transaction, {
            type: requestData.type,
            subType: requestData.subType,
            brandId: currentUser.brandId,
            fromUserId: currentUser.id,
            toUserId: currentUser.id,
            chainId: chain.id,
            sponsorId: redeemItem.sponsorId,
            sponsorAssetId: redeemItem.sponsorAssetId,
            redeemItemId: redeemItem.id,
            assetId: redeemedAsset.id,
            point: redeemItem.point,
            paidType: redeemItem.paidType,
            paidAssetId: redeemItem.paidAssetId, // for redeem using token
            redeemedAmount: redeemItem.redeemedAmount.toString(),
            nftId: nft?.id, // for redeem nft
            status: TransactionStatus.PROCESSING,
          });

          transaction = await manager.save(transaction);
          signedTx = await web3.createSignedTransaction(transaction);
          if (chain.chainType === ChainType.MOVEVM) {
            const response = await web3.sendSignedTransaction(signedTx);
            transaction.txHash = response.hash;
          } else {
            transaction.txHash = signedTx.txHash;
          }

          if (!transaction.txHash) {
            throw ApiError('', 'Failed to create signed transaction');
          }

          transaction.hotWalletId = signedTx.hotwallet.id;
          transaction = await manager.save(transaction);

          if (!transaction.txHash) {
            throw ApiError('', 'Failed to create signed transaction');
          }

          transaction.hotWalletId = signedTx.hotwallet.id;
          transaction = await manager.save(transaction);

          // Update user point
          await this.updatePaidUserAsset({
            manager,
            transaction,
            redeemItem,
          });

          // Update sponsor asset
          await this.updateSponsorAsset({
            manager,
            transaction,
            redeemItem,
          });

          // Update asset
          await this.updateRedeemedUserAsset({
            manager,
            transaction,
            redeemItem,
            nft,
          });

          // Update redeem item
          await manager.update(
            RedeemItem,
            {
              id: transaction.redeemItemId,
            },
            {
              remainReward: () => `remainReward - ${redeemItem.redeemedAmount}`,
              status: () =>
                `CASE WHEN remainReward - ${redeemItem.redeemedAmount} <= 0 THEN 'out_of_stock' ELSE status END`,
            },
          );
          return transaction;
        },
      );
    });

    // Add to queue to confirm transaction
    try {
      if (chain.chainType === ChainType.EVM) {
        await web3.sendSignedTransaction(signedTx);
      }

      await this.queueService.addTransactionQueue({
        transactionId: transaction.id,
        assetType: redeemedAsset.type as AssetType,
      });
    } catch (error) {
      await this.dataSource.transaction(async (manager) => {
        await this.commonService.rollbackTransactionRedeem({
          manager,
          transaction,
          redeemItem,
          error,
        });
      });
      throw error;
    }

    return transaction;
  }

  async getTransactions(
    currentUser: UserPayload,
    requestData: GetTransactionDto,
  ) {
    const queryBuilder = this.transactionRepository
      .createQueryBuilder('tx')
      .leftJoin('tx.article', 'article')
      .leftJoin('tx.quest', 'quest')
      .leftJoin('tx.asset', 'asset')
      .leftJoin('tx.redeemItem', 'redeemItem')
      .leftJoin('tx.paidAsset', 'paidAsset')
      .where(
        '(tx.fromUserId = :userId OR tx.toUserId = :userId) AND tx.brandId = :brandId AND tx.status = :status',
        {
          userId: currentUser.id,
          brandId: currentUser.brandId,
          status: TransactionStatus.SUCCESS,
        },
      );

    if (
      !requestData.filterType ||
      requestData.filterType === TransactionFilterType.POINT
    ) {
      queryBuilder
        .andWhere(`tx.subType IN (:...pointSubTypes)`, {
          pointSubTypes: [
            TransactionSubType.LEARN,
            TransactionSubType.QUEST,
            TransactionSubType.REDEEM_ITEM,
          ],
        })
        .andWhere('tx.paidAssetId IS NULL');
      queryBuilder.select([
        'tx.id',
        'tx.fromUserId',
        'tx.type',
        'tx.subType',
        'tx.point',
        'tx.status',
        'tx.createdAt',
        'article.id',
        'article.title',
        'quest.id',
        'quest.title',
        'asset.id',
        'asset.name',
        'asset.type',
        'asset.image',
        'redeemItem.id',
        'redeemItem.name',
        'redeemItem.type',
        'redeemItem.imageUrl',
      ]);
    } else if (requestData.filterType === TransactionFilterType.VOUCHER) {
      queryBuilder
        .andWhere('asset.type = :assetType', {
          assetType: AssetType.NFT_VOUCHER,
        })
        .andWhere(
          `(
            (tx.subType = :subTypeAsset AND tx.type = :typeTransfer) 
            OR 
            (tx.subType = :subTypeRedeemItem AND tx.type = :typeRedeem)
          )`,
          {
            subTypeAsset: TransactionSubType.ASSET,
            typeTransfer: TransactionType.TRANSFER,
            subTypeRedeemItem: TransactionSubType.REDEEM_ITEM,
            typeRedeem: TransactionType.REDEEM,
          },
        );
      queryBuilder.select([
        'tx.id',
        'tx.fromUserId',
        'tx.type',
        'tx.subType',
        'tx.status',
        'tx.createdAt',
        'asset.id',
        'asset.name',
        'asset.type',
        'asset.image',
        'redeemItem.id',
        'redeemItem.name',
        'redeemItem.type',
        'redeemItem.imageUrl',
      ]);
    }

    const result = await Utils.paginate<Transaction>(queryBuilder, requestData);

    const newItems = result.items.map((tx) => {
      const cleanedTx: any = { ...tx };

      ['article', 'quest', 'asset', 'redeemItem'].forEach((key) => {
        if (cleanedTx[key] === null) {
          delete cleanedTx[key];
        }
      });

      const isSent =
        tx.type === TransactionType.EARN
          ? false
          : tx.type === TransactionType.REDEEM &&
              requestData.filterType === TransactionFilterType.VOUCHER
            ? false
            : tx.type === TransactionType.REDEEM
              ? true
              : tx.fromUserId === currentUser.id;

      cleanedTx.isSent = isSent;
      cleanedTx.displayType =
        requestData.filterType === TransactionFilterType.VOUCHER
          ? TransactionDisplayType.VOUCHER_RECEIVED
          : TransactionDisplayType.POINT_SPENT;
      return cleanedTx;
    });

    return {
      ...result,
      items: newItems,
    };
  }

  async getDetailTransaction(currentUser: UserPayload, id: number) {
    const brand = await this.brandRepository.findOneBy({
      id: currentUser.brandId,
    });
    const transaction = await this.transactionRepository
      .createQueryBuilder('tx')
      .leftJoin('tx.userFrom', 'userFrom')
      .leftJoin('tx.userTo', 'userTo')
      .leftJoin('userTo.userChains', 'userChain')
      .leftJoin('tx.article', 'article')
      .leftJoin('tx.quest', 'quest')
      .leftJoin('tx.redeemItem', 'redeemItem')
      .leftJoin('tx.asset', 'asset')
      .where(
        'tx.id = :txId AND (tx.fromUserId = :userId OR tx.toUserId = :userId) AND tx.brandId = :brandId AND tx.status = :status',
        {
          txId: id,
          userId: currentUser.id,
          brandId: currentUser.brandId,
          status: TransactionStatus.SUCCESS,
        },
      )
      .select([
        'tx.id',
        'tx.fromUserId',
        'tx.type',
        'tx.subType',
        'tx.sendOption',
        'tx.point',
        'tx.status',
        'tx.createdAt',
        'userFrom.email',
        'userTo.email',
        'userChain.walletAddress',
        'article.id',
        'article.title',
        'article.imageUrl',
        'quest.id',
        'quest.imageUrl',
        'quest.title',
        'asset.id',
        'asset.name',
        'asset.type',
        'asset.image',
        'redeemItem.id',
        'redeemItem.name',
        'redeemItem.type',
        'redeemItem.imageUrl',
      ])
      .getOne();

    if (!transaction) {
      throw ApiError(
        ErrorCode.NO_DATA_EXISTS,
        'Transaction not found or you do not have permission to view this transaction',
      );
    }

    if (transaction.asset) {
      transaction.asset.type = AssetTypeLabel[transaction.asset.type];
    }

    ['article', 'quest', 'asset', 'redeemItem'].forEach((key) => {
      if (!transaction[key]) {
        delete transaction[key];
      }
    });

    let recipient: string;

    if (transaction.sendOption === SendOption.TO_NEX3_WALLET) {
      recipient = transaction.userTo?.userChains?.[0]?.walletAddress;
    } else {
      recipient = transaction.userTo?.email;
    }
    if (transaction.userTo) {
      delete transaction.userTo;
    }

    const isSent =
      transaction.type === TransactionType.REDEEM ||
      transaction.type === TransactionType.EARN
        ? false
        : transaction.fromUserId === currentUser.id;

    return {
      ...transaction,
      brandName: brand.name,
      brandLogo: brand.logoUrl,
      isSent,
      recipient,
    };
  }

  async checkTransactionStatus(currentUser: UserPayload, id: number) {
    const transaction = await this.transactionRepository.findOne({
      where: {
        id: id,
        fromUserId: currentUser.id,
      },
      select: ['id', 'type', 'subType', 'txHash', 'status'],
    });

    const response = new Transaction();
    response.id = transaction.id;
    response.type = transaction.type;
    response.subType = transaction.subType;
    response.txHash = transaction.txHash;
    response.status = transaction.status;

    return response;
  }

  async getTransactionEvent(currentUser: UserPayload, id: number) {
    const transaction = await this.transactionRepository.findOneBy({
      id: id,
    });

    const result = await this.commonService.updateTransaction(
      id,
      AssetType.TOKEN,
    );

    return result;
  }
}
