import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { UserPayload } from 'src/entities/user.entity';
import { User } from 'src/common/decorator/user.decorator';
import { GetTransactionDto } from './dto/get-transaction.dto';
import { JwtAdminAuthGuard } from 'src/auth/guard/jwt-admin-auth.guard';
import { AdminTransactionService } from './transaction.admin.service';
import { GetTransactionAdminDto } from './dto/admin/get-transaction.admin.dto';

@UseGuards(JwtAdminAuthGuard)
@Controller('admin/transactions')
export class TransactionAdminController {
  constructor(
    private readonly transactionAdminService: AdminTransactionService,
  ) {}

  @Get('')
  async getTransactions(@Query() requestData: GetTransactionAdminDto) {
    const answer =
      await this.transactionAdminService.getTransactions(requestData);
    return answer;
  }

  @Get('tokens')
  async getTokens() {
    const answer = await this.transactionAdminService.getTokens();
    return answer;
  }
}
