import { Module } from '@nestjs/common';
import { TransactionService } from './transaction.service';
import { TransactionController } from './transaction.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Transaction } from 'src/entities/transaction.entity';
import { Asset } from 'src/entities/asset.entity';
import { Quest } from 'src/entities/quest.entity';
import { CommonModule } from 'src/common-services/common.module';
import { UserQuest } from 'src/entities/user-quest.entity';
import { User } from 'src/entities/user.entity';
import { UserChain } from 'src/entities/user-chain.entity';
import { UserAsset } from 'src/entities/user-asset.entity';
import { Article } from 'src/entities/article.entity';
import { UserArticleAnswer } from 'src/entities/user-article-answer.entity';
import { AdminTransactionService } from './transaction.admin.service';
import { AdminTransaction } from 'src/entities/admin-transaction.entity';
import { Brand } from 'src/entities/brand.entity';
import { TransactionAdminController } from './transaction.admin.controller';
import { Web3Module } from 'src/web3/web3.module';
import { QueueModule } from 'src/queue/queue.module';
import { SponsorAsset } from 'src/entities/sponsor-asset.entity';
import { RedeemItem } from 'src/entities/redeem-item.entity';

@Module({
  controllers: [TransactionController, TransactionAdminController],
  providers: [TransactionService, AdminTransactionService],
  imports: [
    CommonModule,
    QueueModule,
    TypeOrmModule.forFeature([
      AdminTransaction,
      Transaction,
      Brand,
      Asset,
      Quest,
      UserQuest,
      User,
      UserChain,
      UserAsset,
      Article,
      UserArticleAnswer,
      SponsorAsset,
      RedeemItem,
    ]),
  ],
  exports: [TransactionService, AdminTransactionService],
})
export class TransactionModule {}
