{"name": "vault_sponsor", "address": "0x1", "friends": [], "exposed_functions": [{"name": "grant_token", "visibility": "public", "is_entry": true, "is_view": false, "generic_type_params": [{"constraints": []}], "params": ["address", "address", "u64", "u64"], "return": []}, {"name": "grant_nft", "visibility": "public", "is_entry": true, "is_view": false, "generic_type_params": [], "params": ["address", "address", "u64", "u64"], "return": []}, {"name": "transfer_token", "visibility": "public", "is_entry": true, "is_view": false, "generic_type_params": [{"constraints": []}], "params": ["address", "u64"], "return": []}, {"name": "transfer_nft", "visibility": "public", "is_entry": true, "is_view": false, "generic_type_params": [], "params": ["address", "address", "u64"], "return": []}], "structs": [{"name": "TokenGranted", "is_native": false, "abilities": ["drop", "store"], "generic_type_params": [], "fields": [{"name": "recipient", "type": "address"}, {"name": "token_address", "type": "address"}, {"name": "amount", "type": "u64"}, {"name": "transaction_id", "type": "u64"}]}, {"name": "NFTGranted", "is_native": false, "abilities": ["drop", "store"], "generic_type_params": [], "fields": [{"name": "recipient", "type": "address"}, {"name": "nft_address", "type": "address"}, {"name": "token_id", "type": "u64"}, {"name": "transaction_id", "type": "u64"}]}]}