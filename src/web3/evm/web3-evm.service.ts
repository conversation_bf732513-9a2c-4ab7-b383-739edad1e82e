/* eslint-disable max-lines-per-function */
import { Injectable, Logger } from '@nestjs/common';
import BigNumber from 'bignumber.js';
import { Utils } from 'src/common/utils';
import { Chain, ChainType } from 'src/entities/chain.entity';
import {
  Web3Account,
  Web3CallViewMethodEVM,
  Web3CallWriteMethodEVM,
  Web3ContractParam,
  Web3Event,
  Web3Service,
  Web3SignedTransaction,
  Web3Token,
  Web3Transaction,
} from '../web3.interface';
import * as erc20Abi from './erc20-abi.json';
import * as erc721Abi from './erc721-abi.json';
import * as gasSponsorContractAbi from './nex3-gas-sponsor-abi.json';
import * as nonceTrackerContractAbi from './nex3-nonce-tracker-abi.json';
import * as nex3VoucherContractAbi from './nex3-voucher-abi.json';
import * as vaultSponsorContractAbi from './vault-sponsor-contract-abi.json';

import { InjectRepository } from '@nestjs/typeorm';
import { ethers } from 'ethers';
import { Asset, AssetType } from 'src/entities/asset.entity';
import { HotWallet } from 'src/entities/hot-wallet.entity';
import { PaidType, RedeemItem } from 'src/entities/redeem-item.entity';
import { Sponsor } from 'src/entities/sponsor.entity';
import { Transaction, TransactionType } from 'src/entities/transaction.entity';
import { UserAsset } from 'src/entities/user-asset.entity';
import { UserChain } from 'src/entities/user-chain.entity';
import { DataSource, Repository } from 'typeorm';
import { createWalletClient, http } from 'viem';
import { privateKeyToAccount } from 'viem/accounts';
import { baseSepolia } from 'viem/chains';

@Injectable()
export class Web3EVMService implements Web3Service {
  private readonly logger = new Logger(Web3EVMService.name);

  constructor(
    private dataSource: DataSource,
    @InjectRepository(HotWallet)
    private hotWalletRepository: Repository<HotWallet>,
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
    @InjectRepository(RedeemItem)
    private redeemItemRepository: Repository<RedeemItem>,
    @InjectRepository(Asset)
    private assetRepository: Repository<Asset>,
    @InjectRepository(UserChain)
    private userChainRepository: Repository<UserChain>,
    @InjectRepository(UserAsset)
    private userAssetRepository: Repository<UserAsset>,
    @InjectRepository(Sponsor)
    private sponsorRepository: Repository<Sponsor>,
  ) {}

  chain: Chain;
  provider: ethers.JsonRpcProvider;

  init(chain: Chain) {
    this.chain = chain;
    this.setProvider();
  }

  getChainType(): ChainType {
    return ChainType.EVM;
  }

  setProvider() {
    const rpc = Utils.getRandom(this.chain.rpcs.split(',')).trim();
    this.logger.debug(`Network ${this.chain.chainName}. RPC = ${rpc}`);
    this.provider = new ethers.JsonRpcProvider(rpc);
  }

  private getSigner() {
    const wallet = new ethers.Wallet(
      Utils.decrypt(this.chain.privateKey),
      this.provider,
    );
    return wallet;
  }

  private async isHotwalletPending(hotwallet: HotWallet) {
    await this.setProvider();
    const walletAddress = hotwallet.walletAddress;

    const confirmedNonce = await this.provider.getTransactionCount(
      walletAddress,
      'latest',
    );
    const pendingNonce = await this.provider.getTransactionCount(
      walletAddress,
      'pending',
    );
    const isPending = pendingNonce > confirmedNonce;

    this.logger.debug(
      `Wallet ${walletAddress} is ${isPending ? 'pending' : 'available'}`,
    );

    return isPending;
  }

  private async getHotwallet(contractAddress: string, calldata: string) {
    const hotwallets = await this.hotWalletRepository.find({
      where: {
        chainId: this.chain.id,
      },
    });

    if (hotwallets.length === 0) {
      throw new Error('No hotwallet found');
    }

    // Estimate gas
    let estimatedGas;
    try {
      estimatedGas = await this.provider.estimateGas({
        from: hotwallets[0].walletAddress,
        to: contractAddress,
        data: calldata,
      });
    } catch (err) {
      throw new Error('Estimate gas failed: ' + err.message);
    }
    // this.logger.debug(`Estimate gas: ${estimatedGas}`);

    const gasLimit = Utils.toBigNumber(estimatedGas)
      .multipliedBy(1.5) // 50% buffer gas
      .decimalPlaces(0)
      .toString();

    // Filter hotwallets that have enough balance to execute transaction
    const balances = await Promise.all(
      hotwallets.map(async (wallet) => {
        const balance =
          (await this.getBalance(wallet.walletAddress)) || wallet.balance;
        this.logger.debug(
          `getHotwallet(): Address: ${wallet.walletAddress}, Balance = ${balance}`,
        );
        return { wallet, balance };
      }),
    );

    const available = balances
      .filter(({ balance }) => Utils.toBigNumber(balance).gte(gasLimit))
      .map(({ wallet }) => wallet);

    if (available.length === 0) {
      throw new Error('No hotwallet has enough balance');
    }

    // Shuffle random hotwallets that have enough balance to execute transaction
    while (available.length > 0) {
      const randomIndex = Math.floor(Math.random() * available.length);
      const hotwallet = available[randomIndex];

      const isPending = await this.isHotwalletPending(hotwallet);
      if (!isPending) {
        const wallet = new ethers.Wallet(
          Utils.decrypt(hotwallet.privateKey),
          this.provider,
        );
        return {
          hotwallet,
          gasLimit: gasLimit,
        };
      } else {
        available.splice(randomIndex, 1); // remove hotwallet that is pending
      }
    }
    throw new Error('No hotwallet is available');
  }

  private async callAndSendTransaction(
    contractAddress: string,
    calldata: string,
    isWait = true,
  ) {
    const { gasLimit, hotwallet } = await this.getHotwallet(
      contractAddress,
      calldata,
    );
    const tx: ethers.TransactionRequest = {
      from: hotwallet.walletAddress,
      to: contractAddress,
      data: calldata,
      gasLimit: new BigNumber(gasLimit).decimalPlaces(0).toString(),
    };

    this.logger.debug(`callAndSendTransaction(): tx = ${JSON.stringify(tx)}`);
    const wallet = new ethers.Wallet(
      Utils.decrypt(hotwallet.privateKey),
      this.provider,
    );

    const transaction = await wallet.sendTransaction(tx);
    this.logger.debug(
      `callAndSendTransaction(): transaction hash = ${transaction.hash}`,
    );
    if (isWait) {
      const receipt = await transaction.wait();
      return receipt;
    }
    return transaction;
  }

  private handleContractException(data: {
    transactionId: number;
    params: string[];
    error: any;
  }) {
    const { transactionId, params, error } = data;

    Utils.logMonitor(
      error,
      `Error when process transaction id ${transactionId} with params ${JSON.stringify(params)}`,
    );
    if (error.cause && error.cause['data']) {
      const logDecode = this.parseMethod(error.cause['data']);
      if (!logDecode) throw error;

      Utils.logMonitor(
        error,
        `Error when process transaction id ${transactionId} has data ${JSON.stringify(logDecode)}`,
      );

      throw {
        message: logDecode.name,
        detail: logDecode,
        params: params,
      };
    }
    error['params'] = params;
    throw error;
  }

  private getNex3ContractAddress() {
    return this.chain.voucherContractAddress;
  }

  signTypedMessage(params: Web3ContractParam[]) {
    const mapJsTypeToAbiType = (type: string): string => {
      switch (type) {
        case 'number':
          return 'uint256';
        case 'bigint':
          return 'uint256';
        case 'string':
          return 'string';
        case 'boolean':
          return 'bool';
        case 'bytes':
          return 'bytes';
        default:
          throw new Error('Unsupported param type: ' + type);
      }
    };
    const paramTypes = params.map((p) => mapJsTypeToAbiType(p.paramType));
    const paramValues = params.map((p) => p.paramValue);

    const abiCoder = new ethers.AbiCoder();
    const encodedParams = abiCoder.encode(paramTypes, paramValues);
    const messageHash = ethers.keccak256(encodedParams);

    const signer = this.getSigner();
    const signature = signer.signMessageSync(ethers.getBytes(messageHash));

    return { messageHash, signature };
  }

  verifySignMessage(
    message: string,
    signature: string,
    address: string,
  ): boolean {
    const addressVerify = ethers.verifyMessage(message, signature);
    return addressVerify === Utils.formatEVMAddress(address);
  }

  async getToken(tokenAddress: string): Promise<Web3Token> {
    const contract = new ethers.Contract(tokenAddress, erc20Abi, this.provider);
    const name = await contract.name();
    const symbol = await contract.symbol();
    const decimals = await contract.decimals();
    return {
      address: tokenAddress,
      name,
      symbol,
      decimals: Utils.toBigNumber(decimals).toNumber(),
    };
  }

  async getTransaction(txId: string): Promise<Web3Transaction> {
    await this.setProvider();

    const transaction = await this.provider.getTransactionReceipt(txId);
    if (!transaction) {
      throw new Error(`Transaction ${txId} has not been mined`);
    }
    return Utils.convertEVMToWeb3Transaction(transaction);
  }

  parseMethod(data: string, abi?: any[]): Web3Event {
    const abiDecoder = require('@kieutuan218/abi-decoder');
    if (abi) {
      abiDecoder.addABI(abi);
    } else {
      abiDecoder.addABI(vaultSponsorContractAbi);
    }
    const decodedData = abiDecoder.decodeMethod(data);
    return decodedData;
  }

  parseLogs(logs: any[], abi: any[]): Web3Event[] {
    const abiDecoder = require('@kieutuan218/abi-decoder');
    abiDecoder.addABI(abi);
    const events = abiDecoder.decodeLogs(logs);
    // this.logger.debug('parseLogs(): Events = ', JSON.stringify(events));
    if (events.length > 0) {
      const lstEvent = [];
      for (let index = 0; index < events.length; index++) {
        const event = events[index];
        if (!event) {
          continue;
        }
        const data = {
          name: event.name,
        };
        for (let index = 0; index < event.events.length; index++) {
          const param = event.events[index];
          data[param.name] = param.value;
        }
        lstEvent.push(data);
      }
      return lstEvent;
    } else {
      throw new Error(`Not found any event`);
    }
  }

  async getBalance(address: string): Promise<BigNumber> {
    await this.setProvider();

    const balance = await this.provider.getBalance(address);
    return new BigNumber(balance.toString());
  }

  async getTokenBalance(asset: Asset, address: string): Promise<BigNumber> {
    await this.setProvider();

    let balance = new BigNumber(0);
    if (asset.isNativeToken) {
      balance = await this.getBalance(address);
    } else {
      const contract = new ethers.Contract(
        asset.contractAddress,
        erc20Abi,
        this.provider,
      );
      balance = await contract.balanceOf(address);
    }
    return new BigNumber(balance.toString());
  }

  getAccount(privateKey: string): Web3Account {
    const account = new ethers.Wallet(privateKey, this.provider);
    return {
      address: account.address,
      privateKey: account.privateKey,
    };
  }

  createAccount(): Web3Account {
    const account = ethers.Wallet.createRandom();
    return {
      address: account.address,
      privateKey: account.privateKey,
    };
  }

  async signTransaction(requestData: Web3CallWriteMethodEVM) {
    const {
      method,
      params,
      contractAddress,
      abi,
      isBatch = false,
      batchCalls,
      userPrivateKey,
    } = requestData;

    let calldata: string;
    let targetContractAddress = contractAddress;
    let hotwallet: HotWallet;
    let gasLimit: string;

    if (isBatch) {
      // Logic batch transaction từ performRedeemVoucherWithNex3Transaction
      const hotwallets = await this.hotWalletRepository.find({
        where: { chainId: this.chain.id },
      });

      if (hotwallets.length === 0) {
        throw new Error('No hotwallet found');
      }

      hotwallet = hotwallets[0];
      const hotWalletAddress = hotwallet.walletAddress;
      // const hotWalletPrivateKey = Utils.decrypt(hotwallet.privateKey);
      // const sponsorWallet = new ethers.Wallet(
      //   hotWalletPrivateKey,
      //   this.provider,
      // );

      // Tạo calldata cho batch transaction
      const adminEoaContract = new ethers.Contract(
        hotWalletAddress,
        gasSponsorContractAbi,
        this.provider,
      );
      calldata = adminEoaContract.interface.encodeFunctionData(
        'execute((bytes,address,uint256)[])',
        [batchCalls],
      );
      targetContractAddress = hotWalletAddress;

      // Estimate gas
      try {
        const estimatedGas = await this.provider.estimateGas({
          from: hotWalletAddress,
          to: targetContractAddress,
          data: calldata,
        });
        gasLimit = Utils.toBigNumber(estimatedGas)
          .multipliedBy(1.5)
          .decimalPlaces(0)
          .toString();
      } catch (err) {
        throw new Error('Estimate gas failed: ' + err.message);
      }
    } else {
      const iface = new ethers.Interface(abi);
      calldata = iface.encodeFunctionData(method, params);
      const hotwalletData = await this.getHotwallet(contractAddress, calldata);
      hotwallet = hotwalletData.hotwallet;
      gasLimit = hotwalletData.gasLimit;
    }

    // Get nonce and gas price
    const signerWallet = new ethers.Wallet(
      Utils.decrypt(hotwallet.privateKey),
      this.provider,
    );
    const nonce = await signerWallet.getNonce();
    const feeData = await this.provider.getFeeData();
    const gasPrice = feeData.gasPrice ?? feeData.maxFeePerGas;

    const tx: ethers.TransactionRequest = {
      type: 0,
      chainId: this.chain.blockchainId,
      from: hotwallet.walletAddress,
      to: targetContractAddress,
      data: calldata,
      gasLimit,
      gasPrice: gasPrice.toString(),
      nonce,
    };

    this.logger.debug(`signTransaction(): tx = ${JSON.stringify(tx)}`);
    const signedTx = await signerWallet.signTransaction(tx);
    const txHash = ethers.keccak256(signedTx);

    return {
      signedTx,
      txHash,
      hotwallet,
    };

    // const iface = new ethers.Interface(abi);
    // const calldata = iface.encodeFunctionData(method, params);

    // // Get hotwallet and gas limit
    // const { gasLimit, hotwallet } = await this.getHotwallet(
    //   contractAddress,
    //   calldata,
    // );

    // // Get nonce and gas price
    // const wallet = new ethers.Wallet(
    //   Utils.decrypt(hotwallet.privateKey),
    //   this.provider,
    // );
    // const nonce = await wallet.getNonce();
    // const feeData = await this.provider.getFeeData();
    // const gasPrice = feeData.gasPrice ?? feeData.maxFeePerGas;

    // const tx: ethers.TransactionRequest = {
    //   type: 0,
    //   chainId: this.chain.blockchainId,
    //   from: hotwallet.walletAddress,
    //   to: contractAddress,
    //   data: calldata,
    //   gasLimit: gasLimit,
    //   gasPrice: gasPrice.toString(),
    //   nonce,
    // };
    // this.logger.debug(`callAndSendTransaction(): tx = ${JSON.stringify(tx)}`);
    // const signedTx = await wallet.signTransaction(tx);
    // const txHash = ethers.keccak256(signedTx);

    // return {
    //   signedTx,
    //   txHash,
    //   hotwallet,
    // };
  }

  async sendSignedTransaction(requestData: Web3SignedTransaction) {
    const { signedTx, txHash, hotwallet } = requestData;

    this.logger.debug(
      `sendSignedTransaction(): signedTx = ${signedTx}, txHash = ${txHash}, hotwallet = ${hotwallet.walletAddress}`,
    );
    const transaction = await this.provider.broadcastTransaction(
      signedTx as string,
    );
    return transaction;
  }

  async callWriteMethod(requestData: Web3CallWriteMethodEVM) {
    const { method, params, contractAddress, abi, isWait } = requestData;
    const iface = new ethers.Interface(abi);

    const data = iface.encodeFunctionData(method, params);
    const transaction = await this.callAndSendTransaction(
      contractAddress,
      data,
      isWait,
    );
    return transaction;
  }

  async callViewMethod(requestData: Web3CallViewMethodEVM): Promise<any> {
    const { method, params, contractAddress, abi } = requestData;
    const contract = new ethers.Contract(contractAddress, abi, this.provider);
    const result = await contract[method](...params);
    return result;
  }

  async getTransactionEvent(txHash: string, assetType: AssetType) {
    const transaction = await this.provider.getTransactionReceipt(txHash);
    if (!transaction) {
      return {
        txHash,
        gasUsed: Utils.toBigNumber(transaction.gasUsed),
        hotWallet: transaction.from,
        status: false,
      };
    }

    console.log('[getTransactionEvent] transaction', transaction);
    const logs = transaction.logs || [];
    let event;
    let events;

    if (assetType === AssetType.NFT_VOUCHER) {
      events = this.parseLogs([...logs], nex3VoucherContractAbi);
    } else {
      events = this.parseLogs([...logs], vaultSponsorContractAbi);
    }

    if (assetType === AssetType.NFT_VOUCHER) {
      event = events.find((event) => event.name === 'VoucherMinted');
    } else if (assetType === AssetType.NFT) {
      event = events.find((event) => event.name === 'NFTGranted');
    } else if (assetType === AssetType.TOKEN) {
      event = events.find((event) => event.name === 'TokenGranted');
    }

    console.log('[getTransactionEvent] event', event);

    return {
      txHash,
      transactionId: event?.transactionId,
      tokenId: event?.tokenId,
      gasUsed: Utils.toBigNumber(transaction.gasUsed),
      hotWallet: transaction.from,
      status: transaction.status === 1,
    };
  }

  async createSignedTransaction(transaction: Transaction) {
    let signedTx: Web3SignedTransaction;
    const [sender, receiver, asset, sponsor] = await Promise.all([
      this.userChainRepository.findOneBy({
        userId: transaction.fromUserId,
        chainId: this.chain.id,
      }),
      this.userChainRepository.findOneBy({
        userId: transaction.toUserId,
        chainId: this.chain.id,
      }),
      this.assetRepository.findOneBy({
        id: transaction.assetId,
      }),
      this.sponsorRepository.findOneBy({
        id: transaction.sponsorId,
      }),
    ]);

    let redeemItem: RedeemItem | null = null;
    if (transaction.type === TransactionType.REDEEM) {
      redeemItem = await this.redeemItemRepository.findOne({
        where: { id: transaction.redeemItemId },
        relations: { asset: true, paidAsset: true },
      });
    }

    this.logger.log(
      `EVM createSignedTransaction(): sender = ${sender.walletAddress}, receiver = ${receiver.walletAddress}, transaction = ${JSON.stringify(transaction)}`,
    );

    const voucherContractInterface = new ethers.Interface(
      nex3VoucherContractAbi,
    );
    const vaultSponsorContractInterface = new ethers.Interface(
      vaultSponsorContractAbi,
    );

    if (transaction.type === TransactionType.REDEEM) {
      if (redeemItem && redeemItem.paidType !== PaidType.NEX3_XP) {
        let transferRedeemedAssetCallData: any;

        if (redeemItem.asset.type === AssetType.NFT_VOUCHER) {
          transferRedeemedAssetCallData = {
            data: voucherContractInterface.encodeFunctionData(
              'mint(address,uint256)',
              [sender.walletAddress, transaction.id],
            ),
            to: this.chain.voucherContractAddress,
            value: ethers.parseEther('0'),
          };
        } else if (redeemItem.asset.type === AssetType.NFT) {
          const nft = await this.userAssetRepository.findOne({
            where: {
              redeemItemId: transaction.redeemItemId,
              id: transaction.nftId,
            },
            relations: { asset: true },
          });

          transferRedeemedAssetCallData = {
            data: vaultSponsorContractInterface.encodeFunctionData(
              'grantNFTForUser(address,address,uint256,uint256)',
              [
                sender.walletAddress,
                nft.asset.contractAddress,
                new BigNumber(nft.tokenId).toNumber(),
                transaction.id,
              ],
            ),
            to: sponsor.sponsorVaultAddress,
            value: ethers.parseEther('0'),
          };
        } else if (redeemItem.asset.type === AssetType.TOKEN) {
          const amount = Utils.toBigNumber(transaction.redeemedAmount)
            .multipliedBy(10 ** redeemItem.asset.decimals)
            .toString();
          transferRedeemedAssetCallData = {
            data: vaultSponsorContractInterface.encodeFunctionData(
              'grantTokenForUser(address,address,uint256,uint256)',
              [
                sender.walletAddress,
                redeemItem.asset.contractAddress,
                amount,
                transaction.id,
              ],
            ),
            to: sponsor.sponsorVaultAddress,
            value: ethers.parseEther('0'),
          };
        }

        const { hotWalletAddress, transferPaidTokenCallData, userPrivateKey } =
          await this.prepareRedeemTransactionByToken(
            transaction,
            sender,
            redeemItem.paidAsset.contractAddress,
          );

        // Truyền vào signTransaction với batchCalls
        signedTx = await this.signTransaction({
          method: 'execute',
          params: [[transferPaidTokenCallData, transferRedeemedAssetCallData]],
          contractAddress: hotWalletAddress,
          abi: gasSponsorContractAbi,
          isBatch: true,
          batchCalls: [
            transferPaidTokenCallData,
            transferRedeemedAssetCallData,
          ],
          userPrivateKey,
        });
      } else if (redeemItem && redeemItem.paidType === PaidType.NEX3_XP) {
        if (asset.type === AssetType.NFT_VOUCHER) {
          signedTx = await this.signTransaction({
            method: 'mint',
            params: [sender.walletAddress, transaction.id],
            contractAddress: this.getNex3ContractAddress(),
            abi: nex3VoucherContractAbi,
          });
        } else if (asset.type === AssetType.NFT) {
          const nft = await this.userAssetRepository.findOne({
            where: {
              redeemItemId: transaction.redeemItemId,
              id: transaction.nftId,
            },
            relations: {
              asset: true,
            },
          });

          signedTx = await this.signTransaction({
            method: 'grantNFTForUser',
            params: [
              sender.walletAddress,
              nft.asset.contractAddress,
              new BigNumber(nft.tokenId).toNumber(),
              transaction.id,
            ],
            contractAddress: sponsor.sponsorVaultAddress,
            abi: vaultSponsorContractAbi,
          });
        } else if (asset.type === AssetType.TOKEN) {
          const amount = Utils.toBigNumber(transaction.redeemedAmount)
            .multipliedBy(10 ** asset.decimals)
            .toString();
          signedTx = await this.signTransaction({
            method: 'grantTokenForUser',
            params: [
              sender.walletAddress,
              asset.contractAddress,
              amount,
              transaction.id,
            ],
            contractAddress: sponsor.sponsorVaultAddress,
            abi: vaultSponsorContractAbi,
          });
        }
      }
    } else if (transaction.type === TransactionType.TRANSFER) {
      const erc20ContractInterface = new ethers.Interface(erc20Abi);
      const erc721ContractInterface = new ethers.Interface(erc721Abi);
      let transferAssetCallData: any;

      // Transaction 1: Transfer paid token
      const {
        hotWalletAddress,
        transferPaidTokenCallData,
        userPrivateKey,
        nonceToUse,
      } = await this.prepareRedeemTransactionByToken(
        transaction,
        sender,
        asset.type === AssetType.TOKEN
          ? asset.contractAddress
          : process.env.TOKEN_ADDRESS,
      );

      if (asset.type === AssetType.TOKEN) {
        const amount = ethers.parseEther(transaction.point);
        const nonceToUseTransferToken = nonceToUse + BigInt(1);
        const callDataTransferToken = {
          data: erc20ContractInterface.encodeFunctionData(
            'transfer(address,uint256)',
            [receiver.walletAddress, amount],
          ),
          to: asset.contractAddress,
          value: ethers.parseEther('0'),
        };
        const signatureTransferToken = await this.signSponsorshipMessage(
          callDataTransferToken,
          hotWalletAddress,
          Number(nonceToUseTransferToken),
          userPrivateKey,
          Utils.toBigNumber(this.chain.blockchainId).toNumber(),
        );
        const eoaContract = new ethers.Contract(
          sender.walletAddress,
          gasSponsorContractAbi,
          this.provider,
        );
        transferAssetCallData = {
          data: eoaContract.interface.encodeFunctionData(
            'execute((bytes,address,uint256),address,address,uint256,bytes)',
            [
              callDataTransferToken,
              hotWalletAddress,
              asset.contractAddress,
              '0',
              signatureTransferToken,
            ],
          ),
          to: sender.walletAddress,
          value: ethers.parseEther('0'),
        };
      } else {
        const nft = await this.userAssetRepository.findOneBy({
          id: transaction.nftId,
        });

        const nonceToUseTransferNft = nonceToUse + BigInt(1);
        const callDataTransferNft = {
          data: erc721ContractInterface.encodeFunctionData(
            'safeTransferFrom(address,address,uint256)',
            [sender.walletAddress, receiver.walletAddress, nft.tokenId],
          ),
          to: asset.contractAddress,
          value: ethers.parseEther('0'),
        };
        const signatureTransferNft = await this.signSponsorshipMessage(
          callDataTransferNft,
          hotWalletAddress,
          Number(nonceToUseTransferNft),
          userPrivateKey,
          Utils.toBigNumber(this.chain.blockchainId).toNumber(),
        );
        const eoaContract = new ethers.Contract(
          sender.walletAddress,
          gasSponsorContractAbi,
          this.provider,
        );
        transferAssetCallData = {
          data: eoaContract.interface.encodeFunctionData(
            'execute((bytes,address,uint256),address,address,uint256,bytes)',
            [
              callDataTransferNft,
              hotWalletAddress,
              process.env.TOKEN_ADDRESS, // contract token muốn chuyển đi
              '0',
              signatureTransferNft,
            ],
          ),
          to: sender.walletAddress,
          value: ethers.parseEther('0'),
        };
      }

      signedTx = await this.signTransaction({
        method: 'execute',
        params: [[transferPaidTokenCallData, transferAssetCallData]],
        contractAddress: hotWalletAddress,
        abi: gasSponsorContractAbi,
        isBatch: true,
        batchCalls: [transferPaidTokenCallData, transferAssetCallData],
        userPrivateKey,
      });
    }

    return signedTx;
  }

  private async prepareRedeemTransactionByToken(
    transaction: Transaction,
    sender: UserChain,
    contractAddress: string,
  ) {
    const userPrivateKey = Utils.decrypt(sender.privateKey);
    const hotwallets = await this.hotWalletRepository.find({
      where: { chainId: this.chain.id },
    });
    if (hotwallets.length === 0) {
      throw new Error('No hotwallet found');
    }
    const hotWalletAddress = hotwallets[0].walletAddress;

    // Lấy nonce từ nonceTrackerContract
    const nonceTrackerContractAddress = this.chain.nonceTrackerContractAddress;
    const nonceTrackerContract = new ethers.Contract(
      nonceTrackerContractAddress,
      nonceTrackerContractAbi,
      this.provider,
    );
    const nextNonce = await nonceTrackerContract.getNextNonce(
      sender.walletAddress,
    );
    const nonceToUse = BigInt(nextNonce);

    // Chuẩn bị callData cho transaction 1 (transfer paid token)
    const callData = {
      data: '0x', //encode function transfer {to: receiver address, amount: transaction.point} // NFT: encode function safeTransferFrom {from: sender address, to: receiver address, tokenId: transaction.nftId}
      to: hotWalletAddress, // function transfer: address token // NFT: address NFT
      value: ethers.parseEther('0'),
    };
    const signature = await this.signSponsorshipMessage(
      callData,
      hotWalletAddress,
      Number(nonceToUse),
      userPrivateKey,
      Utils.toBigNumber(this.chain.blockchainId).toNumber(),
    );

    const eoaContract = new ethers.Contract(
      sender.walletAddress,
      gasSponsorContractAbi,
      this.provider,
    );
    const transferPaidTokenCallData = {
      data: eoaContract.interface.encodeFunctionData(
        'execute((bytes,address,uint256),address,address,uint256,bytes)',
        [
          callData,
          hotWalletAddress,
          contractAddress,
          transaction.type === TransactionType.REDEEM
            ? ethers.parseEther(transaction.point)
            : ethers.parseEther('0'),
          signature,
        ],
      ),
      to: sender.walletAddress,
      value: ethers.parseEther('0'),
    };

    return {
      hotWalletAddress,
      transferPaidTokenCallData,
      userPrivateKey,
      nonceToUse,
    };
  }

  // EIP 7702
  async checkDelegation(
    eoaAddress: string,
  ): Promise<{ isDelegated: boolean; delegatedTo?: string }> {
    const code = await this.provider.getCode(eoaAddress);
    if (!code || code === '0x' || code.length === 0) {
      return { isDelegated: false };
    }
    if (code.toLowerCase().startsWith('0xef0100')) {
      // Extract the delegated address (remove 0xef0100 prefix)
      // The delegation designator format: 0xef0100 + contract_address (20 bytes)
      const delegatedAddress = '0x' + code.slice(8, 48); // Extract 20 bytes after 0xef0100
      return { isDelegated: true, delegatedTo: delegatedAddress };
    }
    return { isDelegated: false };
  }

  async signSponsorshipMessage(
    call: { data: string; to: string; value: bigint },
    sponsor: string,
    nonce: number,
    privateKey: string,
    chainId: number,
  ): Promise<string> {
    // TODO: if privateKey is encrypted, we need to decrypt it first
    const wallet = new ethers.Wallet(privateKey);

    const packedData = ethers.concat([
      ethers.toBeHex(chainId, 32), // uint256 chainId (32 bytes)
      ethers.getAddress(call.to), // address to (20 bytes, no padding)
      ethers.toBeHex(call.value, 32), // uint256 value (32 bytes)
      ethers.keccak256(call.data), // bytes32 keccak256(data) (32 bytes)
      ethers.getAddress(sponsor), // address sponsor (20 bytes, no padding)
      ethers.toBeHex(nonce, 32), // uint256 nonce (32 bytes)
    ]);

    const digest = ethers.keccak256(packedData);

    // Sign with Ethereum message prefix (matching contract's toEthSignedMessageHash)
    const signature = await wallet.signMessage(ethers.getBytes(digest));
    return signature;
  }

  // admin use to delegate admin hotwallet to gas sponsor contract
  async selfDelegate() {
    // TODO: get this private key from database (NOTE: SHOULD BE START WITH 0x)
    const adminPrivateKey = '0xabc';
    const hotWallet = privateKeyToAccount(adminPrivateKey);
    const wallet: any = createWalletClient({
      account: hotWallet,
      // TODO: switch chain base on chainId
      chain: baseSepolia,
      transport: http(),
    });

    // TODO: get this address from database
    const nex3GasSponsorContract = '0xabc';
    const authorization = await wallet.signAuthorization({
      contractAddress: nex3GasSponsorContract,
      executor: 'self',
    });

    const hash = await wallet.sendTransaction({
      authorizationList: [authorization],
      data: '0x',
      to: wallet.account.address,
    });
  }

  async setUpDelegation(eoaAddress: string, privateKey: string, chain: Chain) {
    const currentDelegation = await this.checkDelegation(eoaAddress);
    // TODO: get this address from database
    const GAS_SPONSOR_CONTRACT_ADDRESS = chain.gasSponsorContractAddress;
    if (
      currentDelegation.isDelegated &&
      currentDelegation.delegatedTo?.toLowerCase() ==
        GAS_SPONSOR_CONTRACT_ADDRESS?.toLowerCase()
    ) {
      return true;
    } else {
      // set up new delegation
      // TODO: if privateKey is encrypted, we need to decrypt it first
      const userWallet = new ethers.Wallet(privateKey, this.provider);
      // TODO: get this private key and public key from database
      const hotwallets = await this.hotWalletRepository.find({
        where: {
          chainId: this.chain.id,
        },
      });

      if (hotwallets.length === 0) {
        throw new Error('No hotwallet found');
      }

      const hotWalletAddress = hotwallets[0].walletAddress;
      const hotWalletPrivateKey = Utils.decrypt(hotwallets[0].privateKey);
      const sponsorWallet = new ethers.Wallet(
        hotWalletPrivateKey,
        this.provider,
      );

      const eoaNonce = await this.provider.getTransactionCount(eoaAddress);
      const sponsorNonce =
        await this.provider.getTransactionCount(hotWalletAddress);

      const authorization = await userWallet.authorize({
        address: GAS_SPONSOR_CONTRACT_ADDRESS,
        nonce: eoaNonce,
        chainId: chain.blockchainId,
      });

      const feeData = await this.provider.getFeeData();

      // send EIP-7702 transaction to establish delegation
      const delegationTx = {
        type: 4,
        chainId: chain.blockchainId,
        nonce: sponsorNonce,
        maxPriorityFeePerGas:
          feeData.maxPriorityFeePerGas || ethers.parseUnits('1', 'gwei'),
        maxFeePerGas: feeData.maxFeePerGas || ethers.parseUnits('20', 'gwei'),
        gasLimit: 300000,
        to: eoaAddress, // Send to EOA to establish delegation
        value: 0,
        data: '0x',
        authorizationList: [authorization],
      };
      const tx = await sponsorWallet.sendTransaction(delegationTx);
      const receipt = await tx.wait();
      if (receipt.status !== 1) {
        throw new Error('Delegation transaction failed');
      }
      return true;
    }
  }

  async performRedeemVoucherWithNex3Transaction(
    userAddress: string,
    userPrivateKey: string,
    transaction: Transaction,
    redeemItem: RedeemItem,
  ) {
    // TODO: get this private key and public key from database
    const hotwallets = await this.hotWalletRepository.find({
      where: {
        chainId: this.chain.id,
      },
    });

    if (hotwallets.length === 0) {
      throw new Error('No hotwallet found');
    }

    const hotWalletAddress = hotwallets[0].walletAddress;
    const hotWalletPrivateKey = Utils.decrypt(hotwallets[0].privateKey);
    const sponsorWallet = new ethers.Wallet(hotWalletPrivateKey, this.provider);

    // TODO: get this address from database
    // const nex3ContractAddress = '0xabc';

    const eoaContract = new ethers.Contract(
      userAddress,
      gasSponsorContractAbi,
      this.provider,
    );

    // TODO: get nonce tracker address from database
    const nonceTrackerContractAddress = this.chain.nonceTrackerContractAddress;
    const nonceTrackerContract = new ethers.Contract(
      nonceTrackerContractAddress,
      nonceTrackerContractAbi,
      this.provider,
    );
    const nextNonce = await nonceTrackerContract.getNextNonce(userAddress);

    const nonceToUse = BigInt(nextNonce);

    // TODO: decode userPrivateKey if it is encrypted
    const callData = {
      data: '0x',
      to: hotWalletAddress,
      value: ethers.parseEther('0'),
    };
    const signature = await this.signSponsorshipMessage(
      callData,
      hotWalletAddress,
      Number(nonceToUse),
      userPrivateKey,
      Utils.toBigNumber(this.chain.blockchainId).toNumber(),
    );

    // Transaction 1: Transfer NEX3 to hot wallet
    const transferNex3CallData = {
      data: eoaContract.interface.encodeFunctionData(
        'execute((bytes,address,uint256),address,address,uint256,bytes)',
        [
          callData,
          hotWalletAddress,
          redeemItem.paidAsset.contractAddress, // contract paid token
          ethers.parseEther(transaction.point),
          signature,
        ],
      ),
      to: userAddress,
      value: ethers.parseEther('0'),
    };

    // Transaction 2: Transfer NFT to user
    const voucherContractInterface = new ethers.Interface(
      nex3VoucherContractAbi,
    );
    // TODO: get this address from database
    const voucherContractAddress = this.chain.voucherContractAddress; // contract voucher redeem token to nft voucher

    const vaultSponsorContract = this.getNex3ContractAddress(); // vault sponsor redeem token to nft or token

    let transferNftCallData: any;

    if (redeemItem.asset.type === AssetType.NFT_VOUCHER) {
      transferNftCallData = {
        data: voucherContractInterface.encodeFunctionData(
          'mint(address,uint256)',
          [userAddress, transaction.id],
        ),
        to: voucherContractAddress, // change vault | contract voucher
        value: ethers.parseEther('0'),
      };
    } else if (redeemItem.asset.type === AssetType.NFT) {
      const nft = await this.userAssetRepository.findOne({
        where: {
          redeemItemId: transaction.redeemItemId,
          id: transaction.nftId,
        },
        relations: {
          asset: true,
        },
      });

      transferNftCallData = {
        data: voucherContractInterface.encodeFunctionData(
          'grantNFTForUser(address,address,uint256,uint256)',
          [
            userAddress,
            nft.asset.contractAddress,
            new BigNumber(nft.tokenId).toNumber(),
            transaction.id,
          ],
        ),
        to: vaultSponsorContract, // change vault | contract voucher
        value: ethers.parseEther('0'),
      };
    } else if (redeemItem.asset.type === AssetType.TOKEN) {
      const amount = ethers.parseEther(transaction.redeemedAmount);

      // Utils.toBigNumber(transaction.redeemedAmount)
      //   .multipliedBy(10 ** redeemItem.asset.decimals)
      //   .toString();

      transferNftCallData = {
        data: voucherContractInterface.encodeFunctionData(
          'grantTokenForUser(address,address,uint256,uint256)',
          [
            userAddress,
            redeemItem.asset.contractAddress,
            amount,
            transaction.id,
          ],
        ),
        to: vaultSponsorContract, // change vault | contract voucher
        value: ethers.parseEther('0'),
      };
    }

    // batch transaction 1 and transaction 2
    const adminEoaContract = new ethers.Contract(
      hotWalletAddress,
      gasSponsorContractAbi,
      this.provider,
    );

    const executeData = {
      data: adminEoaContract.interface.encodeFunctionData(
        'execute((bytes,address,uint256)[])',
        [[transferNex3CallData, transferNftCallData]],
      ),
    };

    const sponsorNonce =
      await this.provider.getTransactionCount(hotWalletAddress);
    const feeData = await this.provider.getFeeData();

    const sponsorTx = {
      type: 0, // regular transaction
      chainId: Utils.toBigNumber(this.chain.blockchainId).toNumber(),
      nonce: sponsorNonce,
      gasPrice: feeData.gasPrice || ethers.parseUnits('10', 'gwei'),
      gasLimit: 500000,
      to: hotWalletAddress,
      from: hotWalletAddress, // Call execute on the delegated EOA
      // value: 0,
      data: executeData.data,
    };

    try {
      const tx = await sponsorWallet.sendTransaction(sponsorTx);
      const receipt = await tx.wait();
      if (receipt.status !== 1) {
        throw new Error('Transaction failed');
      }
    } catch (error) {
      throw new Error('Transaction failed: ' + error.message);
    }
  }
}
