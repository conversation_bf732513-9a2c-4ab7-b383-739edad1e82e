import { Injectable, Logger } from '@nestjs/common';
import BigNumber from 'bignumber.js';
import { Utils } from 'src/common/utils';
import { Chain, ChainType } from 'src/entities/chain.entity';
import {
  TransactionEvent,
  Web3Account,
  Web3CallViewMethodAptos,
  Web3CallViewMethodEVM,
  Web3CallWriteMethodAptos,
  Web3CallWriteMethodEVM,
  Web3ContractParam,
  Web3Event,
  Web3Service,
  Web3Signature,
  Web3SignedTransaction,
  Web3Token,
  Web3Transaction,
} from '../web3.interface';
import * as erc20Abi from './erc20-abi.json';
import * as erc721Abi from './erc721-abi.json';
import * as gasSponsorContractAbi from './nex3-gas-sponsor-abi.json';
import * as nonceTrackerContractAbi from './nex3-nonce-tracker-abi.json';
import * as nex3VoucherContractAbi from './nex3-voucher-abi.json';
import * as vaultSponsorContractAbi from './vault-sponsor-contract-abi.json';

import { InjectRepository } from '@nestjs/typeorm';
import { ethers } from 'ethers';
import { Asset, AssetType } from 'src/entities/asset.entity';
import { HotWallet } from 'src/entities/hot-wallet.entity';
import { PaidType, RedeemItem } from 'src/entities/redeem-item.entity';
import { Sponsor } from 'src/entities/sponsor.entity';
import { Transaction, TransactionType } from 'src/entities/transaction.entity';
import { UserAsset } from 'src/entities/user-asset.entity';
import { UserChain } from 'src/entities/user-chain.entity';
import { DataSource, Repository } from 'typeorm';
import { createWalletClient, http } from 'viem';
import { privateKeyToAccount } from 'viem/accounts';
import { baseSepolia } from 'viem/chains';
import { Aptos, AptosConfig, Network } from '@aptos-labs/ts-sdk';

@Injectable()
export class Web3AptosService implements Web3Service {
  private readonly logger = new Logger(Web3AptosService.name);
  private client: Aptos;

  constructor(
    private dataSource: DataSource,
    @InjectRepository(HotWallet)
    private hotWalletRepository: Repository<HotWallet>,
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
    @InjectRepository(RedeemItem)
    private redeemItemRepository: Repository<RedeemItem>,
    @InjectRepository(Asset)
    private assetRepository: Repository<Asset>,
    @InjectRepository(UserChain)
    private userChainRepository: Repository<UserChain>,
    @InjectRepository(UserAsset)
    private userAssetRepository: Repository<UserAsset>,
    @InjectRepository(Sponsor)
    private sponsorRepository: Repository<Sponsor>,
  ) {}
  signTypedMessage(params: Web3ContractParam[]): Web3Signature {
    throw new Error('Method not implemented.');
  }
  verifySignMessage(
    originalMessage: string,
    signature: string,
    address: string,
  ): boolean {
    throw new Error('Method not implemented.');
  }

  parseLogs(logs: any[], abi: any[]): Web3Event[] {
    throw new Error('Method not implemented.');
  }
  getTokenBalance(asset: Asset, address: string): Promise<BigNumber> {
    throw new Error('Method not implemented.');
  }
  getAccount(privateKey: string): Web3Account {
    throw new Error('Method not implemented.');
  }
  createAccount(): Web3Account {
    throw new Error('Method not implemented.');
  }
  signTransaction(
    requestData: Web3CallWriteMethodEVM | Web3CallWriteMethodAptos,
  ): Promise<Web3SignedTransaction> {
    throw new Error('Method not implemented.');
  }
  sendSignedTransaction(requestData: Web3SignedTransaction): Promise<any> {
    throw new Error('Method not implemented.');
  }
  callWriteMethod(
    requestData: Web3CallWriteMethodEVM | Web3CallWriteMethodAptos,
  ): Promise<any> {
    throw new Error('Method not implemented.');
  }
  callViewMethod(
    requestData: Web3CallViewMethodEVM | Web3CallViewMethodAptos,
  ): Promise<any> {
    throw new Error('Method not implemented.');
  }
  getTransactionEvent(
    txHash: string,
    assetType: AssetType,
  ): Promise<TransactionEvent> {
    throw new Error('Method not implemented.');
  }
  createSignedTransaction(
    transaction: Transaction,
    redeemItem?: RedeemItem,
  ): Promise<Web3SignedTransaction> {
    throw new Error('Method not implemented.');
  }
  setUpDelegation(
    eoaAddress: string,
    privateKey: string,
    chain: Chain,
  ): Promise<boolean> {
    throw new Error('Method not implemented.');
  }
  performRedeemVoucherWithNex3Transaction(
    userAddress: string,
    userPrivateKey: string,
    transaction: Transaction,
    redeemItem: RedeemItem,
  ): Promise<any> {
    throw new Error('Method not implemented.');
  }

  chain: Chain;

  init(chain: Chain) {
    this.chain = chain;
    this.setProvider();
  }

  getChainType(): ChainType {
    return ChainType.MOVEVM;
  }

  setProvider() {
    const rpc = Utils.getRandom(this.chain.rpcs.split(',')).trim();
    this.logger.log(`Network ${this.chain.chainName}. RPC: ${rpc}`);
    const config = new AptosConfig({ network: Network.TESTNET, fullnode: rpc });
    this.client = new Aptos(config);
  }

  async getBalance(address: string): Promise<BigNumber> {
    if (!this.client) {
      this.setProvider();
    }

    const balance = await this.client.getAccountAPTAmount({
      accountAddress: address,
    });
    return new BigNumber(balance);
  }

  async getToken(tokenAddress: string): Promise<Web3Token> {
    const metadata = await this.client.getAccountCoinsData({
      accountAddress: tokenAddress,
    });
    return {
      address: metadata[0].owner_address,
      name: metadata[0].metadata.name,
      symbol: metadata[0].metadata.symbol,
      decimals: Number(metadata[0].amount),
    };
  }

  async getTransaction(txId: string): Promise<Web3Transaction> {
    if (!this.client) {
      this.setProvider();
    }

    const tx = await this.client.getTransactionByHash({
      transactionHash: txId,
    });

    if (tx.type !== 'user_transaction') {
      throw new Error(`Transaction ${txId} has not been mined`);
    }

    const args = (tx.payload as any).arguments;
    const toAddress = args[0];

    const contractAddress =
      (tx.payload as any).type === 'entry_function_payload'
        ? (tx.payload as any).function.split('::')[0]
        : null;

    const gasUsed = new BigNumber(tx.gas_used);
    const gasPrice = new BigNumber(tx.gas_unit_price);
    const totalFee = gasUsed.multipliedBy(gasPrice);

    return {
      txHash: tx.hash,
      from: tx.sender,
      to: toAddress,
      status: tx.success,
      gasPrice: gasPrice,
      gasUsed: gasUsed,
      fee: totalFee,
      payload: JSON.stringify(tx.payload),
      logs: tx.events,
      contractAddress: contractAddress || tx.sender || '',
    };
  }
}
