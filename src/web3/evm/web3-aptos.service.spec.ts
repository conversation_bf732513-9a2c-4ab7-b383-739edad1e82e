import BigNumber from 'bignumber.js';
import { Web3AptosService } from './web3-aptos.service';

describe('Web3AptosService direct test', () => {
  let service: Web3AptosService;

  beforeAll(() => {
    service = new Web3AptosService(
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
    );

    service.init({
      chainName: 'Aptos Testnet',
      rpcs: 'https://api.testnet.staging.aptoslabs.com/v1',
    } as any);
  });

  it('should return balance as BigNumber', async () => {
    const address =
      '0x3077f653bc19ae800b498e4fea6750296a7458b46efed6eacf01b250f881c9df';
    const balance = await service.getBalance(address);

    expect(balance).toBeInstanceOf(BigNumber);
    console.log('APT Balance:', balance.dividedBy(1e8).toString());
  });

  it('should return token metadata from resource', async () => {
    const test1 = await service.getToken(
      '0x3077f653bc19ae800b498e4fea6750296a7458b46efed6eacf01b250f881c9df',
    );

    expect(test1).toHaveProperty('address', test1.address);
    expect(typeof test1.name).toBe('string');
    expect(typeof test1.symbol).toBe('string');
    expect(typeof test1.decimals).toBe('number');

    console.log('Token metadata:', test1);
  });

  it('should return standardized transaction data from a hash', async () => {
    const knownTestnetHash =
      '0x6d19445afa7b679a3a625552ab31fc4bd11989eeddcb6b3f9e86754b96c5a64d';

    const tx = await service.getTransaction(knownTestnetHash);

    console.log('Standardized Transaction:', tx);
  }, 30000);
});
