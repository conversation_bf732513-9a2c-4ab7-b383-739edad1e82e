[{"inputs": [{"internalType": "address", "name": "nonceTracker", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "ECDSAInvalidSignature", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "length", "type": "uint256"}], "name": "ECDSAInvalidSignatureLength", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "ECDSAInvalidSignatureS", "type": "error"}, {"inputs": [], "name": "Nex3GasSponsor__ExternalCallFailed", "type": "error"}, {"inputs": [], "name": "Nex3GasSponsor__InsufficientETHBalance", "type": "error"}, {"inputs": [], "name": "Nex3GasSponsor__InsufficientNex3TokenBalance", "type": "error"}, {"inputs": [], "name": "Nex3GasSponsor__InvalidSigner", "type": "error"}, {"inputs": [], "name": "Nex3GasSponsor__Nex3TokenTransferFailed", "type": "error"}, {"inputs": [], "name": "Nex3GasSponsor__NotAuthorized", "type": "error"}, {"inputs": [], "name": "Nex3GasSponsor__RefundedFailed", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "eoa", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sponsor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "nonce", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "callTo", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "callValue", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "erc20Token", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "erc20TokenPaid", "type": "uint256"}], "name": "TransactionSponsored", "type": "event"}, {"inputs": [{"components": [{"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "internalType": "struct Nex3GasSponsor.Call", "name": "userCall", "type": "tuple"}, {"internalType": "address", "name": "sponsor", "type": "address"}, {"internalType": "address", "name": "erc20Token", "type": "address"}, {"internalType": "uint256", "name": "erc20Fee", "type": "uint256"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "name": "execute", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"components": [{"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "internalType": "struct Nex3GasSponsor.Call[]", "name": "calls", "type": "tuple[]"}], "name": "execute", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "erc20Token", "type": "address"}], "name": "getEOAERC20TokenBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getEOAETHBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "i_nonceTracker", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"stateMutability": "payable", "type": "receive"}]