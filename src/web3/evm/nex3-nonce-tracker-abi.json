[{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "nonce", "type": "uint256"}], "name": "NonceUsed", "type": "event"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "getNextNonce", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "nonces", "outputs": [{"internalType": "uint256", "name": "nonce", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "useNonce", "outputs": [{"internalType": "uint256", "name": "nonce", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}]