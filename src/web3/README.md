# Web3 Multi-Chain Integration

Hệ thống Web3 hỗ trợ cả EVM và Aptos chains với factory pattern, đảm bảo code clean, maintainable và reusable.

## 🏗️ <PERSON><PERSON>n trúc

```
src/web3/
├── web3.interface.ts          # Common interface cho tất cả chains
├── web3.factory.service.ts    # Factory pattern để tạo service theo chain type
├── web3.module.ts            # NestJS module configuration
├── evm/
│   ├── web3-evm.service.ts   # EVM implementation (Ethereum, BSC, Base, etc.)
│   └── *.json                # EVM ABI files
├── aptos/
│   ├── web3-aptos.service.ts # Aptos implementation
│   └── *.json                # Aptos module definitions
└── web3-integration-example.ts # Usage examples
```

## 🚀 Cách sử dụng

### 1. Inject Web3ServiceFactory

```typescript
import { Injectable } from '@nestjs/common';
import { Web3ServiceFactory } from './web3/web3.factory.service';
import { Chain, ChainType } from './entities/chain.entity';

@Injectable()
export class YourService {
  constructor(private readonly web3ServiceFactory: Web3ServiceFactory) {}

  async processTransaction(chain: Chain) {
    // Factory tự động chọn service phù hợp dựa trên chain.chainType
    const web3Service = this.web3ServiceFactory.get(chain);

    // Sử dụng unified interface cho tất cả chains
    const balance = await web3Service.getBalance(address);
    const account = web3Service.createAccount();
    const transaction = await web3Service.getTransaction(txHash);
  }
}
```

### 2. Cấu hình Chain

#### EVM Chain:

```typescript
const evmChain: Chain = {
  chainName: 'ethereum',
  chainType: ChainType.EVM,
  blockchainId: '1',
  rpcs: 'https://eth-mainnet.alchemyapi.io/v2/your-api-key',
  voucherContractAddress: '0x1234...',
  gasSponsorContractAddress: '0x5678...',
  // ... other EVM-specific fields
};
```

#### Aptos Chain:

```typescript
const aptosChain: Chain = {
  chainName: 'aptos',
  chainType: ChainType.APTOS,
  blockchainId: '1',
  rpcs: 'https://fullnode.mainnet.aptoslabs.com/v1',
  voucherContractAddress: '0x1::nex3_voucher',
  gasSponsorContractAddress: '0x1::gas_sponsor',
  // ... other Aptos-specific fields
};
```

### 3. Unified Interface Methods

Tất cả chains đều implement cùng interface:

```typescript
interface Web3Service {
  // Account management
  createAccount(): Web3Account;
  getAccount(privateKey: string): Web3Account;

  // Balance operations
  getBalance(address: string): Promise<BigNumber>;
  getTokenBalance(asset: Asset, address: string): Promise<BigNumber>;

  // Transaction operations
  getTransaction(txId: string): Promise<Web3Transaction>;
  signTransaction(requestData: any): Promise<Web3SignedTransaction>;
  sendSignedTransaction(requestData: Web3SignedTransaction): Promise<any>;

  // Contract interactions
  callWriteMethod(requestData: any): Promise<any>;
  callViewMethod(requestData: any): Promise<any>;

  // Event handling
  getTransactionEvent(
    txHash: string,
    assetType: AssetType,
  ): Promise<TransactionEvent>;
  parseLogs(logs: any[], abi: any[]): Web3Event[];

  // Chain-specific
  getChainType(): ChainType;
  init(chain: Chain): void;
}
```

## 🔧 Chain-Specific Implementations

### EVM Implementation

- Sử dụng **ethers.js** v6
- Hỗ trợ EIP-7702 delegation
- Gas estimation và optimization
- Multi-signature transactions
- Batch transactions
- Event parsing với ABI

### Aptos Implementation

- Sử dụng **@aptos-labs/ts-sdk** v1.28.0
- Move VM smart contract interactions
- Ed25519 key management
- Sponsored transactions
- Event parsing cho Move events
- Type-safe function calls

## 📦 Dependencies

```json
{
  "dependencies": {
    "@aptos-labs/ts-sdk": "^1.28.0",
    "ethers": "^6.15.0",
    "viem": "^2.33.3",
    "bignumber.js": "^9.1.2"
  }
}
```

## 🎯 Key Features

### 1. **Factory Pattern**

- Tự động chọn implementation dựa trên chain type
- Dễ dàng thêm chains mới
- Centralized service management

### 2. **Unified Interface**

- Cùng một API cho tất cả chains
- Type-safe với TypeScript
- Consistent error handling

### 3. **Clean Architecture**

- Separation of concerns
- Dependency injection
- Testable code structure

### 4. **Production Ready**

- Error handling và logging
- Gas optimization
- Transaction retry logic
- Hot wallet management

## 🧪 Testing

Sử dụng `web3-integration-example.ts` để test:

```typescript
import { Web3IntegrationExample } from './web3-integration-example';

// Test all features
await integrationExample.runAllDemonstrations();

// Test specific features
await integrationExample.demonstrateGetBalance();
await integrationExample.demonstrateAccountCreation();
```

## 🔒 Security Considerations

1. **Private Key Management**: Tất cả private keys được encrypt
2. **Hot Wallet Rotation**: Tự động chọn available hot wallets
3. **Gas Limit Protection**: Buffer 50% cho gas estimation
4. **Transaction Validation**: Validate trước khi sign
5. **Network Isolation**: Separate RPC endpoints

## 🚀 Extending for New Chains

Để thêm chain mới (ví dụ: Solana):

1. Tạo `src/web3/solana/web3-solana.service.ts`
2. Implement `Web3Service` interface
3. Thêm `ChainType.SOLANA` vào enum
4. Update factory pattern trong `web3.factory.service.ts`
5. Add to `Web3Module` providers

## 📝 Best Practices

1. **Always use factory pattern** thay vì direct service injection
2. **Handle errors gracefully** với proper logging
3. **Use typed parameters** cho contract calls
4. **Implement retry logic** cho network calls
5. **Cache frequently used data** như token info
6. **Monitor gas prices** và adjust accordingly
7. **Test on testnets** trước khi deploy production

## 🔄 Migration từ single-chain

```typescript
// Before (EVM only)
const evmService = new Web3EVMService();
await evmService.getBalance(address);

// After (Multi-chain)
const web3Service = this.web3ServiceFactory.get(chain);
await web3Service.getBalance(address);
```

Với architecture này, bạn có thể dễ dàng scale và maintain code khi thêm nhiều chains mới!

