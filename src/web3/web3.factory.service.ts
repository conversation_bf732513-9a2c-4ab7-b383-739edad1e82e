import { Injectable } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { Chain, ChainType } from 'src/entities/chain.entity';
import { Web3EVMService } from './evm/web3-evm.service';
// import { Web3AptosService } from './evm/web3-aptos.service';
import { Web3AptosService } from './aptos/web3-aptos.service';

@Injectable()
export class Web3ServiceFactory {
  constructor(private readonly moduleRef: ModuleRef) {}

  get(chain: Chain) {
    switch (chain.chainType) {
      case ChainType.EVM:
        const evmService = this.moduleRef.get(Web3EVMService);
        evmService.init(chain);
        return evmService;
      case ChainType.MOVEVM:
        const aptosService = this.moduleRef.get(Web3AptosService);
        aptosService.init(chain);
        return aptosService;
      default:
        throw new Error(`Not support network ${chain.chainName}`);
    }
  }
}
