import BigNumber from 'bignumber.js';
import { Transaction } from 'src/entities/transaction.entity';
import { Chain, ChainType } from 'src/entities/chain.entity';
import { SponsorAsset } from 'src/entities/sponsor-asset.entity';
import { Asset, AssetType } from 'src/entities/asset.entity';
import { UserAsset } from 'src/entities/user-asset.entity';
import { HotWallet } from 'src/entities/hot-wallet.entity';
import { RedeemItem } from 'src/entities/redeem-item.entity';
import { Account, AccountAuthenticator } from '@aptos-labs/ts-sdk';
import { UserChain } from 'src/entities/user-chain.entity';

export type Web3ContractParam = {
  paramType:
    | 'u8'
    | 'number'
    | 'string'
    | 'boolean'
    | 'bytes'
    | 'array'
    | 'array256'
    | 'address'
    | 'object';
  paramValue: any;
};

export type Web3Signature = {
  messageHash: string;
  signature: any;
};

export type Web3Account = {
  address: string;
  privateKey: string;
  publicKey?: string;
};

export type Web3Transaction = {
  txHash: string;
  status: boolean;
  from: string;
  to: string;
  gasPrice: BigNumber;
  gasUsed: BigNumber;
  fee: BigNumber;
  contractAddress: string;
  payload: any;
  logs: any[];
  events?: Web3Event[];
};

export type Web3Event = {
  name: string;
  [key: string]: any;
};

export type Web3Token = {
  address: string;
  name: string;
  symbol: string;
  decimals: number;
};

export type Web3SignedTransaction = {
  signedTx: string | AccountAuthenticator;
  signedTxAsFeePayer?: string | AccountAuthenticator;
  txHash: string;
  hotwallet: HotWallet;
  transaction?: any;
};

export type Web3CallWriteMethodEVM = {
  method: string;
  params: any[];
  contractAddress?: string;
  abi?: any[];
  isWait?: boolean;
  isBatch?: boolean;
  batchCalls?: any[];
  userPrivateKey?: string;
};

export type Web3CallWriteMethodAptos = {
  method: string;
  typeParams?: any[];
  params: any[];
  contractAddress?: string;
  sender?: UserChain;
  isTransfer?: boolean;
};

export type Web3CallViewMethodEVM = {
  method: string;
  params: any[];
  contractAddress?: string;
  abi?: any[];
};

export type Web3CallViewMethodAptos = {
  method: string;
  typeParams?: any[];
  params: any[];
  contractAddress?: string;
};

export type TransactionEvent = {
  txHash: string;
  transactionId?: number;
  tokenId?: string;
  gasUsed: BigNumber;
  hotWallet: string;
  status: boolean;
  nftContractAddress?: string;
};

export type Web3SendNFTRequest = {
  transaction: Transaction;
  userAsset: UserAsset;
  sender: Web3Account;
  receiver: Web3Account;
};

export type Web3SendTokenRequest = {
  transaction: Transaction;
  userAsset: UserAsset;
  sender: Web3Account;
  receiver: Web3Account;
  amount: string;
};
export interface Web3Service {
  init(chain: Chain);

  getChainType(): ChainType;

  signTypedMessage(params: Web3ContractParam[]): Web3Signature;

  verifySignMessage(
    originalMessage: string,
    signature: string,
    address: string,
  ): boolean;

  getToken(tokenAddress: string): Promise<Web3Token>;

  getTransaction(txId: string): Promise<Web3Transaction>;

  parseLogs(logs: any[], abi: any[]): Web3Event[];

  getBalance(address: string): Promise<BigNumber>;

  getTokenBalance(asset: Asset, address: string): Promise<BigNumber>;

  getAccount(privateKey: string): Web3Account | Account;

  createAccount(): Web3Account;

  signTransaction(
    requestData: Web3CallWriteMethodEVM | Web3CallWriteMethodAptos,
  ): Promise<Web3SignedTransaction>;

  sendSignedTransaction(requestData: Web3SignedTransaction): Promise<any>;

  callWriteMethod(
    requestData: Web3CallWriteMethodEVM | Web3CallWriteMethodAptos,
  ): Promise<any>;

  callViewMethod(
    requestData: Web3CallViewMethodEVM | Web3CallViewMethodAptos,
  ): Promise<any>;

  getTransactionEvent(
    txHash: string,
    assetType: AssetType,
  ): Promise<TransactionEvent>;

  createSignedTransaction(
    transaction: Transaction,
    redeemItem: RedeemItem,
  ): Promise<Web3SignedTransaction>;

  setUpDelegation(
    eoaAddress: string,
    privateKey: string,
    chain: Chain,
  ): Promise<boolean>;

  performRedeemVoucherWithNex3Transaction(
    userAddress: string,
    userPrivateKey: string,
    transaction: Transaction,
    redeemItem: RedeemItem,
  ): Promise<any>;
}
