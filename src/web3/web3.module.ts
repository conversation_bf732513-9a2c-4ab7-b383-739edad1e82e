import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Asset } from 'src/entities/asset.entity';
import { HotWallet } from 'src/entities/hot-wallet.entity';
import { RedeemItem } from 'src/entities/redeem-item.entity';
import { Sponsor } from 'src/entities/sponsor.entity';
import { Transaction } from 'src/entities/transaction.entity';
import { UserAsset } from 'src/entities/user-asset.entity';
import { UserChain } from 'src/entities/user-chain.entity';
import { Web3EVMService } from './evm/web3-evm.service';
import { Web3AptosService } from './aptos/web3-aptos.service';
import { Web3ServiceFactory } from './web3.factory.service';
// import { Web3AptosService } from './evm/web3-aptos.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      HotWallet,
      Transaction,
      RedeemItem,
      Asset,
      <PERSON>r<PERSON>hain,
      <PERSON>rAsset,
      Sponsor,
    ]),
  ],
  providers: [Web3ServiceFactory, Web3EVMService, Web3AptosService],
  exports: [Web3ServiceFactory],
})
export class Web3Module {}
