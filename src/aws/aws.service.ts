import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { Injectable, Logger } from '@nestjs/common';
import { MimeType } from 'src/common/constants';
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');

@Injectable()
export class AwsService {
  private readonly logger = new Logger(AwsService.name);
  private s3Client: S3Client;

  constructor() {
    this.s3Client = new S3Client({
      region: process.env.AWS_REGION,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      },
    });
  }

  async upload(key: string, content: any, mimeType: string) {
    await this.s3Client.send(
      new PutObjectCommand({
        Bucket: process.env.AWS_S3_BUCKET,
        Key: key,
        Body: content,
        ContentType: mimeType,
      }),
    );
    return `${process.env.AWS_S3_URL}/${key}`;
  }

  async uploadImage(key: string, content: any) {
    return this.upload(key, content, MimeType.IMAGE_PNG);
  }

  async uploadJson(key: string, content: any) {
    return this.upload(key, content, MimeType.APPLICATION_JSON);
  }

  async getSignedUrl(key: string, expiresIn = 600) {
    const command = new PutObjectCommand({
      Bucket: process.env.AWS_S3_BUCKET,
      Key: key,
    });
    const url = await getSignedUrl(this.s3Client, command, { expiresIn });
    return url;
  }
}
