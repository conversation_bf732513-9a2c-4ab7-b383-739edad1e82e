import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { DataSource, DataSourceOptions } from 'typeorm';

export const dbConfig: TypeOrmModuleOptions = {
  type: 'mysql',
  host: process.env.DB_HOST,
  port: Number(process.env.DB_PORT),
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  entities: ['dist/**/*.entity{.ts,.js}'],
  migrations: ['dist/migrations/*{.ts,.js}'],
  autoLoadEntities: true,
  synchronize: ['dev', 'local', 'test'].includes(process.env.ENV)
    ? true
    : false,
  retryAttempts: 10,
  logging: ['dev', 'local'].includes(process.env.ENV) ? true : false,
  cache: {
    type: 'ioredis',
    options: {
      url: `redis://${process.env.REDIS_HOST}:${process.env.REDIS_PORT}`,
      host: process.env.REDIS_HOST,
      port: Number(process.env.REDIS_PORT),
      socket: {
        host: process.env.REDIS_HOST,
        port: Number(process.env.REDIS_PORT),
      },
      ttl: Number(process.env.REDIS_TTL),
      keyPrefix: `${process.env.REDIS_PREFIX}:sql:`,
    },
  },
};

export const connectionSource = new DataSource(dbConfig as DataSourceOptions);
