import { Module } from '@nestjs/common';
import { FeedPricesService } from './feed-prices.service';
import { FeedPricesController } from './feed-prices.controller';
import { CommonModule } from 'src/common-services/common.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Asset } from 'src/entities/asset.entity';

@Module({
  imports: [CommonModule, TypeOrmModule.forFeature([Asset])],
  controllers: [FeedPricesController],
  providers: [FeedPricesService],
  exports: [FeedPricesService],
})
export class FeedPricesModule {}
