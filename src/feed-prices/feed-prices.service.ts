import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import axios from 'axios';
import { CommonService } from 'src/common-services/common.service';
import { Utils } from 'src/common/utils';
import { Asset } from 'src/entities/asset.entity';
import { DataSource, Repository } from 'typeorm';

export enum TokenPriceSource {
  COINMARKETCAP = 'coinmarketcap',
  COINGECKO = 'coingecko',
  GATEIO = 'gateio',
}

export type TokenPrice = {
  id: number;
  usd: string;
  jpy?: string;
  vnd?: string;
  source?: string;
};

@Injectable()
export class FeedPricesService {
  private readonly logger = new Logger(FeedPricesService.name);

  constructor(
    private commonService: CommonService,
    private dataSource: DataSource,
    @InjectRepository(Asset)
    private assetRepository: Repository<Asset>,
  ) {}

  async getPrice(requestData: any) {
    if (requestData.source === TokenPriceSource.COINMARKETCAP) {
      return this.getPriceFromCoinmarketcap({
        priceFeedApi: { coinmarketcapApiId: requestData.tokenId },
      } as any);
    } else if (requestData.source === TokenPriceSource.COINGECKO) {
      return this.getPriceFromCoingecko([
        {
          priceFeedApi: { coingeckoApiId: requestData.tokenId },
        } as any,
      ]);
    }
    // else if (requestData.source === TokenPriceSource.GATEIO) {
    //   return this.getPriceFromGateIo({
    //     priceFeedApi: { gateIoCurrencyPair: requestData.tokenId },
    //   } as any);
    // }
  }

  async syncPrice() {
    const currencies = await this.assetRepository.find();

    const coingeckoCurrencies = [] as Asset[];
    const coinmarketcapCurrencies = [] as Asset[];
    // const gateioCurrencies = [];
    for (let index = 0; index < currencies.length; index++) {
      const currency = currencies[index];
      if (!currency.priceSource) {
        continue;
      }
      if (currency.priceSource.coingeckoApiId) {
        coingeckoCurrencies.push(currency);
      } else if (currency.priceSource.coinmarketcapApiId) {
        coinmarketcapCurrencies.push(currency);
      }
      // else if (currency.gateIoCurrencyPair) {
      //   gateioCurrencies.push(currency);
      // }
    }

    // Update price from coingecko
    if (coingeckoCurrencies.length > 0) {
      try {
        const tokenPrices =
          await this.getPriceFromCoingecko(coingeckoCurrencies);
        for (let index = 0; index < tokenPrices.length; index++) {
          const tokenPrice = tokenPrices[index];
          await this.assetRepository.update(
            { id: tokenPrice.id },
            {
              priceUsd: tokenPrice.usd,
              priceVnd: tokenPrice.vnd,
            },
          );
        }
      } catch (error) {
        Utils.logMonitor(error);
      }
    }

    // Update price from coinmarketcap
    if (coinmarketcapCurrencies.length > 0) {
      for (let index = 0; index < coinmarketcapCurrencies.length; index++) {
        try {
          const currency = coinmarketcapCurrencies[index];
          const tokenPrice = await this.getPriceFromCoinmarketcap(currency);
          await this.assetRepository.update(
            { id: tokenPrice.id },
            {
              priceUsd: tokenPrice.usd,
              priceVnd: tokenPrice.vnd,
            },
          );
        } catch (error) {
          Utils.logMonitor(
            error,
            `Can not get price of token ${coinmarketcapCurrencies[index].symbol} from coinmarketcap`,
          );
        }
      }
    }

    // Update price from gateio
    // if (gateioCurrencies.length > 0) {
    //   for (let index = 0; index < gateioCurrencies.length; index++) {
    //     try {
    //       const currency = gateioCurrencies[index];
    //       const tokenPrice = await this.getPriceFromGateIo(currency);
    //       await this.assetRepository.update(
    //         { id: tokenPrice.id },
    //         {
    //           priceUsd: tokenPrice.usd,
    //         },
    //       );
    //     } catch (error) {
    //       Utils.logMonitor(
    //         error,
    //         `Can not get price of token ${gateioCurrencies[index].symbol} from gateio`,
    //       );
    //     }
    //   }
    // }

    // Clear cache
    return this.assetRepository.find();
  }

  async getPriceFromCoinmarketcap(currency: Asset): Promise<TokenPrice> {
    const tokenId = currency.priceSource.coinmarketcapApiId;
    const url = `https://pro-api.coinmarketcap.com/v2/tools/price-conversion?id=${tokenId}&amount=1&convert=VND`;
    const response = await axios.get(url, {
      headers: {
        'X-CMC_PRO_API_KEY': process.env.COINMARKETCAP_API_KEY,
      },
    });
    if (response.status === 200) {
      return {
        id: currency.id,
        usd: response.data.data.quote.USD.price.toString(),
        vnd: response.data.data.quote.VND.price.toString(),
        source: TokenPriceSource.COINMARKETCAP,
      };
    } else {
      throw new Error(`Can't get price from coingecko ${response.statusText}`);
    }
  }

  async getPriceFromCoingecko(currencies: Asset[]): Promise<TokenPrice[]> {
    const tokenIds = currencies.map((obj) => obj.priceSource.coingeckoApiId);
    const url = `https://api.coingecko.com/api/v3/simple/price?ids=${tokenIds}&vs_currencies=usd,jpy,vnd`;
    const response = await axios.get(url);
    if (response.status === 200) {
      const tokenPrices: TokenPrice[] = [];
      for (const [key, value] of Object.entries(response.data)) {
        const currency = currencies.find(
          (obj) => obj.priceSource.coingeckoApiId === key,
        );
        tokenPrices.push({
          id: currency.id,
          usd: value['usd'].toString(),
          jpy: value['jpy'].toString(),
          vnd: value['vnd'].toString(),
          source: TokenPriceSource.COINGECKO,
        });
      }
      return tokenPrices;
    } else {
      throw new Error(`Can't get price from coingecko ${response.statusText}`);
    }
  }

  // async getPriceFromGateIo(currency: Asset): Promise<TokenPrice> {
  //   const tokenId = currency.gateIoCurrencyPair;
  //   const url = `https://api.gateio.ws/api/v4/spot/tickers?currency_pair=${tokenId}`;
  //   const response = await axios.get(url);
  //   if (response.status === 200) {
  //     return {
  //       id: currency.id,
  //       usd: response.data
  //         .find((pair) => pair.currency_pair === tokenId)
  //         .last.toString(),
  //       source: TokenPriceSource.GATEIO,
  //     };
  //   } else {
  //     throw new Error(`Can't get price from gateio ${response.statusText}`);
  //   }
  // }
}
