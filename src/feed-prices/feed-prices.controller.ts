import { Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAdminAuthGuard } from 'src/auth/guard/jwt-admin-auth.guard';
import { FeedPricesService } from './feed-prices.service';

@ApiTags('Feed-prices')
@Controller('feed-prices')
export class FeedPricesController {
  constructor(private readonly feedPricesService: FeedPricesService) {}

  @ApiOperation({ summary: 'Get feed price' })
  @ApiQuery({ name: 'requestData', required: false, type: Object })
  @Get()
  getPrice(@Query() requestData: any) {
    return this.feedPricesService.getPrice(requestData);
  }

  @ApiOperation({ summary: 'Sync feed prices' })
  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('sync')
  syncPrice() {
    return this.feedPricesService.syncPrice();
  }
}
