import { Module } from '@nestjs/common';
import { RedeemItemsService } from './redeem-items.service';
import { RedeemItemsController } from './redeem-items.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RedeemItem } from 'src/entities/redeem-item.entity';

@Module({
  controllers: [RedeemItemsController],
  providers: [RedeemItemsService],
  imports: [TypeOrmModule.forFeature([RedeemItem])],
})
export class RedeemItemsModule {}
