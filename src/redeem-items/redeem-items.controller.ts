import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { RedeemItemsService } from './redeem-items.service';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { FindRedeemItemsDto } from './dto/find-redeem-items.dto';
import { User } from 'src/common/decorator/user.decorator';
import { UserPayload } from 'src/entities/user.entity';
import { JwtAuthGuard } from 'src/auth/guard/jwt-auth.guard';

@ApiTags('Redeem Items')
@ApiBearerAuth()
@Controller('redeem-items')
@UseGuards(JwtAuthGuard)
export class RedeemItemsController {
  constructor(private readonly redeemItemsService: RedeemItemsService) {}

  @ApiOperation({ summary: `Get redeem-item list` })
  @Get('')
  async getAll(
    @User() currentUser: UserPayload,
    @Query() requestData: FindRedeemItemsDto,
  ) {
    const result = await this.redeemItemsService.getAll(
      currentUser,
      requestData,
    );
    return result;
  }

  @ApiOperation({ summary: `Get redeem-item detail` })
  @Get(':id')
  async getDetail(@Param('id') id: number) {
    const result = await this.redeemItemsService.getDetail(+id);
    return result;
  }
}
