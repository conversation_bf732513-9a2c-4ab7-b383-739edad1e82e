import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, Validate } from 'class-validator';
import { SearchDto } from 'src/common/dto.common';
import { PaidType, RedeemItemType } from 'src/entities/redeem-item.entity';

export class FindRedeemItemsDto extends SearchDto {
  @ApiProperty({
    description: 'Filter by redeem item type',
    required: false,
    enum: RedeemItemType,
  })
  @IsOptional()
  @IsEnum(RedeemItemType)
  redeemItemType?: RedeemItemType;

  @ApiProperty({
    description: 'Filter by paid item type',
    required: false,
    enum: PaidType,
  })
  @IsOptional()
  @IsEnum(PaidType)
  paidItemType?: PaidType;
}
