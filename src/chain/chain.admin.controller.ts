import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common';
import { JwtAdminAuthGuard } from 'src/auth/guard/jwt-admin-auth.guard';
import { JwtAuthGuard } from 'src/auth/guard/jwt-auth.guard';
import { ChainAdminService } from './chain.admin.service';
import { UpdateChainDto } from './dto/update-chain.dto';
import { AddHotwalletDto } from './dto/add-hotwallet.dto';

@Controller('admin/chains')
@UseGuards(JwtAdminAuthGuard)
export class ChainAdminController {
  constructor(private readonly chainService: ChainAdminService) {}

  @Get()
  findChains() {
    return this.chainService.findChains();
  }

  @Post()
  updateChain(@Body() requestData: UpdateChainDto) {
    return this.chainService.updateChain(requestData);
  }

  @Post(':id/generate-signer')
  generateSigner(@Param('id') id: number) {
    return this.chainService.generateSigner(id);
  }

  @Post(':id/generate-hotwallet')
  generateHotWallet(@Param('id') id: number) {
    return this.chainService.generateHotWallet(id);
  }

  @Post(':id/hotwallet')
  addHotWallet(@Param('id') id: number, @Body() requestData: AddHotwalletDto) {
    return this.chainService.addHotWallet(id, requestData);
  }

  @Post(':id/sync')
  syncBalance(@Param('id') id: number) {
    return this.chainService.syncBalance(id);
  }

  @Post('decrypt/private-key')
  decryptPrivateKey(@Body() privateKey: { privateKey: string }) {
    return this.chainService.decryptPrivateKey(privateKey);
  }
}
