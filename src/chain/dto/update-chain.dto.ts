import { IsEnum, IsString } from 'class-validator';
import { ChainName, ChainType } from 'src/entities/chain.entity';

export class UpdateChainDto {
  @IsEnum(ChainName)
  chainName: ChainName;

  @IsEnum(ChainType)
  chainType: ChainType;

  @IsString()
  blockchainId: string;

  @IsString()
  rpcs: string;

  @IsString()
  factorySponsorContractAddress: string;

  @IsString()
  voucherContractAddress: string;
}
