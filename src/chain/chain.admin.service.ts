import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { paginate } from 'nestjs-typeorm-paginate';
import { CommonService } from 'src/common-services/common.service';
import { CacheSetting } from 'src/common/constants';
import { SearchDto } from 'src/common/dto.common';
import { Utils } from 'src/common/utils';
import { Chain, ChainName } from 'src/entities/chain.entity';
import { DataSource, Repository } from 'typeorm';
import { UpdateChainDto } from './dto/update-chain.dto';
import { HotWallet } from 'src/entities/hot-wallet.entity';
import { AddHotwalletDto } from './dto/add-hotwallet.dto';

@Injectable()
export class ChainAdminService {
  private readonly logger = new Logger(ChainAdminService.name);

  constructor(
    private commonService: CommonService,
    private dataSource: DataSource,
    @InjectRepository(Chain)
    private chainRepository: Repository<Chain>,
    @InjectRepository(HotWallet)
    private hotWalletRepository: Repository<HotWallet>,
  ) {}

  async findChains() {
    return this.chainRepository.find();
  }

  async updateChain(requestData: UpdateChainDto) {
    await this.commonService.clearCacheByPrefix(Utils.getSqlKey('CHAIN_GET'));
    return this.chainRepository.upsert(requestData, ['chainName']);
  }

  async generateSigner(id: number) {
    await this.commonService.clearCacheByPrefix(Utils.getSqlKey('CHAIN_GET'));
    const web3 = await this.commonService.getWeb3ByChainId(id);
    const account = await web3.createAccount();
    await this.chainRepository.update(
      {
        id,
      },
      {
        signer: account.address,
        privateKey: Utils.encrypt(account.privateKey),
      },
    );
    return await this.chainRepository.findOneBy({ id });
  }

  async generateHotWallet(id: number) {
    await this.commonService.clearCacheByPrefix(
      Utils.getSqlKey('HOT_WALLET_GET'),
    );

    const chain = await this.chainRepository.findOneBy({ id });
    const web3 = await this.commonService.getWeb3ByChainId(chain.id);
    const account = await web3.createAccount();
    await this.hotWalletRepository.save({
      chainId: chain.id,
      walletAddress: account.address,
      privateKey: Utils.encrypt(account.privateKey),
    });
    return await this.hotWalletRepository.findOneBy({
      walletAddress: account.address,
    });
  }

  async addHotWallet(id: number, requestData: AddHotwalletDto) {
    await this.commonService.clearCacheByPrefix(
      Utils.getSqlKey('HOT_WALLET_GET'),
    );

    const chain = await this.chainRepository.findOneBy({ id });
    await this.hotWalletRepository.save({
      chainId: chain.id,
      walletAddress: requestData.walletAddress,
      privateKey: Utils.encrypt(requestData.privateKey),
    });
    return await this.hotWalletRepository.findOneBy({
      walletAddress: requestData.walletAddress,
    });
  }

  async syncBalance(id: number) {
    await this.commonService.clearCacheByPrefix(
      Utils.getSqlKey('HOT_WALLET_GET'),
    );

    const chain = await this.chainRepository.findOneBy({ id });
    const web3 = await this.commonService.getWeb3ByChainId(chain.id);
    const hotWallets = await this.hotWalletRepository.findBy({
      chainId: chain.id,
    });
    for (let index = 0; index < hotWallets.length; index++) {
      const hotWallet = hotWallets[index];
      const balance = await web3.getBalance(hotWallet.walletAddress);
      await this.hotWalletRepository.update(
        { id: hotWallet.id },
        { balance: balance.toString() },
      );
    }
    return await this.hotWalletRepository.findBy({
      chainId: chain.id,
    });
  }

  async decryptPrivateKey(privateKey: { privateKey: string }) {
    const decryptedPrivateKey = Utils.decrypt(privateKey.privateKey);
    return {
      privateKey: decryptedPrivateKey,
    };
  }
}
