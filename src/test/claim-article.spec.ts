/* eslint-disable max-lines-per-function */
jest.setTimeout(60000);
import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { Redis } from 'ioredis';
import * as moment from 'moment';
import { AppModule } from 'src/app.module';
import { Article } from 'src/entities/article.entity';
import {
  UserArticleAnswer,
  UserArticleAnswerStatus,
} from 'src/entities/user-article-answer.entity';
import { User } from 'src/entities/user.entity';
import { TransactionService } from 'src/transactions/transaction.service';
import { DataSource } from 'typeorm';
import { InitTestData, setupTestApp, TestUtils } from './test-utils';
import {
  Transaction,
  TransactionStatus,
  TransactionSubType,
  TransactionType,
} from 'src/entities/transaction.entity';

describe('TransactionService: Claim XP For Article', () => {
  let testUtils: TestUtils;
  let app: INestApplication;
  let dataSource: DataSource;
  let transactionService: TransactionService;
  let redis: Redis;
  let testData: InitTestData;

  beforeAll(async () => {
    testUtils = await setupTestApp();
    app = testUtils.app;
    dataSource = testUtils.dataSource;
    redis = testUtils.redis;

    transactionService = testUtils.module.get(TransactionService);
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    await dataSource.queryResultCache?.clear();
    await redis.flushdb();
    await testUtils.clearTestData();
    jest.clearAllMocks();
    testData = await testUtils.initTestData();
  });

  const doQuiz = async (data: { user: User; article: Article }) => {
    const { user, article } = data;
    const transaction = await transactionService.createTransaction(
      {
        id: user.id,
        brandId: testData.brandNex3.id,
        email: user.email,
      },
      {
        type: TransactionType.EARN,
        subType: TransactionSubType.LEARN,
        articleId: article.id,
      } as any,
    );
    return transaction;
  };

  it('nên nhận thưởng thành công và cập nhật tất cả các bản ghi liên quan', async () => {
    const user = await testUtils.createUser({ point: '1000' });
    const article = await testUtils.createArticle({
      brand: testData.brandNex3,
      point: '100',
    });
    const userArticleAnswer = await testUtils.createUserArticleAnswer({
      user,
      article,
    });

    // Do quiz
    const transaction = await doQuiz({ user, article });
    const updatedTransaction = await dataSource
      .getRepository(Transaction)
      .findOneBy({
        id: transaction.id,
      });
    expect(updatedTransaction).toBeDefined();
    expect(updatedTransaction.id).toBeDefined();
    expect(updatedTransaction.type).toBe(TransactionType.EARN);
    expect(updatedTransaction.subType).toBe(TransactionSubType.LEARN);
    expect(updatedTransaction.articleId).toBe(article.id);
    expect(updatedTransaction.userArticleAnswerId).toBe(userArticleAnswer.id);
    expect(updatedTransaction.fromUserId).toBe(user.id);
    expect(updatedTransaction.toUserId).toBe(user.id);
    expect(updatedTransaction.brandId).toBe(testData.brandNex3.id);
    expect(Number(updatedTransaction.point)).toBe(100);
    expect(updatedTransaction.status).toBe(TransactionStatus.SUCCESS);

    // Kiểm tra điểm của user đã được cộng
    const userUpdated = await dataSource.getRepository(User).findOneBy({
      id: user.id,
    });
    expect(userUpdated).toBeDefined();
    expect(Number(userUpdated.point)).toBe(1100); // Ban đầu 1000 + 100 điểm nhận thưởng

    // Kiểm tra trạng thái userArticleAnswer đã được cập nhật
    const userArticleAnswerUpdated = await dataSource
      .getRepository(UserArticleAnswer)
      .findOneBy({
        id: userArticleAnswer.id,
      });
    expect(userArticleAnswerUpdated.status).toBe(
      UserArticleAnswerStatus.CLAIMED,
    );
    expect(userArticleAnswerUpdated.claimedAt).toBeDefined();
    expect(
      moment(userArticleAnswerUpdated.claimedAt).startOf('hour').toISOString(),
    ).toBe(moment().startOf('hour').toISOString());

    // Kiểm tra điểm đã claim của article đã được cập nhật
    const articleUpdated = await dataSource.getRepository(Article).findOneBy({
      id: article.id,
    });
    expect(Number(articleUpdated.claimedPoint)).toBe(100); // Ban đầu 0 + 100 điểm
  });

  it('nên nhận thưởng thành công và cập nhật tất cả các bản ghi liên quan cho nhiều bài viết', async () => {
    const user1 = await testUtils.createUser({ point: '1000' });
    const article1 = await testUtils.createArticle({
      brand: testData.brandNex3,
      point: '100',
    }); // Tạo article 1
    const userArticleAnswer1 = await testUtils.createUserArticleAnswer({
      user: user1,
      article: article1,
    });

    const article2 = await testUtils.createArticle({
      brand: testData.brandNex3,
      point: '100',
    }); // Tạo article 2
    const userArticleAnswer2 = await testUtils.createUserArticleAnswer({
      user: user1,
      article: article2,
    });

    await doQuiz({ user: user1, article: article1 }); // Claim article 1
    await doQuiz({ user: user1, article: article2 }); // Claim article 2

    // Kiểm tra điểm của user đã được cộng đúng (mỗi bài viết +100 => tổng cộng 1200)
    const userUpdated = await dataSource.getRepository(User).findOneBy({
      id: user1.id,
    });
    expect(userUpdated).toBeDefined();
    expect(Number(userUpdated.point)).toBe(1200); // Ban đầu 1000 + 2*100

    // Kiểm tra trạng thái và thời gian claim của userArticleAnswer 1
    const userArticleAnswer1Updated = await dataSource
      .getRepository(UserArticleAnswer)
      .findOneBy({
        id: userArticleAnswer1.id,
      });
    expect(userArticleAnswer1Updated.status).toBe(
      UserArticleAnswerStatus.CLAIMED,
    );
    expect(userArticleAnswer1Updated.claimedAt).toBeDefined();
    expect(
      moment(userArticleAnswer1Updated.claimedAt)
        .startOf('minute')
        .toISOString(),
    ).toBe(moment().startOf('minute').toISOString());

    // Kiểm tra trạng thái và thời gian claim của userArticleAnswer 2
    const userArticleAnswer2Updated = await dataSource
      .getRepository(UserArticleAnswer)
      .findOneBy({
        id: userArticleAnswer2.id,
      });
    expect(userArticleAnswer2Updated.status).toBe(
      UserArticleAnswerStatus.CLAIMED,
    );
    expect(userArticleAnswer2Updated.claimedAt).toBeDefined();
    expect(
      moment(userArticleAnswer2Updated.claimedAt)
        .startOf('minute')
        .toISOString(),
    ).toBe(moment().startOf('minute').toISOString());

    // Kiểm tra điểm đã claim của article 1
    const article1Updated = await dataSource.getRepository(Article).findOneBy({
      id: article1.id,
    });
    expect(Number(article1Updated.claimedPoint)).toBe(100); // Ban đầu 0 + 100

    // Kiểm tra điểm đã claim của article 2
    const article2Updated = await dataSource.getRepository(Article).findOneBy({
      id: article2.id,
    });
    expect(Number(article2Updated.claimedPoint)).toBe(100); // Ban đầu 0 + 100
  });

  it('nên cho phép 2 người dùng làm cùng một bài viết đồng thời', async () => {
    const user1 = await testUtils.createUser({ point: '1000' });
    const user2 = await testUtils.createUser({ point: '0' });

    const article = await testUtils.createArticle({
      brand: testData.brandNex3,
      point: '100',
    }); // tạo article + user1
    const userArticleAnswer1 = await testUtils.createUserArticleAnswer({
      user: user1,
      article,
    }); // user1 làm article
    const userArticleAnswer2 = await testUtils.createUserArticleAnswer({
      user: user2,
      article,
    }); // user2 làm article

    // Thực hiện đồng thời 2 giao dịch
    await Promise.all([
      doQuiz({ user: user1, article }),
      doQuiz({ user: user2, article }),
    ]);

    // Kiểm tra kết quả của từng user
    const userUpdated1 = await dataSource
      .getRepository(User)
      .findOneBy({ id: user1.id });
    const userUpdated2 = await dataSource
      .getRepository(User)
      .findOneBy({ id: user2.id });

    expect(Number(userUpdated1.point)).toBe(1100);
    expect(Number(userUpdated2.point)).toBe(100);

    // Kiểm tra điểm đã claim của article đã được cập nhật
    const articleUpdated = await dataSource
      .getRepository(Article)
      .findOneBy({ id: article.id });
    expect(Number(articleUpdated.claimedPoint)).toBe(200); // 100 mỗi người
  });

  it('nên xử lý chính xác các yêu cầu nhận thưởng đồng thời và duy trì tính toàn vẹn của số dư', async () => {
    const user1 = await testUtils.createUser({ point: '1000' });
    const article = await testUtils.createArticle({
      brand: testData.brandNex3,
      point: '100',
    });
    const userArticleAnswer = await testUtils.createUserArticleAnswer({
      user: user1,
      article,
    });

    // Gọi đồng thời 5 giao dịch để giả lập race condition
    // Dùng Promise.allSettled để đảm bảo tất cả đều được thực thi (kể cả khi một số bị lỗi)
    // Mục tiêu: chỉ 1 giao dịch thành công, các giao dịch còn lại sẽ bị từ chối
    await Promise.allSettled(
      Array.from({ length: 5 }).map(() => doQuiz({ user: user1, article })),
    );

    // Kiểm tra số dư người dùng sau khi xử lý đồng thời
    const userUpdated = await dataSource.getRepository(User).findOneBy({
      id: user1.id,
    });

    // Đảm bảo điểm chỉ được cộng 1 lần
    expect(userUpdated).toBeDefined();
    expect(Number(userUpdated.point)).toBe(1100); // 1000 ban đầu + 100 điểm thưởng

    // Kiểm tra trạng thái userArticleAnswer đã được cập nhật đúng
    const userArticleAnswerUpdated = await dataSource
      .getRepository(UserArticleAnswer)
      .findOneBy({ id: userArticleAnswer.id });

    expect(userArticleAnswerUpdated.status).toBe(
      UserArticleAnswerStatus.CLAIMED,
    );
    expect(userArticleAnswerUpdated.claimedAt).toBeDefined();
    expect(
      moment(userArticleAnswerUpdated.claimedAt).startOf('hour').toISOString(),
    ).toBe(moment().startOf('hour').toISOString());

    // Kiểm tra điểm của article cũng chỉ được cộng 1 lần
    const articleUpdated = await dataSource.getRepository(Article).findOneBy({
      id: article.id,
    });
    expect(Number(articleUpdated.claimedPoint)).toBe(100); // 0 ban đầu + 100
  });
});
