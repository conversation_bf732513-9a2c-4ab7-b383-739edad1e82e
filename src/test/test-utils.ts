/* eslint-disable max-lines-per-function */
import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { AppModule } from 'src/app.module';
import { DataSource } from 'typeorm';
import Redis from 'ioredis';
import { Article, ArticleCategory } from 'src/entities/article.entity';
import { Brand } from 'src/entities/brand.entity';
import { User, UserStatus } from 'src/entities/user.entity';
import { UserChain } from 'src/entities/user-chain.entity';
import { Chain, ChainType } from 'src/entities/chain.entity';
import { HotWallet } from 'src/entities/hot-wallet.entity';
import { Utils } from 'src/common/utils';
import { Asset, AssetType } from 'src/entities/asset.entity';
import { UserAsset, UserAssetStatus } from 'src/entities/user-asset.entity';
import { UserArticleAnswer } from 'src/entities/user-article-answer.entity';
import { Quest, QuestLevel, QuestStatus } from 'src/entities/quest.entity';
import { UserQuest, UserQuestStatus } from 'src/entities/user-quest.entity';
import {
  PaidType,
  RedeemItem,
  RedeemItemStatus,
  RedeemItemType,
} from 'src/entities/redeem-item.entity';
import { Sponsor } from 'src/entities/sponsor.entity';
import { SponsorAsset } from 'src/entities/sponsor-asset.entity';
import * as moment from 'moment';

export interface InitTestData {
  brandNex3: Brand;
  chainEvm: Chain;
  hotwalletEvm: HotWallet;
  assetNex3: Asset;
  assetNex3NFT: Asset;
  assetNex3Voucher: Asset;
  sponsorNex3: Sponsor;
  sponsorAssetNex3: SponsorAsset;
  sponsorAssetNex3NFT: SponsorAsset;
}

export interface TestUtils {
  app: INestApplication;
  module: TestingModule;
  dataSource: DataSource;
  redis: Redis;
  clearTestData: () => Promise<void>;
  initTestData: () => Promise<InitTestData>;

  // Article
  createArticle: (data: { brand: Brand; point: string }) => Promise<Article>;
  createUserArticleAnswer: (data: {
    user: User;
    article: Article;
  }) => Promise<UserArticleAnswer>;

  // Quest
  createQuest: (data: {
    point: string;
    brand: Brand;
    startDate: Date;
    toDate: Date;
  }) => Promise<Quest>;
  createUserQuest: (data: { user: User; quest: Quest }) => Promise<UserQuest>;

  // Redeem Item
  createRedeemItem: (data: {
    chain: Chain;
    brand: Brand;
    redeemItemData: Partial<RedeemItem>;
  }) => Promise<RedeemItem>;

  // User
  createUser: (data: { point?: string }) => Promise<User>;
  createUserChain: (data: { user: User; chain: Chain }) => Promise<UserChain>;
  createUserAsset: (data: {
    user: User;
    asset: Asset;
    balance: string;
  }) => Promise<UserAsset>;
  createUserAssetNFT: (data: { asset: Asset }) => Promise<UserAsset>;
  createUserAssetVoucher: (data: { asset: Asset }) => Promise<UserAsset>;
}

export async function setupTestApp(): Promise<TestUtils> {
  const module: TestingModule = await Test.createTestingModule({
    imports: [AppModule],
  }).compile();

  const app = module.createNestApplication();
  await app.init();

  const dataSource = module.get(DataSource);
  const redis = new Redis({
    host: process.env.REDIS_HOST,
    port: Number(process.env.REDIS_PORT),
    keyPrefix: process.env.REDIS_PREFIX,
  });

  const initTestData = async () => {
    jest.clearAllMocks();
    jest.resetAllMocks();

    // Brand
    const brandNex3 = await dataSource.getRepository(Brand).save({
      name: 'Nex3',
      logoUrl: 'https://example.com/logo.png',
      theme: {
        primaryColor: '#000000',
        secondaryColor: '#FFFFFF',
        fontFamily: 'Arial, sans-serif',
      },
    });

    // Chain
    const chainEvm = await dataSource.getRepository(Chain).save({
      chainName: 'Ethereum Mainnet',
      chainType: ChainType.EVM,
      imageUrl: 'https://example.com/images/ethereum.png',
      blockchainId: '1',
      rpcs: 'https://eth.llamarpc.com',
      signer: '******************************************',
      privateKey: Utils.encrypt('0xabcdef1234567890'),
      contractAddress: '******************************************',
    });

    // Hotwallet
    const hotwalletEvm = await dataSource.getRepository(HotWallet).save({
      chainId: chainEvm.id,
      walletAddress: '0x1234567890abcdef',
      privateKey: Utils.encrypt('0xabcdef1234567890'),
    });

    // Asset
    const assetNex3 = await dataSource.getRepository(Asset).save({
      chainId: chainEvm.id,
      type: AssetType.TOKEN,
      name: 'Nex3',
      symbol: 'NEX3',
      image: 'https://example.com/image.png',
      description: 'This is a nex3 token',
      contractAddress: '******************************************',
      isStableToken: false,
      isNativeToken: false,
      priceUsd: '1.00',
      priceVnd: '24000',
    });

    const assetNex3NFT = await dataSource.getRepository(Asset).save({
      chainId: chainEvm.id,
      type: AssetType.NFT,
      name: 'Nex3 NFT',
      symbol: 'NEX3NFT',
      image: 'https://example.com/image.png',
      description: 'This is a nex3 nft',
      contractAddress: '******************************************',
      isStableToken: false,
      isNativeToken: false,
      priceUsd: '1.00',
      priceVnd: '24000',
    });

    const assetNex3Voucher = await dataSource.getRepository(Asset).save({
      chainId: chainEvm.id,
      type: AssetType.NFT_VOUCHER,
      name: 'Nex3 NFT Voucher',
      symbol: 'NEX3NFTVOUCHER',
      image: 'https://example.com/image.png',
      description: 'This is a nex3 nft voucher',
      contractAddress: '******************************************',
      isStableToken: false,
      isNativeToken: false,
      priceUsd: '1.00',
      priceVnd: '24000',
    });

    // Sponsor
    const sponsorNex3 = await dataSource.getRepository(Sponsor).save({
      name: 'Nex3 Sponsor',
    });

    // Sponsor Asset
    const sponsorAssetNex3 = await dataSource.getRepository(SponsorAsset).save({
      sponsorId: sponsorNex3.id,
      assetId: assetNex3.id,
      totalReward: '100',
      quantity: 0,
      remainReward: '100',
    });
    const sponsorAssetNex3NFT = await dataSource
      .getRepository(SponsorAsset)
      .save({
        sponsorId: sponsorNex3.id,
        assetId: assetNex3NFT.id,
        totalReward: '100',
        quantity: 100,
        remainReward: '100',
      });

    return {
      brandNex3,
      chainEvm,
      hotwalletEvm,
      assetNex3,
      assetNex3NFT,
      assetNex3Voucher,
      sponsorNex3,
      sponsorAssetNex3,
      sponsorAssetNex3NFT,
    };
  };

  const clearTestData = async () => {
    const queryRunner = dataSource.createQueryRunner();
    await queryRunner.query(`SET FOREIGN_KEY_CHECKS = 0`);

    const tables = dataSource.entityMetadatas.map((e) => e.tableName);
    for (const table of tables) {
      await queryRunner.query(`TRUNCATE TABLE \`${table}\``);
    }

    await queryRunner.query(`SET FOREIGN_KEY_CHECKS = 1`);
    await queryRunner.release();

    jest.clearAllMocks();
    jest.resetAllMocks();
  };

  const createArticle = async (data: { brand: Brand; point: string }) => {
    const { brand, point } = data;
    const article = await dataSource.getRepository(Article).save({
      brandId: brand.id,
      point,
      title: 'Test Article',
      category: ArticleCategory.GENERAL,
      imageUrl: 'https://example.com/article.png',
      content: `This is the content. Lorem ipsum dolor sit amet, consectetur adipiscing elit.`,
      readTime: `2 mins`,
    });
    expect(article.id).toBeDefined();
    return article;
  };

  const createUserArticleAnswer = async (data: {
    user: User;
    article: Article;
  }) => {
    const { user, article } = data;
    const userArticleAnswer = await dataSource
      .getRepository(UserArticleAnswer)
      .save({
        userId: user.id,
        articleId: article.id,
      });
    expect(userArticleAnswer.id).toBeDefined();
    return userArticleAnswer;
  };

  const createUser = async (data: { point?: string }) => {
    const { point } = data;
    const user = await dataSource.getRepository(User).save({
      email: `test-${Date.now().toString()}@demo.com`,
      status: UserStatus.ACTIVE,
      point: point || '0',
    });
    expect(user.id).toBeDefined();
    return user;
  };

  const createUserChain = async (data: { user: User; chain: Chain }) => {
    const { user, chain } = data;
    const userChain = await dataSource.getRepository(UserChain).save({
      userId: user.id,
      chainId: chain.id,
      walletAddress: `0x${Date.now().toString().valueOf()}`,
      privateKey: Utils.encrypt('0xabcdef1234567890'),
    });
    expect(userChain.id).toBeDefined();
    return userChain;
  };

  const createUserAsset = async (data: {
    user: User;
    asset: Asset;
    balance: string;
  }) => {
    const { user, asset, balance } = data;
    const userAsset = await dataSource.getRepository(UserAsset).save({
      userId: user.id,
      assetId: asset.id,
      balance,
      status: UserAssetStatus.REDEEMED,
    });
    expect(userAsset.id).toBeDefined();
    return userAsset;
  };

  const createUserAssetNFT = async (data: { asset: Asset }) => {
    const { asset } = data;
    const userAsset = await dataSource.getRepository(UserAsset).save({
      tokenId: new Date().valueOf().toString(),
      assetId: asset.id,
      balance: '1',
      status: UserAssetStatus.UNREDEEMED,
    });
    expect(userAsset.id).toBeDefined();
    return userAsset;
  };

  const createUserAssetVoucher = async (data: { asset: Asset }) => {
    const { asset } = data;
    const userAsset = await dataSource.getRepository(UserAsset).save({
      tokenId: new Date().valueOf().toString(),
      assetId: asset.id,
      balance: '1',
      status: UserAssetStatus.UNREDEEMED,
      code: `CODE-${Date.now().valueOf()}`,
      validFrom: moment().toDate(),
      validTo: moment().add(1, 'month').toDate(),
    });
    expect(userAsset.id).toBeDefined();
    return userAsset;
  };

  const createQuest = async (data: {
    point: string;
    brand: Brand;
    startDate: Date;
    toDate: Date;
  }) => {
    const { point, brand, startDate, toDate } = data;
    const quest = await dataSource.getRepository(Quest).save({
      point,
      claimedPoint: '0',
      title: 'First Quest',
      imageUrl: 'https://example.com/quest-image.png',
      description: 'Complete this quest to earn rewards.',
      startDate,
      toDate,
      level: QuestLevel.EASY,
      totalParticipants: 0,
      status: QuestStatus.ACTIVE,
      brandId: brand.id,
    });
    expect(quest.id).toBeDefined();
    return quest;
  };

  const createUserQuest = async (data: { user: User; quest: Quest }) => {
    const { user, quest } = data;
    const userQuest = await dataSource.getRepository(UserQuest).save({
      userId: user.id,
      questId: quest.id,
      status: UserQuestStatus.UNCLAIMED,
      claimedAt: null,
    });
    expect(userQuest.id).toBeDefined();
    return userQuest;
  };

  const createRedeemItem = async (data: {
    chain: Chain;
    brand: Brand;
    redeemItemData: Partial<RedeemItem>;
  }) => {
    const { chain, brand, redeemItemData } = data;
    const redeemItem = await dataSource.getRepository(RedeemItem).save({
      name: 'NEX3 Voucher Pack',
      chainId: chain.id,
      brandId: brand.id,
      point: redeemItemData.point,
      totalReward: redeemItemData.totalReward,
      remainReward: redeemItemData.remainReward,
      status: RedeemItemStatus.ACTIVE,
      type: redeemItemData.type,
      redeemedAmount: redeemItemData.redeemedAmount || '1',
      paidType: redeemItemData.paidType,
      assetId: redeemItemData.assetId,
      sponsorId: redeemItemData.sponsorId,
      sponsorAssetId: redeemItemData.sponsorAssetId,
    });
    expect(redeemItem.id).toBeDefined();
    return redeemItem;
  };

  return {
    app,
    module,
    dataSource,
    redis,
    clearTestData,
    initTestData,
    createArticle,
    createUserArticleAnswer,
    createUser,
    createUserChain,
    createUserAsset,
    createUserAssetNFT,
    createUserAssetVoucher,
    createQuest,
    createUserQuest,
    createRedeemItem,
  };
}
