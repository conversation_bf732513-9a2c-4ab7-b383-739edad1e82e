/* eslint-disable max-lines-per-function */
jest.setTimeout(60000);
import { INestApplication } from '@nestjs/common';
import { Redis } from 'ioredis';
import { CommonService } from 'src/common-services/common.service';
import { Utils } from 'src/common/utils';
import {
  PaidType,
  RedeemItem,
  RedeemItemType,
} from 'src/entities/redeem-item.entity';
import {
  Transaction,
  TransactionStatus,
  TransactionSubType,
  TransactionType,
} from 'src/entities/transaction.entity';
import { User } from 'src/entities/user.entity';
import { QueueService } from 'src/queue/queue.service';
import { TransactionService } from 'src/transactions/transaction.service';
import { DataSource } from 'typeorm';
import { InitTestData, setupTestApp, TestUtils } from './test-utils';
import { UserAsset, UserAssetStatus } from 'src/entities/user-asset.entity';
import { HotWallet } from 'src/entities/hot-wallet.entity';
import { Web3SignedTransaction } from 'src/web3/web3.interface';
import { SponsorAsset } from 'src/entities/sponsor-asset.entity';
import { Asset } from 'src/entities/asset.entity';

describe('TransactionService: Send Token', () => {
  let testUtils: TestUtils;
  let app: INestApplication;
  let dataSource: DataSource;
  let commonService: CommonService;
  let queueService: QueueService;
  let transactionService: TransactionService;
  let redis: Redis;
  let testData: InitTestData;

  beforeAll(async () => {
    testUtils = await setupTestApp();
    app = testUtils.app;
    dataSource = testUtils.dataSource;
    redis = testUtils.redis;

    commonService = testUtils.module.get(CommonService);
    queueService = testUtils.module.get(QueueService);
    transactionService = testUtils.module.get(TransactionService);
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    await dataSource.queryResultCache?.clear();
    await redis.flushdb();
    await testUtils.clearTestData();
    jest.clearAllMocks();
    testData = await testUtils.initTestData();
  });

  const redeemAsset = async (data: { user: User; redeemItem: RedeemItem }) => {
    const { user, redeemItem } = data;
    const transaction = await transactionService.createTransaction(
      {
        id: user.id,
        brandId: redeemItem.brandId,
        email: user.email,
      },
      {
        type: TransactionType.REDEEM,
        subType: TransactionSubType.REDEEM_ITEM,
        redeemItemId: redeemItem.id,
      } as any,
    );
    expect(transaction.id).toBeDefined();
    return transaction;
  };

  const sendToken = async (data: {
    sender: User;
    receiver: User;
    asset: Asset;
    amount: string;
  }) => {
    const { sender, receiver, asset, amount } = data;
    const transaction = await transactionService.createTransaction(
      {
        id: sender.id,
        brandId: 1,
        email: sender.email,
      },
      {
        type: TransactionType.TRANSFER,
        subType: TransactionSubType.ASSET,
        assetId: asset.id,
        amount: amount,
        receiverEmail: receiver.email,
      } as any,
    );
    expect(transaction.id).toBeDefined();
    return transaction;
  };

  const mockWeb3TransactionSuccess = async () => {
    // Mock web3 transaction
    const mockCreateSignedTransaction = jest.fn().mockResolvedValue({
      signedTx: '0xabc123',
      txHash: '0xabc123',
      hotwallet: testData.hotwalletEvm,
    } as unknown as Web3SignedTransaction);
    const mockSendSignedTransaction = jest.fn().mockResolvedValue(true);

    const mockWeb3 = {
      createSignedTransaction: mockCreateSignedTransaction,
      sendSignedTransaction: mockSendSignedTransaction,
    } as any;
    jest.spyOn(commonService, 'getWeb3ByChain').mockResolvedValue(mockWeb3);
    jest.spyOn(commonService, 'getWeb3ByChainId').mockResolvedValue(mockWeb3);

    // Mock queue service
    const mockAddTransactionQueue = jest.fn().mockResolvedValue(true);
    jest
      .spyOn(queueService, 'addTransactionQueue')
      .mockImplementation(mockAddTransactionQueue);
  };

  const mockWeb3TransactionFail = async () => {
    // Mock web3 transaction
    const mockCreateSignedTransaction = jest.fn().mockResolvedValue({
      signedTx: '0xabc123',
      txHash: '0xabc123',
      hotwallet: testData.hotwalletEvm,
    } as unknown as Web3SignedTransaction);
    const mockSendSignedTransaction = jest.fn().mockImplementation(() => {
      throw new Error('redeem failed');
    });

    const mockWeb3 = {
      createSignedTransaction: mockCreateSignedTransaction,
      sendSignedTransaction: mockSendSignedTransaction,
    } as any;
    jest.spyOn(commonService, 'getWeb3ByChain').mockResolvedValue(mockWeb3);
    jest.spyOn(commonService, 'getWeb3ByChainId').mockResolvedValue(mockWeb3);
  };

  it('nên cập nhật số dư khi gửi token thành công', async () => {
    const sender = await testUtils.createUser({ point: '1000' });
    const senderUserChain = await testUtils.createUserChain({
      user: sender,
      chain: testData.chainEvm,
    });
    const senderUserAsset = await testUtils.createUserAsset({
      user: sender,
      asset: testData.assetNex3,
      balance: '100',
    });
    const receiver = await testUtils.createUser({ point: '1000' });
    const receiverUserChain = await testUtils.createUserChain({
      user: receiver,
      chain: testData.chainEvm,
    });

    // Redeem asset
    mockWeb3TransactionSuccess();
    const transaction = await sendToken({
      sender,
      receiver,
      asset: testData.assetNex3,
      amount: '10',
    });
    let updatedTransaction = await dataSource
      .getRepository(Transaction)
      .findOneBy({
        id: transaction.id,
      });
    expect(updatedTransaction).toBeDefined();
    expect(updatedTransaction.id).toBeDefined();
    expect(updatedTransaction.type).toBe(TransactionType.TRANSFER);
    expect(updatedTransaction.subType).toBe(TransactionSubType.ASSET);
    expect(updatedTransaction.chainId).toBe(testData.chainEvm.id);
    expect(updatedTransaction.fromUserId).toBe(sender.id);
    expect(updatedTransaction.toUserId).toBe(receiver.id);
    expect(updatedTransaction.brandId).toBe(testData.brandNex3.id);
    expect(updatedTransaction.assetId).toBe(testData.assetNex3.id);
    expect(Number(updatedTransaction.point)).toBe(10);
    expect(updatedTransaction.status).toBe(TransactionStatus.PROCESSING);

    // Kiểm tra số dư của sender đã được cập nhật đúng
    const senderUserAssetUpdated = await dataSource
      .getRepository(UserAsset)
      .findOneBy({ userId: sender.id, assetId: testData.assetNex3.id });
    expect(Number(senderUserAssetUpdated.balance)).toBe(90); // 100 - 10 = 90

    // Kiểm tra số dư của receiver đã được cập nhật đúng
    const receiverUserAssetUpdated = await dataSource
      .getRepository(UserAsset)
      .findOneBy({ userId: receiver.id, assetId: testData.assetNex3.id });
    expect(Number(receiverUserAssetUpdated.balance)).toBe(10); // 0 + 10 = 10

    // Sau khi onchain thành công
    await commonService.updateTransactionSendAsset(updatedTransaction, {
      txHash: '0xabc123',
      gasUsed: Utils.toBigNumber(21000),
      hotWallet: testData.hotwalletEvm.walletAddress,
      status: true,
    });

    // Kiểm tra transaction đã được cập nhật đúng
    updatedTransaction = await dataSource.getRepository(Transaction).findOneBy({
      id: transaction.id,
    });
    expect(updatedTransaction.status).toBe(TransactionStatus.SUCCESS);
    expect(updatedTransaction.txHash).toBe('0xabc123');
    expect(Utils.toBigNumber(updatedTransaction.gasUsed).toString()).toBe(
      Utils.toBigNumber(21000).toString(),
    );
    expect(updatedTransaction.hotWalletId).toBe(testData.hotwalletEvm.id);
  });

  it('nên cập nhật số dư khi gửi token thất bại', async () => {
    const sender = await testUtils.createUser({ point: '1000' });
    const senderUserChain = await testUtils.createUserChain({
      user: sender,
      chain: testData.chainEvm,
    });
    const senderUserAsset = await testUtils.createUserAsset({
      user: sender,
      asset: testData.assetNex3,
      balance: '100',
    });
    const receiver = await testUtils.createUser({ point: '1000' });
    const receiverUserChain = await testUtils.createUserChain({
      user: receiver,
      chain: testData.chainEvm,
    });

    // Redeem asset
    mockWeb3TransactionFail();
    try {
      const transaction = await sendToken({
        sender,
        receiver,
        asset: testData.assetNex3,
        amount: '10',
      });
    } catch (error) {}
    const updatedTransaction = await dataSource
      .getRepository(Transaction)
      .createQueryBuilder('tx')
      .getOne();
    expect(updatedTransaction).toBeDefined();
    expect(updatedTransaction.id).toBeDefined();
    expect(updatedTransaction.type).toBe(TransactionType.TRANSFER);
    expect(updatedTransaction.subType).toBe(TransactionSubType.ASSET);
    expect(updatedTransaction.chainId).toBe(testData.chainEvm.id);
    expect(updatedTransaction.fromUserId).toBe(sender.id);
    expect(updatedTransaction.toUserId).toBe(receiver.id);
    expect(updatedTransaction.brandId).toBe(testData.brandNex3.id);
    expect(updatedTransaction.assetId).toBe(testData.assetNex3.id);
    expect(Number(updatedTransaction.point)).toBe(10);
    expect(updatedTransaction.status).toBe(TransactionStatus.FAILED);

    // Kiểm tra số dư của sender không thay đổi
    const senderUserAssetUpdated = await dataSource
      .getRepository(UserAsset)
      .findOneBy({ userId: sender.id, assetId: testData.assetNex3.id });
    expect(Number(senderUserAssetUpdated.balance)).toBe(100);

    // Kiểm tra số dư của receiver không thay đổi
    const receiverUserAssetUpdated = await dataSource
      .getRepository(UserAsset)
      .findOneBy({ userId: receiver.id, assetId: testData.assetNex3.id });
    expect(Number(receiverUserAssetUpdated.balance)).toBe(0);
  });

  it('nên cập nhật số dư khi gửi token thành công (trường hợp user đã có sẵn token)', async () => {
    const sender = await testUtils.createUser({ point: '1000' });
    const senderUserChain = await testUtils.createUserChain({
      user: sender,
      chain: testData.chainEvm,
    });
    const senderUserAsset = await testUtils.createUserAsset({
      user: sender,
      asset: testData.assetNex3,
      balance: '100',
    });
    const receiver = await testUtils.createUser({ point: '1000' });
    const receiverUserChain = await testUtils.createUserChain({
      user: receiver,
      chain: testData.chainEvm,
    });
    const receiverUserAsset = await testUtils.createUserAsset({
      user: receiver,
      asset: testData.assetNex3,
      balance: '10',
    });

    // Redeem asset
    mockWeb3TransactionSuccess();
    const transaction = await sendToken({
      sender,
      receiver,
      asset: testData.assetNex3,
      amount: '10',
    });

    // Kiểm tra số dư của sender đã được cập nhật đúng
    const senderUserAssetUpdated = await dataSource
      .getRepository(UserAsset)
      .findOneBy({ userId: sender.id, assetId: testData.assetNex3.id });
    expect(Number(senderUserAssetUpdated.balance)).toBe(90); // 100 - 10 = 90

    // Kiểm tra số dư của receiver đã được cập nhật đúng
    const receiverUserAssetUpdated = await dataSource
      .getRepository(UserAsset)
      .findOneBy({ userId: receiver.id, assetId: testData.assetNex3.id });
    expect(Number(receiverUserAssetUpdated.balance)).toBe(20); // 10 + 10 = 20
  });

  it('nên cập nhật số dư khi gửi token 3 lần liên tiếp', async () => {
    const sender = await testUtils.createUser({ point: '1000' });
    const senderUserChain = await testUtils.createUserChain({
      user: sender,
      chain: testData.chainEvm,
    });
    const senderUserAsset = await testUtils.createUserAsset({
      user: sender,
      asset: testData.assetNex3,
      balance: '10',
    });
    const receiver = await testUtils.createUser({ point: '1000' });
    const receiverUserChain = await testUtils.createUserChain({
      user: receiver,
      chain: testData.chainEvm,
    });

    // Redeem asset
    mockWeb3TransactionSuccess();
    const promises = [];
    for (let index = 0; index < 3; index++) {
      promises.push(
        sendToken({
          sender,
          receiver,
          asset: testData.assetNex3,
          amount: '5',
        }),
      );
    }
    await Promise.allSettled(promises);
    const transactions = await dataSource
      .getRepository(Transaction)
      .createQueryBuilder('tx')
      .getMany();
    expect(transactions.length).toBe(2);

    // Kiểm tra số dư của sender đã được cập nhật đúng
    const senderUserAssetUpdated = await dataSource
      .getRepository(UserAsset)
      .findOneBy({ userId: sender.id, assetId: testData.assetNex3.id });
    expect(Number(senderUserAssetUpdated.balance)).toBe(0); // 10 - 5 - 5 = 0

    // Kiểm tra số dư của receiver đã được cập nhật đúng
    const receiverUserAssetUpdated = await dataSource
      .getRepository(UserAsset)
      .findOneBy({ userId: receiver.id, assetId: testData.assetNex3.id });
    expect(Number(receiverUserAssetUpdated.balance)).toBe(10); // 0 + 5 + 5 = 10
  });

  it('nên cập nhật số dư khi user 1 chuyển token cho user 2, user 3 chuyển token cho user 1 và user 2', async () => {
    const user1 = await testUtils.createUser({ point: '1000' });
    const user1UserChain = await testUtils.createUserChain({
      user: user1,
      chain: testData.chainEvm,
    });
    const user1UserAsset = await testUtils.createUserAsset({
      user: user1,
      asset: testData.assetNex3,
      balance: '10',
    });
    const user2 = await testUtils.createUser({ point: '1000' });
    const user2UserChain = await testUtils.createUserChain({
      user: user2,
      chain: testData.chainEvm,
    });
    const user3 = await testUtils.createUser({ point: '1000' });
    const user3UserChain = await testUtils.createUserChain({
      user: user3,
      chain: testData.chainEvm,
    });
    const user3UserAsset = await testUtils.createUserAsset({
      user: user3,
      asset: testData.assetNex3,
      balance: '10',
    });

    // Redeem asset
    mockWeb3TransactionSuccess();
    const promises = [];
    promises.push(
      sendToken({
        sender: user1,
        receiver: user2,
        asset: testData.assetNex3,
        amount: '10',
      }),
    );
    promises.push(
      sendToken({
        sender: user3,
        receiver: user1,
        asset: testData.assetNex3,
        amount: '5',
      }),
    );
    promises.push(
      sendToken({
        sender: user3,
        receiver: user2,
        asset: testData.assetNex3,
        amount: '1',
      }),
    );
    await Promise.allSettled(promises);

    // Kiểm tra số dư của user1 đã được cập nhật đúng
    const user1UserAssetUpdated = await dataSource
      .getRepository(UserAsset)
      .findOneBy({ userId: user1.id, assetId: testData.assetNex3.id });
    expect(Number(user1UserAssetUpdated.balance)).toBe(5); // 10 - 10 + 5 = 5

    // Kiểm tra số dư của user2 đã được cập nhật đúng
    const user2UserAssetUpdated = await dataSource
      .getRepository(UserAsset)
      .findOneBy({ userId: user2.id, assetId: testData.assetNex3.id });
    expect(Number(user2UserAssetUpdated.balance)).toBe(11); // 0 + 10 + 1 = 11

    // Kiểm tra số dư của user3 đã được cập nhật đúng
    const user3UserAssetUpdated = await dataSource
      .getRepository(UserAsset)
      .findOneBy({ userId: user3.id, assetId: testData.assetNex3.id });
    expect(Number(user3UserAssetUpdated.balance)).toBe(4); // 10 - 5 - 1 = 4
  });

  it('nên cập nhật số dư khi gửi token và nhận token đồng thời', async () => {
    const redeemItem = await testUtils.createRedeemItem({
      chain: testData.chainEvm,
      brand: testData.brandNex3,
      redeemItemData: {
        type: RedeemItemType.ASSET,
        paidType: PaidType.NEX3_XP,
        totalReward: '100',
        remainReward: '100',
        point: '50',
        assetId: testData.assetNex3.id,
        sponsorId: testData.sponsorNex3.id,
        sponsorAssetId: testData.sponsorAssetNex3.id,
        redeemedAmount: '1',
      },
    });
    const createSender = async (index: number) => {
      const sender = await testUtils.createUser({ point: '1000' });
      const senderUserChain = await testUtils.createUserChain({
        user: sender,
        chain: testData.chainEvm,
      });
      const senderUserAsset = await testUtils.createUserAsset({
        user: sender,
        asset: testData.assetNex3,
        balance: '10',
      });
      return { sender, senderUserChain, senderUserAsset };
    };
    // create 5 sender
    const senders = [];
    for (let index = 0; index < 5; index++) {
      const { sender, senderUserChain, senderUserAsset } =
        await createSender(index);
      senders.push({ sender, senderUserChain, senderUserAsset });
    }
    // create receiver
    const receiver1 = await testUtils.createUser({ point: '1000' });
    const receiverUserChain1 = await testUtils.createUserChain({
      user: receiver1,
      chain: testData.chainEvm,
    });

    // Redeem asset
    mockWeb3TransactionSuccess();
    const promises = [];
    promises.push(
      redeemAsset({
        user: receiver1,
        redeemItem,
      }),
    );
    for (const sender of senders) {
      promises.push(
        sendToken({
          sender: sender.sender,
          receiver: receiver1,
          asset: testData.assetNex3,
          amount: '1',
        }),
      );
    }
    await Promise.all(promises);

    // Kiểm tra số dư của sender đã được cập nhật đúng
    for (const sender of senders) {
      const senderUserAssetUpdated = await dataSource
        .getRepository(UserAsset)
        .findOneBy({
          userId: sender.sender.id,
          assetId: testData.assetNex3.id,
        });
      expect(Number(senderUserAssetUpdated.balance)).toBe(9); // 10 - 1 = 9
    }

    // Kiểm tra số dư của receiver đã được cập nhật đúng
    const receiverUserAssetUpdated1 = await dataSource
      .getRepository(UserAsset)
      .findOneBy({ userId: receiver1.id, assetId: testData.assetNex3.id });
    expect(Number(receiverUserAssetUpdated1.balance)).toBe(6); // 5 (from sender) + 1 (redeem) = 6
  });
});
