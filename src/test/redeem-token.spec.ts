/* eslint-disable max-lines-per-function */
jest.setTimeout(60000);
import { INestApplication } from '@nestjs/common';
import { Redis } from 'ioredis';
import { CommonService } from 'src/common-services/common.service';
import { Utils } from 'src/common/utils';
import {
  PaidType,
  RedeemItem,
  RedeemItemType,
} from 'src/entities/redeem-item.entity';
import {
  Transaction,
  TransactionStatus,
  TransactionSubType,
  TransactionType,
} from 'src/entities/transaction.entity';
import { User } from 'src/entities/user.entity';
import { QueueService } from 'src/queue/queue.service';
import { TransactionService } from 'src/transactions/transaction.service';
import { DataSource } from 'typeorm';
import { InitTestData, setupTestApp, TestUtils } from './test-utils';
import { UserAsset, UserAssetStatus } from 'src/entities/user-asset.entity';
import { HotWallet } from 'src/entities/hot-wallet.entity';
import { Web3SignedTransaction } from 'src/web3/web3.interface';
import { SponsorAsset } from 'src/entities/sponsor-asset.entity';

describe('TransactionService: Redeem Token', () => {
  let testUtils: TestUtils;
  let app: INestApplication;
  let dataSource: DataSource;
  let commonService: CommonService;
  let queueService: QueueService;
  let transactionService: TransactionService;
  let redis: Redis;
  let testData: InitTestData;

  beforeAll(async () => {
    testUtils = await setupTestApp();
    app = testUtils.app;
    dataSource = testUtils.dataSource;
    redis = testUtils.redis;

    commonService = testUtils.module.get(CommonService);
    queueService = testUtils.module.get(QueueService);
    transactionService = testUtils.module.get(TransactionService);
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    await dataSource.queryResultCache?.clear();
    await redis.flushdb();
    await testUtils.clearTestData();
    jest.clearAllMocks();
    testData = await testUtils.initTestData();
  });

  const redeemAsset = async (data: { user: User; redeemItem: RedeemItem }) => {
    const { user, redeemItem } = data;
    const transaction = await transactionService.createTransaction(
      {
        id: user.id,
        brandId: redeemItem.brandId,
        email: user.email,
      },
      {
        type: TransactionType.REDEEM,
        subType: TransactionSubType.REDEEM_ITEM,
        redeemItemId: redeemItem.id,
      } as any,
    );
    expect(transaction.id).toBeDefined();
    return transaction;
  };

  const mockWeb3TransactionSuccess = async () => {
    // Mock web3 transaction
    const mockCreateSignedTransaction = jest.fn().mockResolvedValue({
      signedTx: '0xabc123',
      txHash: '0xabc123',
      hotwallet: testData.hotwalletEvm,
    } as unknown as Web3SignedTransaction);
    const mockSendSignedTransaction = jest.fn().mockResolvedValue(true);

    const mockWeb3 = {
      createSignedTransaction: mockCreateSignedTransaction,
      sendSignedTransaction: mockSendSignedTransaction,
    } as any;
    jest.spyOn(commonService, 'getWeb3ByChain').mockResolvedValue(mockWeb3);
    jest.spyOn(commonService, 'getWeb3ByChainId').mockResolvedValue(mockWeb3);

    // Mock queue service
    const mockAddTransactionQueue = jest.fn().mockResolvedValue(true);
    jest
      .spyOn(queueService, 'addTransactionQueue')
      .mockImplementation(mockAddTransactionQueue);
  };

  const mockWeb3TransactionFail = async () => {
    // Mock web3 transaction
    const mockCreateSignedTransaction = jest.fn().mockResolvedValue({
      signedTx: '0xabc123',
      txHash: '0xabc123',
      hotwallet: testData.hotwalletEvm,
    } as unknown as Web3SignedTransaction);
    const mockSendSignedTransaction = jest.fn().mockImplementation(() => {
      throw new Error('redeem failed');
    });

    const mockWeb3 = {
      createSignedTransaction: mockCreateSignedTransaction,
      sendSignedTransaction: mockSendSignedTransaction,
    } as any;
    jest.spyOn(commonService, 'getWeb3ByChain').mockResolvedValue(mockWeb3);
    jest.spyOn(commonService, 'getWeb3ByChainId').mockResolvedValue(mockWeb3);
  };

  describe('đổi token bằng xp point', () => {
    it('nên cập nhật số dư khi nhận đổi token thành công', async () => {
      const redeemItem = await testUtils.createRedeemItem({
        chain: testData.chainEvm,
        brand: testData.brandNex3,
        redeemItemData: {
          type: RedeemItemType.ASSET,
          paidType: PaidType.NEX3_XP,
          totalReward: '100',
          remainReward: '100',
          point: '50',
          assetId: testData.assetNex3.id,
          sponsorId: testData.sponsorNex3.id,
          sponsorAssetId: testData.sponsorAssetNex3.id,
          redeemedAmount: '10',
        },
      });
      const user = await testUtils.createUser({ point: '1000' });
      const userChain = await testUtils.createUserChain({
        user,
        chain: testData.chainEvm,
      });

      // Redeem asset
      mockWeb3TransactionSuccess();
      const transaction = await redeemAsset({ user, redeemItem });
      let updatedTransaction = await dataSource
        .getRepository(Transaction)
        .findOneBy({
          id: transaction.id,
        });
      expect(updatedTransaction).toBeDefined();
      expect(updatedTransaction.id).toBeDefined();
      expect(updatedTransaction.type).toBe(TransactionType.REDEEM);
      expect(updatedTransaction.subType).toBe(TransactionSubType.REDEEM_ITEM);
      expect(updatedTransaction.chainId).toBe(redeemItem.chainId);
      expect(updatedTransaction.redeemItemId).toBe(redeemItem.id);
      expect(updatedTransaction.fromUserId).toBe(user.id);
      expect(updatedTransaction.toUserId).toBe(user.id);
      expect(updatedTransaction.brandId).toBe(testData.brandNex3.id);
      expect(updatedTransaction.assetId).toBe(redeemItem.assetId);
      expect(Number(updatedTransaction.point)).toBe(50);
      expect(Number(updatedTransaction.redeemedAmount)).toBe(10);
      expect(updatedTransaction.status).toBe(TransactionStatus.PROCESSING);

      // Kiểm tra số dư của user đã được cập nhật đúng
      const userUpdated = await dataSource.getRepository(User).findOneBy({
        id: user.id,
      });

      expect(Number(userUpdated.point)).toBe(950); // Số dư ban đầu là 1000, voucher tiêu tốn 50 point → 1000 - 50 = 950

      // Kiểm tra số lượng token còn lại sau khi redeem
      const redeemItemUpdated = await dataSource
        .getRepository(RedeemItem)
        .findOneBy({ id: redeemItem.id });
      expect(redeemItemUpdated).toBeDefined();
      expect(Number(redeemItemUpdated.remainReward)).toBe(90); // 100 - 10 = 90

      // Kiểm tra userAsset đã được cập nhật chính xác
      const userAssetUpdated = await dataSource
        .getRepository(UserAsset)
        .findOneBy({ userId: user.id, assetId: redeemItem.assetId });
      expect(userAssetUpdated).toBeDefined();
      expect(Number(userAssetUpdated.balance)).toBe(10); // 0 + 10 = 10
      expect(userAssetUpdated.status).toBe(UserAssetStatus.REDEEMED);

      // Kiểm tra sponsorAsset đã được cập nhật chính xác
      const sponsorAssetUpdated = await dataSource
        .getRepository(SponsorAsset)
        .findOneBy({ id: testData.sponsorAssetNex3.id });
      expect(sponsorAssetUpdated).toBeDefined();
      expect(Number(sponsorAssetUpdated.remainReward)).toBe(90); // 100 - 10 = 90

      // Sau khi onchain thành công
      await commonService.updateTransactionRedeem(updatedTransaction, {
        txHash: '0xabc123',
        gasUsed: Utils.toBigNumber(21000),
        hotWallet: testData.hotwalletEvm.walletAddress,
        status: true,
      });

      // Kiểm tra transaction đã được cập nhật đúng
      updatedTransaction = await dataSource
        .getRepository(Transaction)
        .findOneBy({
          id: transaction.id,
        });
      expect(updatedTransaction.status).toBe(TransactionStatus.SUCCESS);
      expect(updatedTransaction.txHash).toBe('0xabc123');
      expect(Utils.toBigNumber(updatedTransaction.gasUsed).toString()).toBe(
        Utils.toBigNumber(21000).toString(),
      );
      expect(updatedTransaction.hotWalletId).toBe(testData.hotwalletEvm.id);
    });

    it('nên cập nhật số dư khi nhận đổi token thành công (trường hợp user đã có sẵn token)', async () => {
      const redeemItem = await testUtils.createRedeemItem({
        chain: testData.chainEvm,
        brand: testData.brandNex3,
        redeemItemData: {
          type: RedeemItemType.ASSET,
          paidType: PaidType.NEX3_XP,
          totalReward: '100',
          remainReward: '100',
          point: '50',
          assetId: testData.assetNex3.id,
          sponsorId: testData.sponsorNex3.id,
          sponsorAssetId: testData.sponsorAssetNex3.id,
          redeemedAmount: '10',
        },
      });
      const user = await testUtils.createUser({ point: '1000' });
      const userChain = await testUtils.createUserChain({
        user,
        chain: testData.chainEvm,
      });
      const userAsset = await testUtils.createUserAsset({
        user,
        asset: testData.assetNex3,
        balance: '10',
      });

      // Redeem asset
      mockWeb3TransactionSuccess();
      const transaction = await redeemAsset({ user, redeemItem });
      let updatedTransaction = await dataSource
        .getRepository(Transaction)
        .findOneBy({
          id: transaction.id,
        });
      expect(updatedTransaction).toBeDefined();
      expect(updatedTransaction.id).toBeDefined();
      expect(updatedTransaction.type).toBe(TransactionType.REDEEM);
      expect(updatedTransaction.subType).toBe(TransactionSubType.REDEEM_ITEM);
      expect(updatedTransaction.chainId).toBe(redeemItem.chainId);
      expect(updatedTransaction.redeemItemId).toBe(redeemItem.id);
      expect(updatedTransaction.fromUserId).toBe(user.id);
      expect(updatedTransaction.toUserId).toBe(user.id);
      expect(updatedTransaction.brandId).toBe(testData.brandNex3.id);
      expect(updatedTransaction.assetId).toBe(redeemItem.assetId);
      expect(Number(updatedTransaction.point)).toBe(50);
      expect(Number(updatedTransaction.redeemedAmount)).toBe(10);
      expect(updatedTransaction.status).toBe(TransactionStatus.PROCESSING);

      // Kiểm tra số dư của user đã được cập nhật đúng
      const userUpdated = await dataSource.getRepository(User).findOneBy({
        id: user.id,
      });

      expect(Number(userUpdated.point)).toBe(950); // Số dư ban đầu là 1000, voucher tiêu tốn 50 point → 1000 - 50 = 950

      // Kiểm tra số lượng token còn lại sau khi redeem
      const redeemItemUpdated = await dataSource
        .getRepository(RedeemItem)
        .findOneBy({ id: redeemItem.id });
      expect(redeemItemUpdated).toBeDefined();
      expect(Number(redeemItemUpdated.remainReward)).toBe(90); // 100 - 10 = 90

      // Kiểm tra userAsset đã được cập nhật chính xác
      const userAssetUpdated = await dataSource
        .getRepository(UserAsset)
        .findOneBy({ userId: user.id, assetId: redeemItem.assetId });
      expect(userAssetUpdated).toBeDefined();
      expect(Number(userAssetUpdated.balance)).toBe(20); // 10 + 10 = 20
      expect(userAssetUpdated.status).toBe(UserAssetStatus.REDEEMED);

      // Sau khi onchain thành công
      await commonService.updateTransactionRedeem(updatedTransaction, {
        txHash: '0xabc123',
        gasUsed: Utils.toBigNumber(21000),
        hotWallet: testData.hotwalletEvm.walletAddress,
        status: true,
      });

      // Kiểm tra transaction đã được cập nhật đúng
      updatedTransaction = await dataSource
        .getRepository(Transaction)
        .findOneBy({
          id: transaction.id,
        });
      expect(updatedTransaction.status).toBe(TransactionStatus.SUCCESS);
      expect(updatedTransaction.txHash).toBe('0xabc123');
      expect(Utils.toBigNumber(updatedTransaction.gasUsed).toString()).toBe(
        Utils.toBigNumber(21000).toString(),
      );
      expect(updatedTransaction.hotWalletId).toBe(testData.hotwalletEvm.id);
    });

    it('nên cập nhật số dư khi nhận đổi token thất bại', async () => {
      const redeemItem = await testUtils.createRedeemItem({
        chain: testData.chainEvm,
        brand: testData.brandNex3,
        redeemItemData: {
          type: RedeemItemType.ASSET,
          paidType: PaidType.NEX3_XP,
          totalReward: '100',
          remainReward: '100',
          point: '50',
          assetId: testData.assetNex3.id,
          sponsorId: testData.sponsorNex3.id,
          sponsorAssetId: testData.sponsorAssetNex3.id,
          redeemedAmount: '10',
        },
      });
      const user = await testUtils.createUser({ point: '1000' });
      const userChain = await testUtils.createUserChain({
        user,
        chain: testData.chainEvm,
      });

      // Redeem asset
      mockWeb3TransactionFail();
      try {
        const transaction = await redeemAsset({ user, redeemItem });
      } catch (error) {}
      const updatedTransaction = await dataSource
        .getRepository(Transaction)
        .createQueryBuilder('transaction')
        .getOne();
      expect(updatedTransaction).toBeDefined();
      expect(updatedTransaction.id).toBeDefined();
      expect(updatedTransaction.type).toBe(TransactionType.REDEEM);
      expect(updatedTransaction.subType).toBe(TransactionSubType.REDEEM_ITEM);
      expect(updatedTransaction.chainId).toBe(redeemItem.chainId);
      expect(updatedTransaction.redeemItemId).toBe(redeemItem.id);
      expect(updatedTransaction.fromUserId).toBe(user.id);
      expect(updatedTransaction.toUserId).toBe(user.id);
      expect(updatedTransaction.brandId).toBe(testData.brandNex3.id);
      expect(updatedTransaction.assetId).toBe(redeemItem.assetId);
      expect(Number(updatedTransaction.point)).toBe(50);
      expect(Number(updatedTransaction.redeemedAmount)).toBe(10);
      expect(updatedTransaction.status).toBe(TransactionStatus.FAILED);

      // Kiểm tra số dư của user không thay đổi
      const userUpdated = await dataSource.getRepository(User).findOneBy({
        id: user.id,
      });

      expect(Number(userUpdated.point)).toBe(1000); // Số dư ban đầu là 1000, voucher không tiêu tốn point

      // Kiểm tra số lượng token còn lại sau khi redeem
      const redeemItemUpdated = await dataSource
        .getRepository(RedeemItem)
        .findOneBy({ id: redeemItem.id });
      expect(redeemItemUpdated).toBeDefined();
      expect(Number(redeemItemUpdated.remainReward)).toBe(100); // 100

      // Kiểm tra userAsset đã được cập nhật chính xác
      const userAssetUpdated = await dataSource
        .getRepository(UserAsset)
        .findOneBy({ userId: user.id, assetId: redeemItem.assetId });
      expect(userAssetUpdated).toBeDefined();
      expect(Number(userAssetUpdated.balance)).toBe(0); // 10 - 10 = 0
    });
  });
});
