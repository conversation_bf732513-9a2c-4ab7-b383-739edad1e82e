/* eslint-disable max-lines-per-function */
jest.setTimeout(60000);
import { INestApplication } from '@nestjs/common';
import { Redis } from 'ioredis';
import { CommonService } from 'src/common-services/common.service';
import { Utils } from 'src/common/utils';
import {
  PaidType,
  RedeemItem,
  RedeemItemType,
} from 'src/entities/redeem-item.entity';
import {
  Transaction,
  TransactionStatus,
  TransactionSubType,
  TransactionType,
} from 'src/entities/transaction.entity';
import { User } from 'src/entities/user.entity';
import { QueueService } from 'src/queue/queue.service';
import { TransactionService } from 'src/transactions/transaction.service';
import { DataSource } from 'typeorm';
import { InitTestData, setupTestApp, TestUtils } from './test-utils';
import { UserAsset, UserAssetStatus } from 'src/entities/user-asset.entity';
import { HotWallet } from 'src/entities/hot-wallet.entity';
import { Web3SignedTransaction } from 'src/web3/web3.interface';
import { SponsorAsset } from 'src/entities/sponsor-asset.entity';

describe('TransactionService: Redeem NFT', () => {
  let testUtils: TestUtils;
  let app: INestApplication;
  let dataSource: DataSource;
  let commonService: CommonService;
  let queueService: QueueService;
  let transactionService: TransactionService;
  let redis: Redis;
  let testData: InitTestData;

  beforeAll(async () => {
    testUtils = await setupTestApp();
    app = testUtils.app;
    dataSource = testUtils.dataSource;
    redis = testUtils.redis;

    commonService = testUtils.module.get(CommonService);
    queueService = testUtils.module.get(QueueService);
    transactionService = testUtils.module.get(TransactionService);
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    await dataSource.queryResultCache?.clear();
    await redis.flushdb();
    await testUtils.clearTestData();
    jest.clearAllMocks();
    testData = await testUtils.initTestData();
  });

  const redeemAsset = async (data: { user: User; redeemItem: RedeemItem }) => {
    const { user, redeemItem } = data;
    const transaction = await transactionService.createTransaction(
      {
        id: user.id,
        brandId: redeemItem.brandId,
        email: user.email,
      },
      {
        type: TransactionType.REDEEM,
        subType: TransactionSubType.REDEEM_ITEM,
        redeemItemId: redeemItem.id,
      } as any,
    );
    expect(transaction.id).toBeDefined();
    return transaction;
  };

  const mockWeb3TransactionSuccess = async () => {
    // Mock web3 transaction
    const mockCreateSignedTransaction = jest.fn().mockResolvedValue({
      signedTx: '0xabc123',
      txHash: '0xabc123',
      hotwallet: testData.hotwalletEvm,
    } as unknown as Web3SignedTransaction);
    const mockSendSignedTransaction = jest.fn().mockResolvedValue(true);

    const mockWeb3 = {
      createSignedTransaction: mockCreateSignedTransaction,
      sendSignedTransaction: mockSendSignedTransaction,
    } as any;
    jest.spyOn(commonService, 'getWeb3ByChain').mockResolvedValue(mockWeb3);
    jest.spyOn(commonService, 'getWeb3ByChainId').mockResolvedValue(mockWeb3);

    // Mock queue service
    const mockAddTransactionQueue = jest.fn().mockResolvedValue(true);
    jest
      .spyOn(queueService, 'addTransactionQueue')
      .mockImplementation(mockAddTransactionQueue);
  };

  const mockWeb3TransactionFail = async () => {
    // Mock web3 transaction
    const mockCreateSignedTransaction = jest.fn().mockResolvedValue({
      signedTx: '0xabc123',
      txHash: '0xabc123',
      hotwallet: testData.hotwalletEvm,
    } as unknown as Web3SignedTransaction);
    const mockSendSignedTransaction = jest.fn().mockImplementation(() => {
      throw new Error('redeem failed');
    });

    const mockWeb3 = {
      createSignedTransaction: mockCreateSignedTransaction,
      sendSignedTransaction: mockSendSignedTransaction,
    } as any;
    jest.spyOn(commonService, 'getWeb3ByChain').mockResolvedValue(mockWeb3);
    jest.spyOn(commonService, 'getWeb3ByChainId').mockResolvedValue(mockWeb3);
  };

  describe('đổi nft bằng xp point', () => {
    it('nên cập nhật số dư và nft khi nhận đổi nft thành công', async () => {
      const redeemItem = await testUtils.createRedeemItem({
        chain: testData.chainEvm,
        brand: testData.brandNex3,
        redeemItemData: {
          type: RedeemItemType.ASSET,
          paidType: PaidType.NEX3_XP,
          totalReward: '5',
          remainReward: '5',
          point: '50',
          assetId: testData.assetNex3NFT.id,
          sponsorId: testData.sponsorNex3.id,
          sponsorAssetId: testData.sponsorAssetNex3NFT.id,
        },
      });
      const user = await testUtils.createUser({ point: '1000' });
      const userChain = await testUtils.createUserChain({
        user,
        chain: testData.chainEvm,
      });

      // Tạo NFT
      const nftAsset = await testUtils.createUserAssetNFT({
        asset: testData.assetNex3NFT,
      });

      // Redeem asset
      mockWeb3TransactionSuccess();
      const transaction = await redeemAsset({ user, redeemItem });
      let updatedTransaction = await dataSource
        .getRepository(Transaction)
        .findOneBy({
          id: transaction.id,
        });
      expect(updatedTransaction).toBeDefined();
      expect(updatedTransaction.id).toBeDefined();
      expect(updatedTransaction.type).toBe(TransactionType.REDEEM);
      expect(updatedTransaction.subType).toBe(TransactionSubType.REDEEM_ITEM);
      expect(updatedTransaction.chainId).toBe(redeemItem.chainId);
      expect(updatedTransaction.redeemItemId).toBe(redeemItem.id);
      expect(updatedTransaction.fromUserId).toBe(user.id);
      expect(updatedTransaction.toUserId).toBe(user.id);
      expect(updatedTransaction.brandId).toBe(testData.brandNex3.id);
      expect(updatedTransaction.assetId).toBe(redeemItem.assetId);
      expect(updatedTransaction.nftId).toBe(nftAsset.id);
      expect(Number(updatedTransaction.point)).toBe(50);
      expect(Number(updatedTransaction.redeemedAmount)).toBe(1);
      expect(updatedTransaction.status).toBe(TransactionStatus.PROCESSING);

      // Kiểm tra số dư của user đã được cập nhật đúng
      const userUpdated = await dataSource.getRepository(User).findOneBy({
        id: user.id,
      });

      expect(Number(userUpdated.point)).toBe(950); // Số dư ban đầu là 1000, voucher tiêu tốn 50 point → 1000 - 50 = 950

      // Kiểm tra số lượng nft còn lại sau khi redeem
      const redeemItemUpdated = await dataSource
        .getRepository(RedeemItem)
        .findOneBy({ id: redeemItem.id });
      expect(redeemItemUpdated).toBeDefined();
      expect(Number(redeemItemUpdated.remainReward)).toBe(4); // 5 - 1 = 4

      // Kiểm tra nft đã được cập nhật chính xác
      let userAssetUpdated = await dataSource
        .getRepository(UserAsset)
        .findOneBy({ id: nftAsset.id });
      expect(userAssetUpdated).toBeDefined();
      expect(userAssetUpdated.status).toBe(UserAssetStatus.PROCESSING);

      // Kiểm tra sponsorAsset đã được cập nhật chính xác
      const sponsorAssetUpdated = await dataSource
        .getRepository(SponsorAsset)
        .findOneBy({ id: testData.sponsorAssetNex3NFT.id });
      expect(sponsorAssetUpdated).toBeDefined();
      expect(Number(sponsorAssetUpdated.remainReward)).toBe(99); // 100 - 1 = 99

      // Sau khi onchain thành công
      await commonService.updateTransactionRedeem(updatedTransaction, {
        txHash: '0xabc123',
        gasUsed: Utils.toBigNumber(21000),
        hotWallet: testData.hotwalletEvm.walletAddress,
        status: true,
        tokenId: nftAsset.tokenId,
      });

      // Kiểm tra transaction đã được cập nhật đúng
      updatedTransaction = await dataSource
        .getRepository(Transaction)
        .findOneBy({
          id: transaction.id,
        });
      expect(updatedTransaction.status).toBe(TransactionStatus.SUCCESS);
      expect(updatedTransaction.txHash).toBe('0xabc123');
      expect(Utils.toBigNumber(updatedTransaction.gasUsed).toString()).toBe(
        Utils.toBigNumber(21000).toString(),
      );
      expect(updatedTransaction.hotWalletId).toBe(testData.hotwalletEvm.id);

      // Kiểm tra nft đã được cập nhật đúng
      userAssetUpdated = await dataSource
        .getRepository(UserAsset)
        .findOneBy({ id: nftAsset.id });
      expect(userAssetUpdated).toBeDefined();
      expect(userAssetUpdated.status).toBe(UserAssetStatus.REDEEMED);
      expect(userAssetUpdated.userId).toBe(user.id);
    });

    it('nên cập nhật số dư và nft khi nhận đổi nft thất bại', async () => {
      const redeemItem = await testUtils.createRedeemItem({
        chain: testData.chainEvm,
        brand: testData.brandNex3,
        redeemItemData: {
          type: RedeemItemType.ASSET,
          paidType: PaidType.NEX3_XP,
          totalReward: '5',
          remainReward: '5',
          point: '50',
          assetId: testData.assetNex3NFT.id,
          sponsorId: testData.sponsorNex3.id,
          sponsorAssetId: testData.sponsorAssetNex3NFT.id,
        },
      });
      const user = await testUtils.createUser({ point: '1000' });
      const userChain = await testUtils.createUserChain({
        user,
        chain: testData.chainEvm,
      });

      // Tạo NFT
      const nftAsset = await testUtils.createUserAssetNFT({
        asset: testData.assetNex3NFT,
      });

      // Redeem asset
      mockWeb3TransactionFail();
      try {
        const transaction = await redeemAsset({ user, redeemItem });
      } catch (error) {}
      const updatedTransaction = await dataSource
        .getRepository(Transaction)
        .createQueryBuilder('transaction')
        .getOne();
      expect(updatedTransaction).toBeDefined();
      expect(updatedTransaction.id).toBeDefined();
      expect(updatedTransaction.type).toBe(TransactionType.REDEEM);
      expect(updatedTransaction.subType).toBe(TransactionSubType.REDEEM_ITEM);
      expect(updatedTransaction.chainId).toBe(redeemItem.chainId);
      expect(updatedTransaction.redeemItemId).toBe(redeemItem.id);
      expect(updatedTransaction.fromUserId).toBe(user.id);
      expect(updatedTransaction.toUserId).toBe(user.id);
      expect(updatedTransaction.brandId).toBe(testData.brandNex3.id);
      expect(Number(updatedTransaction.point)).toBe(50);
      expect(updatedTransaction.status).toBe(TransactionStatus.FAILED);

      // Kiểm tra số dư của user không thay đổi
      const userUpdated = await dataSource.getRepository(User).findOneBy({
        id: user.id,
      });
      expect(Number(userUpdated.point)).toBe(1000);

      // Kiểm tra số lượng nft còn lại không thay đổi
      const redeemItemUpdated = await dataSource
        .getRepository(RedeemItem)
        .findOneBy({ id: redeemItem.id });
      expect(redeemItemUpdated).toBeDefined();
      expect(Number(redeemItemUpdated.remainReward)).toBe(5);

      // Kiểm tra nft đã được cập nhật chính xác
      const userAssetUpdated = await dataSource
        .getRepository(UserAsset)
        .findOneBy({ id: nftAsset.id });
      expect(userAssetUpdated).toBeDefined();
      expect(userAssetUpdated.status).toBe(UserAssetStatus.UNREDEEMED);
      expect(userAssetUpdated.userId).toBe(null);
    });

    it('không nên ảnh hưởng đến số dư khi đổi nft đồng thời', async () => {
      const redeemItem = await testUtils.createRedeemItem({
        chain: testData.chainEvm,
        brand: testData.brandNex3,
        redeemItemData: {
          type: RedeemItemType.ASSET,
          paidType: PaidType.NEX3_XP,
          totalReward: '5',
          remainReward: '5',
          point: '50',
          assetId: testData.assetNex3NFT.id,
          sponsorId: testData.sponsorNex3.id,
          sponsorAssetId: testData.sponsorAssetNex3NFT.id,
        },
      });
      const user = await testUtils.createUser({ point: '1000' });
      const userChain = await testUtils.createUserChain({
        user,
        chain: testData.chainEvm,
      });

      // Tạo NFT
      const nftAsset = await testUtils.createUserAssetNFT({
        asset: testData.assetNex3NFT,
      });

      // Gọi đồng thời 5 lần
      mockWeb3TransactionSuccess();
      const concurrentCalls = Array.from({ length: 5 }).map(() =>
        redeemAsset({ user, redeemItem }),
      );
      await Promise.allSettled(concurrentCalls);

      // Kiểm tra số dư của user đã được cập nhật đúng
      const userUpdated = await dataSource.getRepository(User).findOneBy({
        id: user.id,
      });
      expect(Number(userUpdated.point)).toBe(950); // Chỉ trừ 1 lần: 1000 - 50 = 950

      // Kiểm tra số lượng voucher còn lại sau khi redeem
      const redeemItemUpdated = await dataSource
        .getRepository(RedeemItem)
        .findOneBy({ id: redeemItem.id });
      expect(redeemItemUpdated).toBeDefined();
      expect(Number(redeemItemUpdated.remainReward)).toBe(4); // 5 - 1 = 4
    });

    it('nên đổi nft cho 1 người dùng khi có 2 người dùng đồng thời đổi 1 nft', async () => {
      // Tạo nft và 3 mã nft
      const redeemItem = await testUtils.createRedeemItem({
        chain: testData.chainEvm,
        brand: testData.brandNex3,
        redeemItemData: {
          type: RedeemItemType.ASSET,
          paidType: PaidType.NEX3_XP,
          totalReward: '5',
          remainReward: '5',
          point: '50',
          assetId: testData.assetNex3NFT.id,
          sponsorId: testData.sponsorNex3.id,
          sponsorAssetId: testData.sponsorAssetNex3NFT.id,
        },
      });
      const nftAsset = await testUtils.createUserAssetNFT({
        asset: testData.assetNex3NFT,
      });

      // Tạo 2 user
      const user1 = await testUtils.createUser({ point: '1000' });
      const user2 = await testUtils.createUser({ point: '1000' });
      const userChain1 = await testUtils.createUserChain({
        user: user1,
        chain: testData.chainEvm,
      });
      const userChain2 = await testUtils.createUserChain({
        user: user2,
        chain: testData.chainEvm,
      });

      // 2 user cùng đổi nft đồng thời
      mockWeb3TransactionSuccess();
      const promise = [];
      promise.push(redeemAsset({ user: user1, redeemItem }));
      promise.push(redeemAsset({ user: user2, redeemItem }));
      const transactions = await Promise.allSettled(promise);

      // Kiểm tra chỉ có 1 transaction thành công
      const successTransaction = transactions.find(
        (t) => t.status === 'fulfilled',
      );
      expect(successTransaction).toBeDefined();
      expect(successTransaction.status).toBe('fulfilled');
    });

    it('nên cập nhật đúng khi nhiều người dùng đồng thời đổi nft', async () => {
      // Tạo nft và 3 nft
      const redeemItem = await testUtils.createRedeemItem({
        chain: testData.chainEvm,
        brand: testData.brandNex3,
        redeemItemData: {
          type: RedeemItemType.ASSET,
          paidType: PaidType.NEX3_XP,
          totalReward: '5',
          remainReward: '5',
          point: '50',
          assetId: testData.assetNex3NFT.id,
          sponsorId: testData.sponsorNex3.id,
          sponsorAssetId: testData.sponsorAssetNex3NFT.id,
        },
      });
      for (let i = 0; i < 3; i++) {
        const nftAsset = await testUtils.createUserAssetNFT({
          asset: testData.assetNex3NFT,
        });
      }

      // Tạo 4 user
      const user1 = await testUtils.createUser({ point: '1000' });
      const user2 = await testUtils.createUser({ point: '500' });
      const user3 = await testUtils.createUser({ point: '500' });
      const user4 = await testUtils.createUser({ point: '500' });
      const userChain1 = await testUtils.createUserChain({
        user: user1,
        chain: testData.chainEvm,
      });
      const userChain2 = await testUtils.createUserChain({
        user: user2,
        chain: testData.chainEvm,
      });
      const userChain3 = await testUtils.createUserChain({
        user: user3,
        chain: testData.chainEvm,
      });
      const userChain4 = await testUtils.createUserChain({
        user: user4,
        chain: testData.chainEvm,
      });

      // 4 user cùng đổi voucher đồng thời
      mockWeb3TransactionSuccess();
      const promise = [];
      promise.push(redeemAsset({ user: user1, redeemItem }));
      promise.push(redeemAsset({ user: user2, redeemItem }));
      promise.push(redeemAsset({ user: user3, redeemItem }));
      promise.push(redeemAsset({ user: user4, redeemItem }));
      await Promise.allSettled(promise);

      // Kiểm tra có 3 transaction được tạo bởi vì chỉ có 3 mã nft
      const nftAssets = await dataSource
        .getRepository(UserAsset)
        .find({ where: { assetId: redeemItem.assetId } });
      expect(nftAssets.length).toBe(3);
      expect(nftAssets[0].status).toBe(UserAssetStatus.PROCESSING);
      expect(nftAssets[1].status).toBe(UserAssetStatus.PROCESSING);
      expect(nftAssets[2].status).toBe(UserAssetStatus.PROCESSING);
      const transactions = await dataSource
        .getRepository(Transaction)
        .find({ where: { redeemItemId: redeemItem.id } });
      expect(transactions.length).toBe(3);
    });
  });

  describe('đổi nft voucher bằng xp point', () => {
    it('nên cập nhật số dư và nft khi nhận đổi nft thành công', async () => {
      const redeemItem = await testUtils.createRedeemItem({
        chain: testData.chainEvm,
        brand: testData.brandNex3,
        redeemItemData: {
          type: RedeemItemType.ASSET,
          paidType: PaidType.NEX3_XP,
          totalReward: '5',
          remainReward: '5',
          point: '50',
          assetId: testData.assetNex3Voucher.id,
          sponsorId: testData.sponsorNex3.id,
          sponsorAssetId: testData.sponsorAssetNex3NFT.id,
        },
      });
      const user = await testUtils.createUser({ point: '1000' });
      const userChain = await testUtils.createUserChain({
        user,
        chain: testData.chainEvm,
      });

      // Tạo NFT
      const nftAsset = await testUtils.createUserAssetVoucher({
        asset: testData.assetNex3Voucher,
      });

      // Redeem asset
      mockWeb3TransactionSuccess();
      const transaction = await redeemAsset({ user, redeemItem });
      let updatedTransaction = await dataSource
        .getRepository(Transaction)
        .findOneBy({
          id: transaction.id,
        });
      expect(updatedTransaction).toBeDefined();
      expect(updatedTransaction.id).toBeDefined();
      expect(updatedTransaction.type).toBe(TransactionType.REDEEM);
      expect(updatedTransaction.subType).toBe(TransactionSubType.REDEEM_ITEM);
      expect(updatedTransaction.chainId).toBe(redeemItem.chainId);
      expect(updatedTransaction.redeemItemId).toBe(redeemItem.id);
      expect(updatedTransaction.fromUserId).toBe(user.id);
      expect(updatedTransaction.toUserId).toBe(user.id);
      expect(updatedTransaction.brandId).toBe(testData.brandNex3.id);
      expect(updatedTransaction.assetId).toBe(redeemItem.assetId);
      expect(updatedTransaction.nftId).toBe(nftAsset.id);
      expect(Number(updatedTransaction.point)).toBe(50);
      expect(updatedTransaction.status).toBe(TransactionStatus.PROCESSING);

      // Kiểm tra số dư của user đã được cập nhật đúng
      const userUpdated = await dataSource.getRepository(User).findOneBy({
        id: user.id,
      });

      expect(Number(userUpdated.point)).toBe(950); // Số dư ban đầu là 1000, voucher tiêu tốn 50 point → 1000 - 50 = 950

      // Kiểm tra số lượng nft còn lại sau khi redeem
      const redeemItemUpdated = await dataSource
        .getRepository(RedeemItem)
        .findOneBy({ id: redeemItem.id });
      expect(redeemItemUpdated).toBeDefined();
      expect(Number(redeemItemUpdated.remainReward)).toBe(4); // 5 - 1 = 4

      // Kiểm tra nft đã được cập nhật chính xác
      let userAssetUpdated = await dataSource
        .getRepository(UserAsset)
        .findOneBy({ id: nftAsset.id });
      expect(userAssetUpdated).toBeDefined();
      expect(userAssetUpdated.status).toBe(UserAssetStatus.PROCESSING);

      // Sau khi onchain thành công
      await commonService.updateTransactionRedeem(updatedTransaction, {
        txHash: '0xabc123',
        gasUsed: Utils.toBigNumber(21000),
        hotWallet: testData.hotwalletEvm.walletAddress,
        status: true,
        tokenId: nftAsset.tokenId,
      });

      // Kiểm tra transaction đã được cập nhật đúng
      updatedTransaction = await dataSource
        .getRepository(Transaction)
        .findOneBy({
          id: transaction.id,
        });
      expect(updatedTransaction.status).toBe(TransactionStatus.SUCCESS);
      expect(updatedTransaction.txHash).toBe('0xabc123');
      expect(Utils.toBigNumber(updatedTransaction.gasUsed).toString()).toBe(
        Utils.toBigNumber(21000).toString(),
      );
      expect(updatedTransaction.hotWalletId).toBe(testData.hotwalletEvm.id);

      // Kiểm tra nft đã được cập nhật đúng
      userAssetUpdated = await dataSource
        .getRepository(UserAsset)
        .findOneBy({ id: nftAsset.id });
      expect(userAssetUpdated).toBeDefined();
      expect(userAssetUpdated.status).toBe(UserAssetStatus.REDEEMED);
      expect(userAssetUpdated.userId).toBe(user.id);
    });
  });

  describe('đổi nft bằng token', () => {
    it('nên cập nhật số dư và nft khi nhận đổi nft thành công', async () => {
      const redeemItem = await testUtils.createRedeemItem({
        chain: testData.chainEvm,
        brand: testData.brandNex3,
        redeemItemData: {
          type: RedeemItemType.ASSET,
          paidType: PaidType.TOKEN,
          paidAssetId: testData.assetNex3.id,
          totalReward: '5',
          remainReward: '5',
          point: '50',
          assetId: testData.assetNex3NFT.id,
          sponsorId: testData.sponsorNex3.id,
          sponsorAssetId: testData.sponsorAssetNex3NFT.id,
          redeemedAmount: '1',
        },
      });
      const user = await testUtils.createUser({ point: '1000' });
      const userChain = await testUtils.createUserChain({
        user,
        chain: testData.chainEvm,
      });
      const userAsset = await testUtils.createUserAsset({
        user,
        asset: testData.assetNex3,
        balance: '1000',
      });

      // Tạo NFT
      const nftAsset = await testUtils.createUserAssetNFT({
        asset: testData.assetNex3NFT,
      });

      // Redeem asset
      mockWeb3TransactionSuccess();
      const transaction = await redeemAsset({ user, redeemItem });
      let updatedTransaction = await dataSource
        .getRepository(Transaction)
        .findOneBy({
          id: transaction.id,
        });
      expect(updatedTransaction).toBeDefined();
      expect(updatedTransaction.id).toBeDefined();
      expect(updatedTransaction.type).toBe(TransactionType.REDEEM);
      expect(updatedTransaction.subType).toBe(TransactionSubType.REDEEM_ITEM);
      expect(updatedTransaction.chainId).toBe(redeemItem.chainId);
      expect(updatedTransaction.redeemItemId).toBe(redeemItem.id);
      expect(updatedTransaction.fromUserId).toBe(user.id);
      expect(updatedTransaction.toUserId).toBe(user.id);
      expect(updatedTransaction.brandId).toBe(testData.brandNex3.id);
      expect(updatedTransaction.assetId).toBe(redeemItem.assetId);
      expect(updatedTransaction.paidAssetId).toBe(redeemItem.paidAssetId);
      expect(updatedTransaction.nftId).toBe(nftAsset.id);
      expect(Number(updatedTransaction.point)).toBe(50);
      expect(updatedTransaction.status).toBe(TransactionStatus.PROCESSING);

      // Kiểm tra số dư của user đã được cập nhật đúng
      const userAssetUpdated = await dataSource
        .getRepository(UserAsset)
        .findOneBy({
          id: user.id,
        });

      expect(Number(userAssetUpdated.balance)).toBe(950); // Số dư ban đầu là 1000, voucher tiêu tốn 50 point → 1000 - 50 = 950

      // Kiểm tra số lượng nft còn lại sau khi redeem
      const redeemItemUpdated = await dataSource
        .getRepository(RedeemItem)
        .findOneBy({ id: redeemItem.id });
      expect(redeemItemUpdated).toBeDefined();
      expect(Number(redeemItemUpdated.remainReward)).toBe(4); // 5 - 1 = 4

      // Kiểm tra nft đã được cập nhật chính xác
      let nftAssetUpdated = await dataSource
        .getRepository(UserAsset)
        .findOneBy({ id: nftAsset.id });
      expect(nftAssetUpdated).toBeDefined();
      expect(nftAssetUpdated.status).toBe(UserAssetStatus.PROCESSING);

      // Kiểm tra sponsorAsset đã được cập nhật chính xác
      const sponsorAssetUpdated = await dataSource
        .getRepository(SponsorAsset)
        .findOneBy({ id: redeemItem.sponsorAssetId });
      expect(sponsorAssetUpdated).toBeDefined();
      expect(Number(sponsorAssetUpdated.remainReward)).toBe(99); // 100 - 1 = 99

      // Sau khi onchain thành công
      await commonService.updateTransactionRedeem(updatedTransaction, {
        txHash: '0xabc123',
        gasUsed: Utils.toBigNumber(21000),
        hotWallet: testData.hotwalletEvm.walletAddress,
        status: true,
        tokenId: nftAsset.tokenId,
      });

      // Kiểm tra transaction đã được cập nhật đúng
      updatedTransaction = await dataSource
        .getRepository(Transaction)
        .findOneBy({
          id: transaction.id,
        });
      expect(updatedTransaction.status).toBe(TransactionStatus.SUCCESS);
      expect(updatedTransaction.txHash).toBe('0xabc123');
      expect(Utils.toBigNumber(updatedTransaction.gasUsed).toString()).toBe(
        Utils.toBigNumber(21000).toString(),
      );
      expect(updatedTransaction.hotWalletId).toBe(testData.hotwalletEvm.id);

      // Kiểm tra nft đã được cập nhật đúng
      nftAssetUpdated = await dataSource
        .getRepository(UserAsset)
        .findOneBy({ id: nftAsset.id });
      expect(nftAssetUpdated).toBeDefined();
      expect(nftAssetUpdated.status).toBe(UserAssetStatus.REDEEMED);
      expect(nftAssetUpdated.userId).toBe(user.id);
      expect(nftAssetUpdated.redeemItemId).toBe(redeemItem.id);
    });

    it('nên cập nhật số dư và nft khi nhận đổi nft thất bại', async () => {
      const redeemItem = await testUtils.createRedeemItem({
        chain: testData.chainEvm,
        brand: testData.brandNex3,
        redeemItemData: {
          type: RedeemItemType.ASSET,
          paidType: PaidType.TOKEN,
          paidAssetId: testData.assetNex3.id,
          totalReward: '5',
          remainReward: '5',
          point: '50',
          assetId: testData.assetNex3NFT.id,
          sponsorId: testData.sponsorNex3.id,
          sponsorAssetId: testData.sponsorAssetNex3NFT.id,
        },
      });
      const user = await testUtils.createUser({ point: '1000' });
      const userChain = await testUtils.createUserChain({
        user,
        chain: testData.chainEvm,
      });
      const userAsset = await testUtils.createUserAsset({
        user,
        asset: testData.assetNex3,
        balance: '1000',
      });

      // Tạo NFT
      const nftAsset = await testUtils.createUserAssetNFT({
        asset: testData.assetNex3NFT,
      });

      // Redeem asset
      mockWeb3TransactionFail();
      try {
        const transaction = await redeemAsset({ user, redeemItem });
      } catch (error) {}
      const updatedTransaction = await dataSource
        .getRepository(Transaction)
        .createQueryBuilder('transaction')
        .getOne();
      expect(updatedTransaction).toBeDefined();
      expect(updatedTransaction.id).toBeDefined();
      expect(updatedTransaction.type).toBe(TransactionType.REDEEM);
      expect(updatedTransaction.subType).toBe(TransactionSubType.REDEEM_ITEM);
      expect(updatedTransaction.chainId).toBe(redeemItem.chainId);
      expect(updatedTransaction.redeemItemId).toBe(redeemItem.id);
      expect(updatedTransaction.fromUserId).toBe(user.id);
      expect(updatedTransaction.toUserId).toBe(user.id);
      expect(updatedTransaction.brandId).toBe(testData.brandNex3.id);
      expect(updatedTransaction.paidAssetId).toBe(redeemItem.paidAssetId);
      expect(Number(updatedTransaction.point)).toBe(50);
      expect(updatedTransaction.status).toBe(TransactionStatus.FAILED);

      // Kiểm tra số dư của user không thay đổi
      const userAssetUpdated = await dataSource
        .getRepository(UserAsset)
        .findOneBy({
          id: user.id,
        });
      expect(Number(userAssetUpdated.balance)).toBe(1000);

      // Kiểm tra số lượng nft còn lại không thay đổi
      const redeemItemUpdated = await dataSource
        .getRepository(RedeemItem)
        .findOneBy({ id: redeemItem.id });
      expect(redeemItemUpdated).toBeDefined();
      expect(Number(redeemItemUpdated.remainReward)).toBe(5);

      // Kiểm tra nft đã được cập nhật chính xác
      const nftAssetUpdated = await dataSource
        .getRepository(UserAsset)
        .findOneBy({ id: nftAsset.id });
      expect(nftAssetUpdated).toBeDefined();
      expect(nftAssetUpdated.status).toBe(UserAssetStatus.UNREDEEMED);
      expect(nftAssetUpdated.userId).toBe(null);
    });
  });
});
