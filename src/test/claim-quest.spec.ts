/* eslint-disable max-lines-per-function */
jest.setTimeout(60000);
import { INestApplication } from '@nestjs/common';
import { Redis } from 'ioredis';
import * as moment from 'moment';
import { Quest } from 'src/entities/quest.entity';
import {
  Transaction,
  TransactionStatus,
  TransactionSubType,
  TransactionType,
} from 'src/entities/transaction.entity';
import { UserQuest, UserQuestStatus } from 'src/entities/user-quest.entity';
import { User } from 'src/entities/user.entity';
import { TransactionService } from 'src/transactions/transaction.service';
import { DataSource } from 'typeorm';
import { InitTestData, setupTestApp, TestUtils } from './test-utils';

describe('TransactionService: Claim XP For Quest', () => {
  let testUtils: TestUtils;
  let app: INestApplication;
  let dataSource: DataSource;
  let transactionService: TransactionService;
  let redis: Redis;
  let testData: InitTestData;

  beforeAll(async () => {
    testUtils = await setupTestApp();
    app = testUtils.app;
    dataSource = testUtils.dataSource;
    redis = testUtils.redis;

    transactionService = testUtils.module.get(TransactionService);
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    await dataSource.queryResultCache?.clear();
    await redis.flushdb();
    await testUtils.clearTestData();
    jest.clearAllMocks();
    testData = await testUtils.initTestData();
  });

  const doQuest = async (data: { user: User; quest: Quest }) => {
    const { user, quest } = data;
    const transaction = await transactionService.createTransaction(
      {
        id: user.id,
        brandId: testData.brandNex3.id,
        email: user.email,
      },
      {
        type: TransactionType.EARN,
        subType: TransactionSubType.QUEST,
        questId: quest.id,
      } as any,
    );
    return transaction;
  };

  it('nên nhận thưởng thành công và cập nhật tất cả các bản ghi liên quan', async () => {
    const quest = await testUtils.createQuest({
      point: '100',
      brand: testData.brandNex3,
      startDate: moment().toDate(),
      toDate: moment().add(7, 'days').toDate(),
    });
    const user = await testUtils.createUser({ point: '1000' });
    const userQuest = await testUtils.createUserQuest({ user, quest });

    // Do quest
    const transaction = await doQuest({ user, quest });
    const updatedTransaction = await dataSource
      .getRepository(Transaction)
      .findOneBy({
        id: transaction.id,
      });
    expect(updatedTransaction).toBeDefined();
    expect(updatedTransaction.id).toBeDefined();
    expect(updatedTransaction.type).toBe(TransactionType.EARN);
    expect(updatedTransaction.subType).toBe(TransactionSubType.QUEST);
    expect(updatedTransaction.questId).toBe(quest.id);
    expect(updatedTransaction.userQuestId).toBe(userQuest.id);
    expect(updatedTransaction.fromUserId).toBe(user.id);
    expect(updatedTransaction.toUserId).toBe(user.id);
    expect(updatedTransaction.brandId).toBe(testData.brandNex3.id);
    expect(Number(updatedTransaction.point)).toBe(100);
    expect(updatedTransaction.status).toBe(TransactionStatus.SUCCESS);

    // Kiểm tra điểm của user đã được cộng
    const userUpdated = await dataSource.getRepository(User).findOneBy({
      id: user.id,
    });
    expect(userUpdated).toBeDefined();
    expect(Number(userUpdated.point)).toBe(1100); // Ban đầu 1000 + 100 điểm nhận thưởng

    // Kiểm tra trạng thái userQuest đã được cập nhật
    const userQuestUpdated = await dataSource
      .getRepository(UserQuest)
      .findOneBy({
        id: userQuest.id,
      });
    expect(userQuestUpdated.status).toBe(UserQuestStatus.CLAIMED);
    expect(userQuestUpdated.claimedAt).toBeDefined();
    expect(
      moment(userQuestUpdated.claimedAt).startOf('hour').toISOString(),
    ).toBe(moment().startOf('hour').toISOString());

    // Kiểm tra điểm đã claim của quest đã được cập nhật
    const questUpdated = await dataSource.getRepository(Quest).findOneBy({
      id: quest.id,
    });
    expect(Number(questUpdated.claimedPoint)).toBe(100); // Ban đầu 0 + 100 điểm
  });

  it('nên nhận thưởng thành công và cập nhật tất cả các bản ghi liên quan cho nhiều nhiệm vụ', async () => {
    const user = await testUtils.createUser({ point: '1000' });
    const quest1 = await testUtils.createQuest({
      point: '100',
      brand: testData.brandNex3,
      startDate: moment().toDate(),
      toDate: moment().add(7, 'days').toDate(),
    }); // Tạo quest 1
    const userQuest1 = await testUtils.createUserQuest({
      user,
      quest: quest1,
    });

    const quest2 = await testUtils.createQuest({
      point: '100',
      brand: testData.brandNex3,
      startDate: moment().toDate(),
      toDate: moment().add(7, 'days').toDate(),
    }); // Tạo quest 2
    const userQuest2 = await testUtils.createUserQuest({ user, quest: quest2 });

    await doQuest({ user, quest: quest1 }); // Claim quest 1
    await doQuest({ user, quest: quest2 }); // Claim quest 2

    // Kiểm tra điểm của user đã được cộng đúng (mỗi nhiệm vụ +100 => tổng cộng 1200)
    const userUpdated = await dataSource.getRepository(User).findOneBy({
      id: user.id,
    });
    expect(userUpdated).toBeDefined();
    expect(Number(userUpdated.point)).toBe(1200); // Ban đầu 1000 + 2*100
    // Kiểm tra trạng thái và thời gian claim của userQuest 1

    const userQuest1Updated = await dataSource
      .getRepository(UserQuest)
      .findOneBy({
        id: userQuest1.id,
      });
    expect(userQuest1Updated.status).toBe(UserQuestStatus.CLAIMED);
    expect(userQuest1Updated.claimedAt).toBeDefined();
    expect(
      moment(userQuest1Updated.claimedAt).startOf('minute').toISOString(),
    ).toBe(moment().startOf('minute').toISOString()); // Kiểm tra trạng thái và thời gian claim của userQuest 2

    const userQuest2Updated = await dataSource
      .getRepository(UserQuest)
      .findOneBy({
        id: userQuest2.id,
      });
    expect(userQuest2Updated.status).toBe(UserQuestStatus.CLAIMED);
    expect(userQuest2Updated.claimedAt).toBeDefined();
    expect(
      moment(userQuest2Updated.claimedAt).startOf('minute').toISOString(),
    ).toBe(moment().startOf('minute').toISOString()); // Kiểm tra điểm đã claim của quest 1

    const quest1Updated = await dataSource.getRepository(Quest).findOneBy({
      id: quest1.id,
    });
    expect(Number(quest1Updated.claimedPoint)).toBe(100); // Ban đầu 0 + 100
    // Kiểm tra điểm đã claim của quest 2

    const quest2Updated = await dataSource.getRepository(Quest).findOneBy({
      id: quest2.id,
    });
    expect(Number(quest2Updated.claimedPoint)).toBe(100); // Ban đầu 0 + 100
  });

  it('nên cho phép 2 người dùng làm cùng một nhiệm vụ đồng thời', async () => {
    const user1 = await testUtils.createUser({ point: '1000' });
    const user2 = await testUtils.createUser({ point: '0' });
    const quest = await testUtils.createQuest({
      point: '100',
      brand: testData.brandNex3,
      startDate: moment().toDate(),
      toDate: moment().add(7, 'days').toDate(),
    }); // tạo quest + user1
    const userQuest1 = await testUtils.createUserQuest({ user: user1, quest }); // user1 làm quest
    const userQuest2 = await testUtils.createUserQuest({ user: user2, quest }); // user2 làm quest

    // Thực hiện đồng thời 2 giao dịch
    await Promise.all([
      doQuest({ user: user1, quest }),
      doQuest({ user: user2, quest }),
    ]);

    // Kiểm tra kết quả của từng user
    const userUpdated1 = await dataSource
      .getRepository(User)
      .findOneBy({ id: user1.id });
    const userUpdated2 = await dataSource
      .getRepository(User)
      .findOneBy({ id: user2.id });

    expect(Number(userUpdated1.point)).toBe(1100);
    expect(Number(userUpdated2.point)).toBe(100);

    // Kiểm tra điểm đã claim của quest đã được cập nhật
    const questUpdated = await dataSource
      .getRepository(Quest)
      .findOneBy({ id: quest.id });
    expect(Number(questUpdated.claimedPoint)).toBe(200); // 100 mỗi người
  });

  it('nên xử lý chính xác các yêu cầu nhận thưởng đồng thời và duy trì tính toàn vẹn của số dư', async () => {
    const user1 = await testUtils.createUser({ point: '1000' });
    const quest = await testUtils.createQuest({
      point: '100',
      brand: testData.brandNex3,
      startDate: moment().toDate(),
      toDate: moment().add(7, 'days').toDate(),
    });
    const userQuest = await testUtils.createUserQuest({ user: user1, quest });

    // Gọi đồng thời 5 giao dịch để giả lập race condition
    // Dùng Promise.allSettled để đảm bảo tất cả đều được thực thi (kể cả khi một số bị lỗi)
    // Mục tiêu: chỉ 1 giao dịch thành công, các giao dịch còn lại sẽ bị từ chối
    await Promise.allSettled(
      Array.from({ length: 5 }).map(() => doQuest({ user: user1, quest })),
    );

    // Kiểm tra số dư người dùng sau khi xử lý đồng thời
    const userUpdated = await dataSource.getRepository(User).findOneBy({
      id: user1.id,
    });

    // Đảm bảo điểm chỉ được cộng 1 lần
    expect(userUpdated).toBeDefined();
    expect(Number(userUpdated.point)).toBe(1100); // 1000 ban đầu + 100 điểm thưởng

    // Kiểm tra trạng thái userQuest đã được cập nhật đúng
    const userQuestUpdated = await dataSource
      .getRepository(UserQuest)
      .findOneBy({ id: userQuest.id });

    expect(userQuestUpdated.status).toBe(UserQuestStatus.CLAIMED);
    expect(userQuestUpdated.claimedAt).toBeDefined();
    expect(
      moment(userQuestUpdated.claimedAt).startOf('minute').toISOString(),
    ).toBe(moment().startOf('minute').toISOString());

    // Kiểm tra điểm của quest cũng chỉ được cộng 1 lần
    const questUpdated = await dataSource.getRepository(Quest).findOneBy({
      id: quest.id,
    });
    expect(Number(questUpdated.claimedPoint)).toBe(100); // 0 ban đầu + 100
  });
});
