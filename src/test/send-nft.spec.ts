/* eslint-disable max-lines-per-function */
jest.setTimeout(60000);
import { INestApplication } from '@nestjs/common';
import { Redis } from 'ioredis';
import { CommonService } from 'src/common-services/common.service';
import { Utils } from 'src/common/utils';
import {
  PaidType,
  RedeemItem,
  RedeemItemType,
} from 'src/entities/redeem-item.entity';
import {
  Transaction,
  TransactionStatus,
  TransactionSubType,
  TransactionType,
} from 'src/entities/transaction.entity';
import { User } from 'src/entities/user.entity';
import { QueueService } from 'src/queue/queue.service';
import { TransactionService } from 'src/transactions/transaction.service';
import { DataSource } from 'typeorm';
import { InitTestData, setupTestApp, TestUtils } from './test-utils';
import { UserAsset, UserAssetStatus } from 'src/entities/user-asset.entity';
import { HotWallet } from 'src/entities/hot-wallet.entity';
import { Web3SignedTransaction } from 'src/web3/web3.interface';
import { SponsorAsset } from 'src/entities/sponsor-asset.entity';
import { Asset } from 'src/entities/asset.entity';

describe('TransactionService: Send NFT', () => {
  let testUtils: TestUtils;
  let app: INestApplication;
  let dataSource: DataSource;
  let commonService: CommonService;
  let queueService: QueueService;
  let transactionService: TransactionService;
  let redis: Redis;
  let testData: InitTestData;

  beforeAll(async () => {
    testUtils = await setupTestApp();
    app = testUtils.app;
    dataSource = testUtils.dataSource;
    redis = testUtils.redis;

    commonService = testUtils.module.get(CommonService);
    queueService = testUtils.module.get(QueueService);
    transactionService = testUtils.module.get(TransactionService);
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    await dataSource.queryResultCache?.clear();
    await redis.flushdb();
    await testUtils.clearTestData();
    jest.clearAllMocks();
    testData = await testUtils.initTestData();
  });

  const redeemAsset = async (data: { user: User; redeemItem: RedeemItem }) => {
    const { user, redeemItem } = data;
    const transaction = await transactionService.createTransaction(
      {
        id: user.id,
        brandId: redeemItem.brandId,
        email: user.email,
      },
      {
        type: TransactionType.REDEEM,
        subType: TransactionSubType.REDEEM_ITEM,
        redeemItemId: redeemItem.id,
      } as any,
    );
    expect(transaction.id).toBeDefined();
    return transaction;
  };

  const sendNFT = async (data: {
    sender: User;
    receiver: User;
    asset: Asset;
    amount: string;
  }) => {
    const { sender, receiver, asset, amount } = data;
    const transaction = await transactionService.createTransaction(
      {
        id: sender.id,
        brandId: 1,
        email: sender.email,
      },
      {
        type: TransactionType.TRANSFER,
        subType: TransactionSubType.ASSET,
        assetId: asset.id,
        amount: amount,
        receiverEmail: receiver.email,
      } as any,
    );
    expect(transaction.id).toBeDefined();
    return transaction;
  };

  const mockWeb3TransactionSuccess = async () => {
    // Mock web3 transaction
    const mockCreateSignedTransaction = jest.fn().mockResolvedValue({
      signedTx: '0xabc123',
      txHash: '0xabc123',
      hotwallet: testData.hotwalletEvm,
    } as unknown as Web3SignedTransaction);
    const mockSendSignedTransaction = jest.fn().mockResolvedValue(true);

    const mockWeb3 = {
      createSignedTransaction: mockCreateSignedTransaction,
      sendSignedTransaction: mockSendSignedTransaction,
    } as any;
    jest.spyOn(commonService, 'getWeb3ByChain').mockResolvedValue(mockWeb3);
    jest.spyOn(commonService, 'getWeb3ByChainId').mockResolvedValue(mockWeb3);

    // Mock queue service
    const mockAddTransactionQueue = jest.fn().mockResolvedValue(true);
    jest
      .spyOn(queueService, 'addTransactionQueue')
      .mockImplementation(mockAddTransactionQueue);
  };

  const mockWeb3TransactionFail = async () => {
    // Mock web3 transaction
    const mockCreateSignedTransaction = jest.fn().mockResolvedValue({
      signedTx: '0xabc123',
      txHash: '0xabc123',
      hotwallet: testData.hotwalletEvm,
    } as unknown as Web3SignedTransaction);
    const mockSendSignedTransaction = jest.fn().mockImplementation(() => {
      throw new Error('redeem failed');
    });

    const mockWeb3 = {
      createSignedTransaction: mockCreateSignedTransaction,
      sendSignedTransaction: mockSendSignedTransaction,
    } as any;
    jest.spyOn(commonService, 'getWeb3ByChain').mockResolvedValue(mockWeb3);
    jest.spyOn(commonService, 'getWeb3ByChainId').mockResolvedValue(mockWeb3);
  };

  it('nên cập nhật số dư khi gửi nft thành công', async () => {
    const sender = await testUtils.createUser({ point: '1000' });
    const senderUserChain = await testUtils.createUserChain({
      user: sender,
      chain: testData.chainEvm,
    });
    const senderUserAsset = await testUtils.createUserAsset({
      user: sender,
      asset: testData.assetNex3NFT,
      balance: '1',
    });
    const receiver = await testUtils.createUser({ point: '1000' });
    const receiverUserChain = await testUtils.createUserChain({
      user: receiver,
      chain: testData.chainEvm,
    });

    // Redeem asset
    mockWeb3TransactionSuccess();
    const transaction = await sendNFT({
      sender,
      receiver,
      asset: testData.assetNex3NFT,
      amount: '1',
    });
    let updatedTransaction = await dataSource
      .getRepository(Transaction)
      .findOneBy({
        id: transaction.id,
      });
    expect(updatedTransaction).toBeDefined();
    expect(updatedTransaction.id).toBeDefined();
    expect(updatedTransaction.type).toBe(TransactionType.TRANSFER);
    expect(updatedTransaction.subType).toBe(TransactionSubType.ASSET);
    expect(updatedTransaction.chainId).toBe(testData.chainEvm.id);
    expect(updatedTransaction.fromUserId).toBe(sender.id);
    expect(updatedTransaction.toUserId).toBe(receiver.id);
    expect(updatedTransaction.brandId).toBe(testData.brandNex3.id);
    expect(updatedTransaction.assetId).toBe(testData.assetNex3NFT.id);
    expect(updatedTransaction.nftId).toBe(senderUserAsset.id);
    expect(Number(updatedTransaction.point)).toBe(1);
    expect(updatedTransaction.status).toBe(TransactionStatus.PROCESSING);

    // Kiểm tra số dư của sender đã được cập nhật đúng
    const userAssetUpdated = await dataSource
      .getRepository(UserAsset)
      .findOneBy({ id: senderUserAsset.id });
    expect(userAssetUpdated.status).toBe(UserAssetStatus.PROCESSING);

    // Sau khi onchain thành công
    await commonService.updateTransactionSendAsset(updatedTransaction, {
      txHash: '0xabc123',
      gasUsed: Utils.toBigNumber(21000),
      hotWallet: testData.hotwalletEvm.walletAddress,
      status: true,
    });

    // Kiểm tra transaction đã được cập nhật đúng
    updatedTransaction = await dataSource.getRepository(Transaction).findOneBy({
      id: transaction.id,
    });
    expect(updatedTransaction.status).toBe(TransactionStatus.SUCCESS);
    expect(updatedTransaction.txHash).toBe('0xabc123');
    expect(Utils.toBigNumber(updatedTransaction.gasUsed).toString()).toBe(
      Utils.toBigNumber(21000).toString(),
    );
    expect(updatedTransaction.hotWalletId).toBe(testData.hotwalletEvm.id);

    // Kiểm tra nft đã được chuyển đi
    const receiverUserAsset = await dataSource
      .getRepository(UserAsset)
      .findOneBy({ id: senderUserAsset.id });
    expect(receiverUserAsset.userId).toBe(receiver.id);
    expect(receiverUserAsset.status).toBe(UserAssetStatus.REDEEMED);
  });

  it('nên cập nhật số dư khi gửi nft voucher thành công', async () => {
    const sender = await testUtils.createUser({ point: '1000' });
    const senderUserChain = await testUtils.createUserChain({
      user: sender,
      chain: testData.chainEvm,
    });
    const senderUserAsset = await testUtils.createUserAsset({
      user: sender,
      asset: testData.assetNex3Voucher,
      balance: '1',
    });
    const receiver = await testUtils.createUser({ point: '1000' });
    const receiverUserChain = await testUtils.createUserChain({
      user: receiver,
      chain: testData.chainEvm,
    });

    // Redeem asset
    mockWeb3TransactionSuccess();
    const transaction = await sendNFT({
      sender,
      receiver,
      asset: testData.assetNex3Voucher,
      amount: '1',
    });
    let updatedTransaction = await dataSource
      .getRepository(Transaction)
      .findOneBy({
        id: transaction.id,
      });
    expect(updatedTransaction).toBeDefined();
    expect(updatedTransaction.id).toBeDefined();
    expect(updatedTransaction.type).toBe(TransactionType.TRANSFER);
    expect(updatedTransaction.subType).toBe(TransactionSubType.ASSET);
    expect(updatedTransaction.chainId).toBe(testData.chainEvm.id);
    expect(updatedTransaction.fromUserId).toBe(sender.id);
    expect(updatedTransaction.toUserId).toBe(receiver.id);
    expect(updatedTransaction.brandId).toBe(testData.brandNex3.id);
    expect(updatedTransaction.assetId).toBe(testData.assetNex3Voucher.id);
    expect(updatedTransaction.nftId).toBe(senderUserAsset.id);
    expect(Number(updatedTransaction.point)).toBe(1);
    expect(updatedTransaction.status).toBe(TransactionStatus.PROCESSING);

    // Kiểm tra số dư của sender đã được cập nhật đúng
    const userAssetUpdated = await dataSource
      .getRepository(UserAsset)
      .findOneBy({ id: senderUserAsset.id });
    expect(userAssetUpdated.status).toBe(UserAssetStatus.PROCESSING);

    // Sau khi onchain thành công
    await commonService.updateTransactionSendAsset(updatedTransaction, {
      txHash: '0xabc123',
      gasUsed: Utils.toBigNumber(21000),
      hotWallet: testData.hotwalletEvm.walletAddress,
      status: true,
    });

    // Kiểm tra transaction đã được cập nhật đúng
    updatedTransaction = await dataSource.getRepository(Transaction).findOneBy({
      id: transaction.id,
    });
    expect(updatedTransaction.status).toBe(TransactionStatus.SUCCESS);
    expect(updatedTransaction.txHash).toBe('0xabc123');
    expect(Utils.toBigNumber(updatedTransaction.gasUsed).toString()).toBe(
      Utils.toBigNumber(21000).toString(),
    );
    expect(updatedTransaction.hotWalletId).toBe(testData.hotwalletEvm.id);

    // Kiểm tra nft đã được chuyển đi
    const receiverUserAsset = await dataSource
      .getRepository(UserAsset)
      .findOneBy({ id: senderUserAsset.id });
    expect(receiverUserAsset.userId).toBe(receiver.id);
    expect(receiverUserAsset.status).toBe(UserAssetStatus.REDEEMED);
  });

  it('nên cập nhật số dư khi 2 user gửi nft thành công', async () => {
    const sender1 = await testUtils.createUser({ point: '1000' });
    const senderUserChain1 = await testUtils.createUserChain({
      user: sender1,
      chain: testData.chainEvm,
    });
    const senderUserAsset1 = await testUtils.createUserAsset({
      user: sender1,
      asset: testData.assetNex3NFT,
      balance: '1',
    });
    const sender2 = await testUtils.createUser({ point: '1000' });
    const senderUserChain2 = await testUtils.createUserChain({
      user: sender2,
      chain: testData.chainEvm,
    });
    const senderUserAsset2 = await testUtils.createUserAsset({
      user: sender2,
      asset: testData.assetNex3NFT,
      balance: '1',
    });
    const receiver = await testUtils.createUser({ point: '1000' });
    const receiverUserChain = await testUtils.createUserChain({
      user: receiver,
      chain: testData.chainEvm,
    });

    // Redeem asset
    mockWeb3TransactionSuccess();
    const promises = [];
    promises.push(
      sendNFT({
        sender: sender1,
        receiver,
        asset: testData.assetNex3NFT,
        amount: '1',
      }),
    );
    promises.push(
      sendNFT({
        sender: sender2,
        receiver,
        asset: testData.assetNex3NFT,
        amount: '1',
      }),
    );
    await Promise.all(promises);
    const transactions = await dataSource
      .getRepository(Transaction)
      .createQueryBuilder('tx')
      .getMany();
    expect(transactions.length).toBe(2);

    // Kiểm tra số dư của sender đã được cập nhật đúng
    const senderAssetUpdated1 = await dataSource
      .getRepository(UserAsset)
      .findOneBy({ id: senderUserAsset1.id });
    expect(senderAssetUpdated1.status).toBe(UserAssetStatus.PROCESSING);
    const senderAssetUpdated2 = await dataSource
      .getRepository(UserAsset)
      .findOneBy({ id: senderUserAsset2.id });
    expect(senderAssetUpdated2.status).toBe(UserAssetStatus.PROCESSING);

    // Sau khi onchain thành công
    for (const transaction of transactions) {
      await commonService.updateTransactionSendAsset(transaction, {
        txHash: '0xabc123',
        gasUsed: Utils.toBigNumber(21000),
        hotWallet: testData.hotwalletEvm.walletAddress,
        status: true,
      });
    }

    // Kiểm tra nft đã được chuyển đi
    const receiverUserAsset1 = await dataSource
      .getRepository(UserAsset)
      .findOneBy({ id: senderUserAsset1.id });
    expect(receiverUserAsset1.userId).toBe(receiver.id);
    expect(receiverUserAsset1.status).toBe(UserAssetStatus.REDEEMED);

    // Kiểm tra nft đã được chuyển đi
    const receiverUserAsset2 = await dataSource
      .getRepository(UserAsset)
      .findOneBy({ id: senderUserAsset2.id });
    expect(receiverUserAsset2.userId).toBe(receiver.id);
    expect(receiverUserAsset2.status).toBe(UserAssetStatus.REDEEMED);
  });
});
