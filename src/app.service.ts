import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { AwsService } from './aws/aws.service';
import { CommonService } from './common-services/common.service';
import { Config } from './entities/config.entity';
import { Brand } from './entities/brand.entity';
import { Quest, QuestLevel, QuestStatus } from './entities/quest.entity';
import { QuestTask } from './entities/quest-task.entity';
import { Utils } from './common/utils';
import { SponsorAsset } from './entities/sponsor-asset.entity';
import { Sponsor } from './entities/sponsor.entity';
import { Chain, ChainName } from './entities/chain.entity';
import { HotWallet } from './entities/hot-wallet.entity';
import { Asset, AssetType } from './entities/asset.entity';
import { Article } from './entities/article.entity';
import { ArticleQuestion } from './entities/article-question.entity';
import { ArticleQuestionAnswer } from './entities/article-question-answer.entity';
import { QueueService } from './queue/queue.service';

@Injectable()
export class AppService {
  constructor(
    private readonly commonService: CommonService,
    private readonly dataSource: DataSource,
    private readonly queueService: QueueService,
    @InjectRepository(Config)
    private configRepository: Repository<Config>,
    @InjectRepository(Brand)
    private brandRepository: Repository<Brand>,
    @InjectRepository(HotWallet)
    private hotWalletRepository: Repository<HotWallet>,
    @InjectRepository(Chain)
    private chainRepository: Repository<Chain>,
    @InjectRepository(SponsorAsset)
    private sponsorAssetRepository: Repository<SponsorAsset>,
    @InjectRepository(Asset)
    private assetRepository: Repository<Asset>,
    @InjectRepository(Article)
    private articleRepository: Repository<Article>,
    @InjectRepository(ArticleQuestion)
    private articleQuestionRepository: Repository<ArticleQuestion>,
    @InjectRepository(ArticleQuestionAnswer)
    private articleQuestionAnswerRepository: Repository<ArticleQuestionAnswer>,
    @InjectRepository(Quest)
    private questRepository: Repository<Quest>,
    @InjectRepository(QuestTask)
    private questTaskRepository: Repository<QuestTask>,
    @InjectRepository(Sponsor)
    private sponsorRepository: Repository<Sponsor>,
  ) {}

  async getHello() {
    const web3Service = await this.commonService.getWeb3('84532');
    const abi20 = require('./web3/evm/erc20-abi.json');
    const signedTx = await web3Service.signTransaction({
      method: 'mint',
      params: [
        '******************************************',
        Utils.toBigNumber(1)
          .multipliedBy(10 ** 18)
          .toString(),
      ],
      contractAddress: '******************************************',
    });
    const txHash = await web3Service.sendSignedTransaction(signedTx);
    return {
      signedTx,
      txHash,
    };
  }

  getConfig() {
    return this.commonService.getConfig();
  }

  getFullConfig() {
    return this.commonService.getFullConfig();
  }

  async updateConfig(requestData: any) {
    await this.commonService.clearCacheByPrefix(Utils.getSqlKey('CONFIG_GET'));
    const currentConfig = await this.configRepository.findOneBy({});
    if (currentConfig) {
      requestData.id = currentConfig.id;
    }
    Object.assign(currentConfig, requestData);
    return currentConfig.save();
  }

  clearCache() {
    return this.commonService.clearCacheByPrefix(process.env.REDIS_PREFIX);
  }
}
