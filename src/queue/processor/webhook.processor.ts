import {
  OnQueueActive,
  OnQueueCompleted,
  OnQueueFailed,
  OnQueueStalled,
  Process,
  Processor,
} from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Job } from 'bull';
import { CommonService } from 'src/common-services/common.service';
import { QueueSetting } from 'src/common/constants';
import { Utils } from 'src/common/utils';
import { WebhookLog, WebhookLogStatus } from 'src/entities/webhook-log.entity';
import { WebhookDataDto } from 'src/webhook/dto/webhook-data.dto';
import { DataSource, Repository } from 'typeorm';
import { QueueService } from '../queue.service';
import { Web3Transaction } from 'src/web3/web3.interface';

@Processor(QueueSetting.WEBHOOK_QUEUE)
export class WebhookProcessor {
  private readonly logger = new Logger(WebhookProcessor.name);

  constructor(
    private commonService: CommonService,
    private dataSource: DataSource,
    @InjectRepository(WebhookLog)
    private webhookLogRepository: Repository<WebhookLog>,
    private queueService: QueueService,
  ) {}

  @OnQueueActive()
  onActive(job: Job) {
    this.logger.log(
      `Processing job ${job.queue.name} [${job.id}] with data ${JSON.stringify(job.data)}...`,
    );
  }

  @OnQueueStalled()
  onQueueStalled(job: Job) {
    this.logger.log(
      `Job ${job.queue.name} [${job.id}] has been marked as stalled`,
    );
  }

  @Process(QueueSetting.WEBHOOK_QUEUE)
  async process(job: Job) {
    return await this.dataSource.transaction(async (manager) => {});
  }

  @OnQueueFailed()
  onQueueFailed(job: Job, error: Error) {
    this.logger.error(`Job ${job.queue.name} [${job.id}] failed`);
    this.logger.error(error, error.stack);
    Utils.logMonitor(error, `Job ${job.queue.name} [${job.id}] failed`);
  }

  @OnQueueCompleted()
  onQueueCompleted(job: Job, result: any) {
    this.logger.log(`Job ${job.queue.name} [${job.id}] completed`);
  }
}
