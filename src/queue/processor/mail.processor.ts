import {
  OnQueueActive,
  OnQueueCompleted,
  OnQueueFailed,
  OnQueueStalled,
  Process,
  Processor,
} from '@nestjs/bull';
import { Job } from 'bull';
import { Logger } from '@nestjs/common';
import { QueueSetting } from '../../common/constants';
import { MailerService } from '@nestjs-modules/mailer';
import { Utils } from 'src/common/utils';

@Processor(QueueSetting.MAIL_QUEUE)
export class MailProcessor {
  private readonly logger = new Logger(MailProcessor.name);

  constructor(private readonly mailerService: MailerService) {}

  @OnQueueActive()
  onActive(job: Job) {
    this.logger.log(
      `Processor:@OnQueueActive - Processing job ${job.id} of type ${job.name}.`,
    );
  }

  @OnQueueStalled()
  onQueueStalled(job: Job) {
    this.logger.log(
      `Job ${job.queue.name} [${job.id}] has been marked as stalled`,
    );
  }

  @OnQueueCompleted()
  onComplete(job: Job) {
    this.logger.log(
      `Processor:@OnQueueCompleted - Completed job ${job.id} of type ${job.name}.`,
    );
  }

  @OnQueueFailed()
  onError(job: Job<any>, error: any) {
    this.logger.error(`Job ${job.queue.name} [${job.id}] failed`);
    this.logger.error(error, error.stack);
    Utils.logMonitor(error, `Job ${job.queue.name} [${job.id}] failed`);
  }

  @Process(QueueSetting.MAIL_QUEUE)
  async processMailQueue(job: Job): Promise<any> {
    this.logger.log('Processor:@Process - Sending email.');
    return await this.mailerService.sendMail(job.data.mail);
  }
}
