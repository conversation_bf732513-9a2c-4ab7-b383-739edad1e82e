import { ISendMailOptions } from '@nestjs-modules/mailer';
import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Queue } from 'bull';
import * as moment from 'moment';
import { MailConfig, QueueSetting } from 'src/common/constants';
import { AssetType } from 'src/entities/asset.entity';
import { WebhookDataDto } from 'src/webhook/dto/webhook-data.dto';

@Injectable()
export class QueueService {
  private readonly logger = new Logger(QueueService.name);

  constructor(
    @InjectQueue(QueueSetting.WEBHOOK_QUEUE)
    private webhookQueue: Queue,
    @InjectQueue(QueueSetting.MAIL_QUEUE)
    private mailQueue: Queue,
    @InjectQueue(QueueSetting.TRANSACTION_QUEUE)
    private transactionQueue: Queue,
  ) {}

  async addWebhookQueue(data: WebhookDataDto) {
    this.logger.log(`addWebhookQueue(): data = ${JSON.stringify(data)}`);
    await this.webhookQueue.add(QueueSetting.WEBHOOK_QUEUE, data, {
      ...QueueSetting.OPTIONS,
      attempts: 10,
      backoff: 60 * 1000, // 1 minute
    });
  }

  public async addMailToQueue(mail: ISendMailOptions) {
    this.logger.log(`addMailToQueue(): mail = ${JSON.stringify(mail)}`);
    mail = {
      ...mail,
      replyTo: MailConfig.REPLY_TO,
      from: MailConfig.FROM_MAIL,
    };
    await this.mailQueue.add(
      QueueSetting.MAIL_QUEUE,
      { mail },
      { attempts: 5, backoff: 2000, timeout: 30000, removeOnComplete: true },
    );
  }

  async addTransactionQueue(data: {
    transactionId: number;
    assetType: AssetType;
  }) {
    this.logger.log(`addTransactionQueue(): data = ${JSON.stringify(data)}`);
    await this.transactionQueue.add(QueueSetting.TRANSACTION_QUEUE, data, {
      ...QueueSetting.OPTIONS,
      attempts: 3,
      backoff: 60 * 1000, // 1 minute
    });
  }
}
