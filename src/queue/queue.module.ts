import { BullModule } from '@nestjs/bull';
import { Mo<PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CommonModule } from 'src/common-services/common.module';
import { QueueSetting } from 'src/common/constants';
import { WebhookLog } from 'src/entities/webhook-log.entity';
import { MailProcessor } from './processor/mail.processor';
import { WebhookProcessor } from './processor/webhook.processor';
import { QueueService } from './queue.service';
import { TransactionProcessor } from './processor/transaction.processor';

@Module({
  imports: [
    CommonModule,
    BullModule.registerQueue(
      {
        name: QueueSetting.WEBHOOK_QUEUE,
      },
      {
        name: QueueSetting.MAIL_QUEUE,
      },
      {
        name: QueueSetting.TRANSACTION_QUEUE,
      },
    ),
    TypeOrmModule.forFeature([WebhookLog]),
  ],
  providers: [
    QueueService,
    WebhookProcessor,
    MailProcessor,
    TransactionProcessor,
  ],
  exports: [QueueService],
})
export class QueueModule {}
