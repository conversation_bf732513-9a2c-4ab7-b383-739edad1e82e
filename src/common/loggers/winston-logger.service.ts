import { Inject, Injectable, LoggerService } from '@nestjs/common';
import * as winston from 'winston';
const {
  combine,
  timestamp,
  label,
  prettyPrint,
  simple,
  json,
  colorize,
  printf,
  splat,
} = winston.format;
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class WinstonLogger implements LoggerService {
  private requestId = '';

  private myFormat = [];

  private logger: winston.Logger;

  constructor() {
    if (process.env.ENV !== 'prod') {
      this.myFormat = [
        timestamp(),
        json(),
        prettyPrint(),
        colorize({
          all: true,
          colors: {
            info: 'green',
            debug: 'magenta',
            warn: 'yellow',
            error: 'red',
          },
        }),
      ];
    } else {
      this.myFormat = [
        timestamp(),
        json(),
        colorize({
          all: true,
          colors: {
            info: 'green',
            debug: 'magenta',
            warn: 'yellow',
            error: 'red',
          },
        }),
      ];
    }

    this.logger = winston.createLogger({
      level: 'debug',
      format: combine(...this.myFormat),
      transports: [new winston.transports.Console()],
    });
  }

  generateRequestId() {
    this.requestId = uuidv4();
  }

  log(message: any, ...optionalParams: any[]) {
    this.logger.info(message, {
      ...optionalParams,
      requestId: this.requestId,
    });
  }

  error(message: any, ...optionalParams: any[]) {
    this.logger.error(message, {
      ...optionalParams,
      requestId: this.requestId,
    });
  }

  warn(message: any, ...optionalParams: any[]) {
    this.logger.warn(message, {
      ...optionalParams,
      requestId: this.requestId,
    });
  }

  debug?(message: any, ...optionalParams: any[]) {
    this.logger.debug(message, {
      ...optionalParams,
      requestId: this.requestId,
    });
  }
}
