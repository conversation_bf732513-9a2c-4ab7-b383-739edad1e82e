import {
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';

@ValidatorConstraint()
export class EnumQueryValidate implements ValidatorConstraintInterface {
  validate(text: string, validationArguments: ValidationArguments) {
    const values = text?.split(',').map((v) => v.trim()) || [];

    const valueValidate = values.filter((v) =>
      validationArguments.constraints.includes(v.trim()),
    );
    return valueValidate.length === values.length;
  }
}
