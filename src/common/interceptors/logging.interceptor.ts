import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@nestjs/common';
import BigNumber from 'bignumber.js';
import { Request } from 'express';
import { Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { WinstonLogger } from '../loggers/winston-logger.service';
import { Utils } from '../utils';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  constructor(private logger: WinstonLogger) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const req: Request = context.switchToHttp().getRequest();

    this.logger.generateRequestId();
    const realIp = Utils.getIp(req);
    const user = Utils.getUser(req);
    this.logger.log(
      `${user ? `[${user.address}]` : ''}[${realIp ? realIp : req.ip}] ${req.method} ${req.originalUrl}`,
    );

    const now = Date.now();
    return next.handle().pipe(
      tap(() => {
        this.logger.log(
          `${user ? `[${user.address}]` : ''}[${realIp ? realIp : req.ip}] ${req.method} ${req.originalUrl} ${Date.now() - now}ms`,
        );
      }),
      map((value, index) => {
        try {
          let str = JSON.stringify(value);
          if (str.indexOf('"$numberDecimal"') > -1) {
            const matches = [...str.matchAll(/{"\$numberDecimal":"(.*?)"}/gim)];
            for (const match of matches) {
              BigNumber.config({ EXPONENTIAL_AT: 100 });
              const price = new BigNumber(match[1]);
              str = str.replace(match[0], `"${price.toString()}"`);
            }
            return JSON.parse(str);
          }
          return value;
        } catch (error) {
          return value;
        }
      }),
    );
  }
}
