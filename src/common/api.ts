import { HttpException } from '@nestjs/common';
import { ErrorCode } from './constants';

export type FieldErrorMetadata = {
  fieldError: string;
};

export function ApiError(
  code = '',
  message: any,
  metadata?: Record<string, any> | FieldErrorMetadata,
) {
  return new HttpException(
    {
      code: code ? code : ErrorCode.SOMETHING_WENT_WRONG,
      message,
      metadata,
    },
    400,
  );
}
