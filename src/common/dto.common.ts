import { Transform, Type } from 'class-transformer';
import { IsNumber, IsString } from 'class-validator';

export class SearchDto {
  @Transform(({ value }) =>
    value.trim().replace(/[.*+?^${}()|[\]\\%_]/g, '\\$&'),
  )
  keyword = '';

  @Type(() => Number)
  @IsNumber()
  page = 1;

  @Type(() => Number)
  @IsNumber()
  limit = 10;

  sort: object;

  projection: object;
}

export class GetSignedUrlDto {
  @IsString()
  key: string;
}
