import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { HttpAdapterHost } from '@nestjs/core';
import { Utils } from 'src/common/utils';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger('AllExceptionsFilter');

  constructor(private readonly httpAdapterHost: HttpAdapterHost) {}

  catch(exception: any, host: ArgumentsHost): void {
    // In certain situations `httpAdapter` might not be available in the
    // constructor method, thus we should resolve it here.
    const { httpAdapter } = this.httpAdapterHost;

    const ctx = host.switchToHttp();

    const httpStatus =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;
    if (httpStatus === HttpStatus.INTERNAL_SERVER_ERROR) {
      const req = ctx.getRequest();
      const realIp = Utils.getIp(req);
      const user = Utils.getUser(req);
      let msg = `${user ? `[${user.address}]` : ''}[${realIp ? realIp : req.ip}] ${req.method} ${req.originalUrl}`;
      try {
        const body = JSON.stringify(req.body);
        msg += `\nRequest body\n\`\`\`${body}\`\`\``;
      } catch (error) {}

      Utils.logMonitor(exception, msg);
    }

    if (exception instanceof HttpException) {
      this.logger.error(
        exception.getResponse()['message'],
        exception.getResponse()['code'],
        exception.getResponse()['metadata'],
      );
    } else {
      this.logger.error(exception?.message, exception?.stack);
    }

    const responseBody = {
      statusCode: httpStatus,
      timestamp: new Date().toISOString(),
      path: httpAdapter.getRequestUrl(ctx.getRequest()),
      message:
        exception instanceof HttpException
          ? exception.getResponse()['message']
          : '',
      errorCode:
        exception instanceof HttpException
          ? exception.getResponse()['code']
          : '',
      metadata:
        exception instanceof HttpException
          ? exception.getResponse()['metadata']
          : {},
    };

    httpAdapter.reply(ctx.getResponse(), responseBody, httpStatus);
  }
}
