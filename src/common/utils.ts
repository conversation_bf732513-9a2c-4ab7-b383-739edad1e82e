import { Logger } from '@nestjs/common';
import axios from 'axios';
import BigNumber from 'bignumber.js';
import { Request } from 'express';
import * as jwt from 'jsonwebtoken';
import { Brackets, SelectQueryBuilder } from 'typeorm';
import { SearchDto } from './dto.common';
import {
  IPaginationOptions,
  paginate,
  paginateRaw,
  Pagination,
} from 'nestjs-typeorm-paginate';
const CryptoJS = require('crypto-js');
import * as bcrypt from 'bcrypt';
import { Web3Transaction } from 'src/web3/web3.interface';
import { SqlCacheKey } from './constants';
import { UserChain } from 'src/entities/user-chain.entity';
import { ethers } from 'ethers';
import * as csv from 'fast-csv';
import { Readable } from 'stream';
import { pipeline } from 'stream/promises';

export class Utils {
  private static readonly logger = new Logger(Utils.name);

  public static logMonitor(exception: any, message = '') {
    if (process.env.ENV === 'local') return;
    const url = process.env.LOG_MONITOR;
    if (!url) return;
    fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json; charset=UTF-8' },
      body: JSON.stringify({
        text: `[${process.env.ENV}] ${message}\nError: ${exception?.message}\n\`\`\`${exception?.stack}\`\`\``,
      }),
    })
      .then((response) => {})
      .catch((error) => {
        console.error(error);
      });
  }

  public static getIp(req: any) {
    const realIp =
      req.headers['x-real-ip'] || req.headers['x-forwarded-for'] || req.ip;
    return realIp;
  }

  public static wait(ms) {
    return new Promise((resolve) => {
      setTimeout(resolve, ms);
    });
  }

  public static encrypt(str: string): string {
    return CryptoJS.AES.encrypt(str, process.env.CRYPTO_SECRET).toString();
  }

  public static decrypt(str: string): string {
    const bytes = CryptoJS.AES.decrypt(str, process.env.CRYPTO_SECRET);
    return bytes.toString(CryptoJS.enc.Utf8);
  }

  public static getRandom(array: any[]) {
    return array[Math.floor(Math.random() * array.length)];
  }

  public static getUser(req: Request) {
    try {
      if (
        req.headers['authorization'] &&
        req.headers['authorization'].split(' ')[0] === 'Bearer'
      ) {
        const jwtToken = req.headers['authorization'].split(' ')[1];
        return jwt.verify(jwtToken, process.env.JWT_SECRET) as any;
      }
    } catch (error) {
      return null;
    }
  }

  public static getTokenFromHeader(req: Request): string | null {
    const token = req.headers['hash'];
    if (!token || typeof token !== 'string') return null;
    return token.trim();
  }

  public static async retryFn(fn: () => Promise<any>, retry = 0, maxRetry = 3) {
    try {
      return await fn();
    } catch (error) {
      this.logger.warn(`retryFn(): Retry ${fn.name} ${retry} / ${maxRetry}`);
      this.logger.warn(error);
      if (retry >= maxRetry) {
        throw error;
      }

      await Utils.wait(300);
      retry++;
      return await this.retryFn(fn, retry);
    }
  }

  public static toBigNumber(value: any) {
    BigNumber.config({
      EXPONENTIAL_AT: 100,
    });
    return new BigNumber(value.toString());
  }

  public static formatEVMAddress(address: string) {
    try {
      return ethers.getAddress(address);
    } catch (error) {
      return address;
    }
  }

  public static convertEVMToWeb3Transaction(
    transaction: ethers.TransactionReceipt,
  ): Web3Transaction {
    const gasPrice = Utils.toBigNumber(transaction.gasPrice);
    const gasUsed = Utils.toBigNumber(transaction.gasUsed);
    return {
      txHash: transaction.hash,
      from: transaction.from,
      to: transaction.to,
      status: transaction.status === 1 ? true : false,
      gasPrice,
      gasUsed,
      fee: gasPrice.multipliedBy(gasUsed),
      payload: '',
      logs: [...transaction.logs],
      contractAddress: transaction.contractAddress || transaction.to,
    };
  }

  public static async streamUrl(url) {
    const response = await axios.get(url, {
      responseType: 'arraybuffer',
    });
    return response.data;
  }

  public static applySearch<T>(
    queryBuilder: SelectQueryBuilder<T>,
    keyword: string,
    fieldSearchs: { name: string; isAbsolute: boolean }[],
  ) {
    if (!keyword) {
      return;
    }
    const searchQuerry = new Brackets((qb) => {
      for (let index = 0; index < fieldSearchs.length; index++) {
        const fieldSearch = fieldSearchs[index];
        if (fieldSearch.isAbsolute) {
          qb.orWhere(`${fieldSearch.name} = :keyword`, { keyword });
        } else {
          qb.orWhere(`${fieldSearch.name} LIKE :keywordLike`, {
            keywordLike: `%${keyword}%`,
          });
        }
      }
    });
    return queryBuilder.andWhere(searchQuerry);
  }

  public static async paginate<T>(
    queryBuilder: SelectQueryBuilder<T>,
    query: Partial<SearchDto>,
    isRaw = false,
  ): Promise<Pagination<T>> {
    // Select field
    if (query.projection && Object.keys(query.projection).length > 0) {
      const projections = Object.keys(query.projection).map((column) => column);
      queryBuilder.select(projections);
    }

    // Sort
    if (query.sort) {
      const sortFields = Object.entries(query.sort).map(([field, order]) => ({
        field,
        sortType: order.toUpperCase(),
      }));
      sortFields.forEach(({ field, sortType }) => {
        queryBuilder.addOrderBy(`${queryBuilder.alias}.${field}`, sortType);
      });
    } else {
      queryBuilder.orderBy(`${queryBuilder.alias}.createdAt`, 'DESC');
    }

    const options: IPaginationOptions = {
      page: query.page,
      limit: query.limit,
    };

    return isRaw
      ? paginateRaw<T>(queryBuilder, options)
      : paginate<T>(queryBuilder, options);
  }

  public static filterByConditions<T>(
    queryBuilder: SelectQueryBuilder<T>,
    requestData: any,
  ): SelectQueryBuilder<T> {
    if (requestData.status) {
      queryBuilder.andWhere(`${queryBuilder.alias}.status = :status`, {
        status: requestData.status,
      });
    }

    if (requestData.dateFrom && requestData.dateTo) {
      queryBuilder.andWhere(
        `${queryBuilder.alias}.createdAt BETWEEN :start AND :end`,
        {
          start: requestData.dateFrom,
          end: requestData.dateTo,
        },
      );
    } else if (requestData.dateFrom) {
      queryBuilder.andWhere(`${queryBuilder.alias}.createdAt >= :start`, {
        start: requestData.dateFrom,
      });
    } else if (requestData.dateTo) {
      queryBuilder.andWhere(`${queryBuilder.alias}.createdAt <= :end`, {
        end: requestData.dateTo,
      });
    }
    return queryBuilder;
  }

  public static generateOTP(length = 4) {
    const digits = '0123456789';
    let OTP = '';
    const len = digits.length;
    for (let i = 0; i < length; i++) {
      OTP += digits[Math.floor(Math.random() * len)];
    }
    return OTP;
  }

  public static async hashPincode(pincode: string) {
    const salt = await bcrypt.genSalt(10);
    const hashed = await bcrypt.hash(pincode, salt);
    return hashed;
  }

  public static async comparePincode(pincode: string, hashedPincode: string) {
    return await bcrypt.compare(pincode, hashedPincode);
  }

  public static sanitizeKey(key: string): string {
    // Giữ lại ký tự Latin, số, dấu gạch dưới, gạch ngang, dấu chấm và dấu /
    return key
      .normalize('NFD') // Tách dấu khỏi ký tự (é -> e + ́)
      .replace(/[\u0300-\u036f]/g, '') // Loại bỏ các dấu (unicode combining marks)
      .replace(/[^a-zA-Z0-9/_\-.]/g, ''); // Loại bỏ tất cả ký tự không thuộc nhóm cho phép
  }

  public static getSqlKey(
    key: keyof typeof SqlCacheKey,
    ...params: (string | number)[]
  ): string {
    const baseKey = SqlCacheKey[key];
    if (!baseKey) {
      throw new Error(`Invalid SQL cache key: ${key}`);
    }

    if (params.length === 0) return baseKey;

    return `${baseKey}:${params.join(':')}`;
  }
  public static getWeb3Account(userChain: UserChain) {
    return {
      address: userChain.walletAddress,
      privateKey: Utils.decrypt(userChain.privateKey),
    };
  }

  public static serializeError(error: unknown): Record<string, any> {
    if (error instanceof Error) {
      const serialized: Record<string, any> = {
        name: error.name,
        message: error.message,
        stack: error.stack,
      };

      if ((error as any).code) {
        serialized.code = (error as any).code;
      }

      if ((error as any).cause) {
        const cause = (error as any).cause;
        serialized.cause =
          cause instanceof Error
            ? {
                name: cause.name,
                message: cause.message,
                stack: cause.stack,
              }
            : cause;
      }

      return serialized;
    }

    return {
      message: String(error),
    };
  }

  public static async readCsv<T extends object>(
    file: Express.Multer.File,
    options?: csv.ParserOptionsArgs,
  ): Promise<T[]> {
    // Check file and buffer
    if (!file || !file.buffer) {
      throw new Error('File or buffer is invalid.');
    }

    const records: T[] = [];

    // Default options
    const defaultOptions: csv.ParserOptionsArgs = {
      headers: true,
      objectMode: true,
      ignoreEmpty: true,
    };

    const csvOptions = { ...defaultOptions, ...options };

    // Create stream parser and listen data
    const csvParser = csv
      .parse(csvOptions)
      .on('data', (row: T) => {
        records.push(row);
      })
      .on('error', (error) => {
        // Error from parser will be caught by pipeline and throw out
        this.logger.error('CSV Parsing Error:', error);
      });

    // Create a readable stream from buffer
    const bufferStream = Readable.from(file.buffer);

    // Use pipeline to safely connect streams
    // Automatically handle errors and close stream when finished
    await pipeline(bufferStream, csvParser);

    return records;
  }

  public static hexToBytes(hex: string) {
    return Array.from(
      Uint8Array.from(Buffer.from(hex.replace(/^0x/, ''), 'hex')),
    );
  }
}
