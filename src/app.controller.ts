import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';
import { AppService } from './app.service';
import { JwtAdminAuthGuard } from './auth/guard/jwt-admin-auth.guard';
import { AwsService } from './aws/aws.service';
import { GetSignedUrlDto } from './common/dto.common';
import { Utils } from './common/utils';

@Controller()
export class AppController {
  constructor(
    private readonly appService: AppService,
    private readonly awsService: AwsService,
  ) {}

  @Get()
  getHello() {
    return this.appService.getHello();
  }

  @Get('/config')
  getConfig() {
    return this.appService.getConfig();
  }

  @UseGuards(JwtAdminAuthGuard)
  @Get('/full-config')
  getFullConfig() {
    return this.appService.getFullConfig();
  }

  @UseGuards(JwtAdminAuthGuard)
  @Post('/config')
  updateConfig(@Body() requestData: any) {
    return this.appService.updateConfig(requestData);
  }

  @UseGuards(JwtAdminAuthGuard)
  @Post('/clear-cache')
  clearCache() {
    return this.appService.clearCache();
  }

  @UseGuards(JwtAdminAuthGuard)
  @Post('/generate-s3-signed-url')
  async getSignedUrl(@Body() requestData: GetSignedUrlDto) {
    return this.awsService.getSignedUrl(
      `${new Date().valueOf()}_${Utils.sanitizeKey(requestData.key)}`,
    );
  }
}
