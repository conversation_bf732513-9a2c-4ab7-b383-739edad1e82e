import { MailerModule } from '@nestjs-modules/mailer';
import { BullModule } from '@nestjs/bull';
import { CacheModule } from '@nestjs/cache-manager';
import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import * as redisStore from 'cache-manager-redis-store';
import { RedisClientOptions } from 'redis';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ArticleModule } from './articles/articles.module';
import { AuthModule } from './auth/auth.module';
import { AwsModule } from './aws/aws.module';
import { BrandsModule } from './brands/brands.module';
import { ChainModule } from './chain/chain.module';
import { CommonModule } from './common-services/common.module';
import { MailConfig, QueueSetting } from './common/constants';
import { WinstonLoggerModule } from './common/loggers/winston-logger.module';
import { dbConfig } from './config/typeorm';
import { ArticleQuestionAnswer } from './entities/article-question-answer.entity';
import { ArticleQuestion } from './entities/article-question.entity';
import { Article } from './entities/article.entity';
import { Asset } from './entities/asset.entity';
import { Badge } from './entities/badge.entity';
import { BonusHistory } from './entities/bonus-history.entity';
import { Brand } from './entities/brand.entity';
import { Chain } from './entities/chain.entity';
import { Config } from './entities/config.entity';
import { HotWallet } from './entities/hot-wallet.entity';
import { QuestTaskUser } from './entities/quest-task-user.entity';
import { QuestTask } from './entities/quest-task.entity';
import { Quest } from './entities/quest.entity';
import { RedeemItem } from './entities/redeem-item.entity';
import { SponsorAsset } from './entities/sponsor-asset.entity';
import { Sponsor } from './entities/sponsor.entity';
import { Transaction } from './entities/transaction.entity';
import { UserBadge } from './entities/user-badge.entity';
import { FeedPricesModule } from './feed-prices/feed-prices.module';
import { HomeModule } from './homes/home.module';
import { QuestsModule } from './quests/quests.module';
import { QueueModule } from './queue/queue.module';
import { TaskModule } from './schedule/tasks.module';
import { SponsorModule } from './sponsor/sponsor.module';
import { TransactionModule } from './transactions/transaction.module';
import { TwitterModule } from './twitter/twitter.module';
import { UserModule } from './users/user.module';
import { WalletModule } from './wallets/wallet.module';
import { Web3Module } from './web3/web3.module';
import { WebhookModule } from './webhook/webhook.module';
import { AssetModule } from './assets/asset.module';
import { RedeemItemsModule } from './redeem-items/redeem-items.module';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    CacheModule.register<RedisClientOptions>({
      store: redisStore,
      url: `redis://${process.env.REDIS_HOST}:${process.env.REDIS_PORT}`,
      socket: {
        host: process.env.REDIS_HOST,
        port: Number(process.env.REDIS_PORT),
      },
      ttl: Number(process.env.REDIS_TTL),
      isGlobal: true,
    }),
    BullModule.forRoot({
      redis: {
        host: process.env.REDIS_HOST,
        port: Number(process.env.REDIS_PORT),
        keyPrefix: QueueSetting.KEY_PREFIX,
      },
    }),
    MailerModule.forRootAsync({
      useFactory: async () => {
        let template: any = undefined;

        if (process.env.ENV !== 'test') {
          const { HandlebarsAdapter } = await import(
            '@nestjs-modules/mailer/dist/adapters/handlebars.adapter'
          );
          template = {
            dir: process.cwd() + '/mail-templates',
            adapter: new HandlebarsAdapter({}, { inlineCssEnabled: true }),
            options: {
              strict: true,
            },
          };
        }

        return {
          transport: {
            host: process.env.MAIL_HOST,
            port: parseInt(process.env.MAIL_PORT || '465'),
            secure: true,
            auth: {
              user: process.env.MAIL_USER,
              pass: process.env.MAIL_PASSWORD,
            },
          },
          defaults: {
            from: `"No Reply" <${MailConfig.FROM_MAIL}>`,
          },
          template,
        };
      },
    }),
    TypeOrmModule.forRoot(dbConfig),
    TypeOrmModule.forFeature([
      Config,
      Brand,
      Chain,
      Asset,
      Sponsor,
      SponsorAsset,
      HotWallet,
      Quest,
      QuestTask,
      QuestTaskUser,
      Article,
      ArticleQuestion,
      ArticleQuestionAnswer,
      Transaction,
      Badge,
      UserBadge,
      RedeemItem,
    ]),
    WinstonLoggerModule,
    AuthModule,
    TaskModule,
    QueueModule,
    Web3Module,
    CommonModule,
    AwsModule,
    ChainModule,
    FeedPricesModule,
    WebhookModule,
    QuestsModule,
    ArticleModule,
    WalletModule,
    TransactionModule,
    BrandsModule,
    UserModule,
    TwitterModule,
    BonusHistory,
    HomeModule,
    RedeemItemsModule,
    SponsorModule,
    AssetModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
