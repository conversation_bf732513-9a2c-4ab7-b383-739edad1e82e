import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/guard/jwt-auth.guard';
import { User } from 'src/common/decorator/user.decorator';
import { UserPayload } from 'src/entities/user.entity';
import { BrandsService } from './brands.service';
import { GetBrandListDto } from './dto/get-brand-list.dto';
import { GenHashCodeDto } from './dto/gen-hash-code.dto';
import { UpdateBrandDto } from './dto/update-brand.dto';
@ApiBearerAuth()
@Controller('brands')
export class BrandsController {
  constructor(private readonly brandsService: BrandsService) {}

  @Post('gen-brand-hash-code')
  @ApiOperation({ summary: 'Gen hashcode for brand' })
  genBrandHashCode(@Body() requestData: GenHashCodeDto) {
    return this.brandsService.genBrandHashCode(requestData);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get brands' })
  async getBrands(
    @User() currentUser: UserPayload,
    @Query() requestData: GetBrandListDto,
  ) {
    return this.brandsService.getBrands(currentUser, requestData);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get detail brand by id' })
  async findOneById(@Param('id') id: number) {
    return this.brandsService.findOneById(id);
  }

  @Get('find-by-token/:token')
  @ApiOperation({ summary: 'Get detail brand by token' })
  async findOneByToken(@Param('token') token: string) {
    return this.brandsService.findOneByToken(token);
  }

  @Put('')
  @ApiOperation({ summary: 'Update brand' })
  async updateOne(@Body() updateBrandDto: UpdateBrandDto) {
    return this.brandsService.updateOne(updateBrandDto);
  }
}
