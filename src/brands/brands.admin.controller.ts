import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { JwtAdminAuthGuard } from 'src/auth/guard/jwt-admin-auth.guard';
import { User } from 'src/common/decorator/user.decorator';
import { AdminPayload } from 'src/entities/admin.entity';
import { BrandsAdminService } from './brands.admin.service';
import { CreateBrandDto } from './dto/create-brand.dto';
import { GetBrandDetailDto } from './dto/get-brand-detail.dto';
import { GetBrandListDto } from './dto/get-brand-list.dto';
import { UpdateBlockStatusDto } from './dto/update-block-status';

@Controller('admin/brands')
@UseGuards(JwtAdminAuthGuard)
export class BrandsAdminController {
  constructor(private readonly brandsService: BrandsAdminService) {}

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.brandsService.findOne(+id);
  }

  @Post()
  create(
    @User() currentUser: AdminPayload,
    @Body() requestData: CreateBrandDto,
  ) {
    return this.brandsService.create(currentUser, requestData);
  }

  @Post(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() requestData: CreateBrandDto,
    @User() currentUser: AdminPayload,
  ) {
    return this.brandsService.update(currentUser, id, requestData);
  }

  @Delete(':id')
  delete(
    @User() currentUser: AdminPayload,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.brandsService.delete(currentUser, id);
  }

  @Get()
  getBrands(@Query() query: GetBrandListDto) {
    return this.brandsService.getBrands(query);
  }

  @Get(':brandId/details')
  getBrandDetails(
    @Param('brandId', ParseIntPipe) brandId: number,
    @Query() query: GetBrandDetailDto,
  ) {
    return this.brandsService.getBrandDetails(brandId, query);
  }

  @Patch(':brandId/block-status')
  updateBlockStatus(
    @User() currentUser: AdminPayload,
    @Param('brandId', ParseIntPipe) brandId: number,
    @Body() body: UpdateBlockStatusDto,
  ) {
    return this.brandsService.updateBlockStatus(
      currentUser,
      brandId,
      body.blocked,
    );
  }
}
