import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CommonModule } from 'src/common-services/common.module';
import { AdminTransaction } from 'src/entities/admin-transaction.entity';
import { BrandAdmin } from 'src/entities/brand-admin.entity';
import { Brand } from 'src/entities/brand.entity';
import { SponsorAsset } from 'src/entities/sponsor-asset.entity';
import { Transaction } from 'src/entities/transaction.entity';
import { User } from 'src/entities/user.entity';
import { TransactionModule } from 'src/transactions/transaction.module';
import { BrandsAdminController } from './brands.admin.controller';
import { BrandsAdminService } from './brands.admin.service';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { BrandsController } from './brands.controller';
import { BrandsService } from './brands.service';

@Module({
  imports: [
    PassportModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET_MERCHANT,
      signOptions: { expiresIn: process.env.JWT_EXPIRATION_TIME },
    }),
    CommonModule,
    TransactionModule,
    TypeOrmModule.forFeature([
      Brand,
      AdminTransaction,
      User,
      Transaction,
      BrandAdmin,
      SponsorAsset,
    ]),
  ],
  controllers: [BrandsController, BrandsAdminController],
  providers: [BrandsService, BrandsAdminService],
  exports: [BrandsService],
})
export class BrandsModule {}
