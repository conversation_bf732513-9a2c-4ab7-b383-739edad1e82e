import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Utils } from 'src/common/utils';
import { Brand } from 'src/entities/brand.entity';
import { QuestStatus } from 'src/entities/quest.entity';
import { Transaction } from 'src/entities/transaction.entity';
import { User, UserPayload } from 'src/entities/user.entity';
import { Repository } from 'typeorm';
import { GetBrandListDto } from './dto/get-brand-list.dto';
import { GenHashCodeDto } from './dto/gen-hash-code.dto';
import { JwtService } from '@nestjs/jwt';
import { ApiError } from 'src/common/api';
import { UpdateBrandDto } from './dto/update-brand.dto';

@Injectable()
export class BrandsService {
  constructor(
    private jwtService: JwtService,
    @InjectRepository(Brand)
    private readonly brandRepository: Repository<Brand>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Transaction)
    private readonly transactionRepository: Repository<Transaction>,
  ) {}

  async genBrandHashCode(requestData: GenHashCodeDto): Promise<string> {
    const { apiKey } = requestData;
    const brand = await this.brandRepository.findOne({
      where: { code: apiKey },
      select: ['id', 'name', 'code'],
    });
    if (!brand) {
      throw new NotFoundException(`Brand with API key ${apiKey} not found`);
    }
    const payload = {
      brandId: brand.id,
      brandName: brand.name,
      brandCode: brand.code,
    };
    return this.jwtService.sign(payload);
  }

  async getBrands(currentUser: UserPayload, requestData: GetBrandListDto) {
    const { page, limit, sort, projection, status, keyword } = requestData;
    let queryBuilder = this.brandRepository
      .createQueryBuilder('brands')
      .leftJoinAndSelect('brands.questBrands', 'quest')
      .select([
        'brands.id AS id',
        'brands.name AS name',
        'brands.logoUrl AS logoUrl',
        'brands.status AS status',
        'brands.createdAt AS createdAt',
        'brands.updatedAt AS updatedAt',
        'brands.createdBy AS createdBy',
        'brands.updatedBy AS updatedBy',
        'brands.owner AS owner',
        'brands.description AS description',
        'brands.website AS website',
        'brands.iosAppUrl AS iosAppUrl',
        'brands.androidAppUrl AS androidAppUrl',
        'brands.contractAddress AS contractAddress',
        'brands.theme AS theme',
      ]);

    if (status) {
      queryBuilder.where('brands.status = :status', { status });
    }

    if (keyword) {
      queryBuilder = Utils.applySearch(queryBuilder, keyword, [
        { name: 'brands.name', isAbsolute: false },
        { name: 'brands.owner', isAbsolute: false },
        { name: 'brands.description', isAbsolute: false },
      ]);
    }

    queryBuilder
      .addSelect('COUNT(quest.id)', 'activeQuestCount')
      .andWhere('quest.status = :questStatus', {
        questStatus: QuestStatus.ACTIVE,
      })
      .groupBy('brands.id');

    const data = await Utils.paginate(
      queryBuilder,
      { page, limit, sort, projection },
      true,
    );

    const result = { items: [], meta: data.meta };
    let covertResult = [];

    if (data && data.items.length) {
      covertResult = data.items.map((item) => ({
        ...item,
        activeQuestCount: Number(item['activeQuestCount']),
      }));
    }

    result.items = covertResult;

    return result;
  }

  async findOneById(id: number): Promise<Brand> {
    const brand = await this.brandRepository.findOne({
      where: { id: id },
      select: [
        'id',
        'name',
        'localize',
        'owner',
        'logoUrl',
        'description',
        'website',
        'iosAppUrl',
        'androidAppUrl',
        'theme',
        'status',
      ],
    });
    if (!brand) {
      throw new NotFoundException(`Brand with ID ${id} not found`);
    }
    return brand;
  }

  async findOneByToken(token: string) {
    try {
      const payload = this.jwtService.verify(token);

      const brand = await this.brandRepository.findOne({
        where: { id: payload.id, code: payload.code },
        select: [
          'id',
          'name',
          'localize',
          'owner',
          'logoUrl',
          'description',
          'website',
          'iosAppUrl',
          'androidAppUrl',
          'theme',
          'status',
        ],
      });

      if (!brand) {
        throw new NotFoundException(`Brand not found`);
      }

      return brand;
    } catch (error) {
      throw ApiError('', 'Invalid token');
    }
  }

  async updateOne(updateBrandDto: UpdateBrandDto): Promise<Brand> {
    const { token, title, logo, fontFamily, primaryColor, secondaryColor } =
      updateBrandDto;

    const payload = this.jwtService.verify(token);

    const brand = await this.brandRepository.findOne({
      where: { id: payload.brandId, code: payload.brandCode },
    });

    if (!brand) {
      throw new NotFoundException(`Brand with ID ${payload.brandId} not found`);
    }

    brand.name = title;
    brand.logoUrl = logo;

    brand.theme = {
      ...brand.theme,
      fontFamily,
      primaryColor,
      secondaryColor,
    };

    return this.brandRepository.save(brand);
  }
}
