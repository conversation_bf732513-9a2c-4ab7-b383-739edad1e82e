import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CommonService } from 'src/common-services/common.service';
import { ApiError } from 'src/common/api';
import { Utils } from 'src/common/utils';
import { AdminTransactionType } from 'src/entities/admin-transaction.entity';
import { AdminPayload } from 'src/entities/admin.entity';
import { BrandAdmin } from 'src/entities/brand-admin.entity';
import { Brand, BrandStatus } from 'src/entities/brand.entity';
import { SponsorAsset } from 'src/entities/sponsor-asset.entity';
import {
  Transaction,
  TransactionStatus,
} from 'src/entities/transaction.entity';
import { User, UserStatus } from 'src/entities/user.entity';
import { AdminTransactionService } from 'src/transactions/transaction.admin.service';
import { DataSource, Repository } from 'typeorm';
import { CreateBrandDto } from './dto/create-brand.dto';
import { GetBrandDetailDto, TypeFilter } from './dto/get-brand-detail.dto';
import { GetBrandListDto } from './dto/get-brand-list.dto';

@Injectable()
export class BrandsAdminService {
  private readonly logger = new Logger(BrandsAdminService.name);

  constructor(
    private readonly dataSource: DataSource,
    private readonly commonService: CommonService,
    private readonly transactionService: AdminTransactionService,
    @InjectRepository(Brand)
    private readonly brandRepository: Repository<Brand>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Transaction)
    private readonly transactionRepository: Repository<Transaction>,
    @InjectRepository(BrandAdmin)
    private readonly brandAdminRepository: Repository<BrandAdmin>,
    @InjectRepository(SponsorAsset)
    private readonly sponsorAssetRepository: Repository<SponsorAsset>,
  ) {}

  async findOne(id: number): Promise<Brand> {
    const brand = await this.brandRepository.findOne({
      where: {
        id,
        isDeleted: false,
      },
    });
    if (!brand) {
      throw new NotFoundException(`Brand with ID ${id} not found`);
    }
    return brand;
  }

  async create(currentUser: AdminPayload, requestData: CreateBrandDto) {
    const brand = await this.brandRepository.save({
      ...requestData,
      status: BrandStatus.ACTIVE,
      createdBy: currentUser.id,
    });

    // Create collection
    // await this.transactionService.createTransaction(currentUser, {
    //   type: AdminTransactionType.CREATE_COLLECTION,
    //   brandId: brand.id,
    // });

    return await this.brandRepository.findOneBy({ id: brand.id });
  }

  async update(
    currentUser: AdminPayload,
    id: number,
    requestData: CreateBrandDto,
  ) {
    const brand = await this.brandRepository.findOne({
      where: {
        id,
        isDeleted: false,
      },
    });

    if (!brand) {
      throw ApiError('', `Brand with ID ${id} not found`);
    }

    await this.brandRepository.update(id, {
      ...requestData,
      updatedBy: currentUser.id,
    });
    return {
      message: `Brand with ID ${id} updated successfully`,
    };
  }

  async delete(currentUser: AdminPayload, id: number) {
    const brand = await this.brandRepository.findOne({
      where: {
        id,
        isDeleted: false,
      },
    });
    if (!brand) {
      throw ApiError('', `Brand with ID ${id} not found`);
    }
    const queryBuilder = this.userRepository
      .createQueryBuilder('users')
      .leftJoin('user-brands', 'userBrands', 'userBrands.userId = users.id')
      .where('userBrands.brandId = :brandId', { brandId: id })
      .andWhere('users.status != :inactive', { inactive: UserStatus.INACTIVE });
    const countUser = await queryBuilder.getCount();
    if (countUser > 0) {
      throw ApiError(
        '',
        `Cannot delete brand with ID ${id} because it has ${countUser} users`,
      );
    }

    await this.brandRepository.update(id, {
      isDeleted: true,
    });
    await this.brandAdminRepository.update(
      { brandId: id },
      { isDeleted: true },
    );
    return {
      message: `Brand with ID ${id} deleted successfully`,
    };
  }

  async getBrands(query: GetBrandListDto) {
    const { keyword, status, startDate, endDate } = query;

    const queryBuilder = this.brandRepository
      .createQueryBuilder('brands')
      .leftJoin('user-brands', 'userBrands', 'userBrands.brandId = brands.id')
      .leftJoin(
        'transactions',
        'transactions',
        'transactions.brandId = brands.id',
      )
      .select([
        'brands.id AS id',
        'brands.name AS name',
        'brands.logoUrl AS logoUrl',
        'brands.status AS status',
        'brands.createdAt AS createdAt',
        'brands.updatedAt AS updatedAt',
        'brands.createdBy AS createdBy',
        'brands.updatedBy AS updatedBy',
        'brands.owner AS owner',
        'brands.description AS description',
        'brands.website AS website',
        'brands.iosAppUrl AS iosAppUrl',
        'brands.androidAppUrl AS androidAppUrl',
        'brands.contractAddress AS contractAddress',
        'brands.theme AS theme',
      ])
      .addSelect('COUNT(DISTINCT userBrands.id)', 'userCount')
      .addSelect('COUNT(DISTINCT transactions.id)', 'transactionCount')
      .where('brands.isDeleted = :isDeleted', { isDeleted: false })
      .andWhere('brands.status != :status', { status: BrandStatus.DRAFT })
      .groupBy('brands.id');

    if (keyword) {
      Utils.applySearch(queryBuilder, keyword, [
        { name: 'brands.name', isAbsolute: false },
      ]);
    }

    if (status) {
      queryBuilder.andWhere('brands.status = :filterStatus', {
        filterStatus: status,
      });
    }

    if (startDate) {
      queryBuilder.andWhere('brands.createdAt >= :startDate', {
        startDate: new Date(startDate),
      });
    }

    if (endDate) {
      queryBuilder.andWhere('brands.createdAt <= :endDate', {
        endDate: new Date(endDate),
      });
    }

    if (query.sort) {
      const sort = query.sort as Record<string, string>;

      for (const field of ['userCount', 'transactionCount']) {
        if (sort[field]) {
          const sortOrder = sort[field].toUpperCase();
          if (sortOrder === 'ASC' || sortOrder === 'DESC') {
            queryBuilder.addOrderBy(field, sortOrder);
          }
          delete sort[field];
        }
      }
    }

    return await Utils.paginate<Brand>(queryBuilder, query, true);
  }

  async getBrandDetails(brandId: number, query: GetBrandDetailDto) {
    const { typeFilter } = query;
    if (typeFilter === TypeFilter.USER) {
      const [userList, brandDetail] = await Promise.all([
        this.getUsers(brandId, query),
        this.findOne(brandId),
      ]);
      return {
        brandDetail,
        userList,
      };
    } else if (typeFilter === TypeFilter.TRANSACTION) {
      const [transactionsList, brandDetail] = await Promise.all([
        this.getTransactions(brandId, query),
        this.findOne(brandId),
      ]);
      return {
        brandDetail,
        transactionsList,
      };
    }
  }

  async getTransactions(brandId: number, query: GetBrandDetailDto) {
    const queryBuilder = this.transactionRepository
      .createQueryBuilder('transactions')
      .leftJoin('users', 'usersFrom', 'usersFrom.id = transactions.fromUserId')
      .leftJoin(
        'user-chains',
        'userChainsFrom',
        'userChainsFrom.userId = usersFrom.id',
      )
      .where('transactions.brandId = :brandId', { brandId: brandId })
      .andWhere('transactions.status != :status', {
        status: TransactionStatus.DRAFT,
      })
      .select([
        'transactions.id AS id',
        'transactions.createdAt AS createdAt',
        'transactions.status AS status',
        'transactions.type AS type',
        'usersFrom.email AS email',
        'userChainsFrom.walletAddress AS walletAddress',
      ]);
    if (query.keyword) {
      Utils.applySearch(queryBuilder, query.keyword, [
        { name: 'usersFrom.email', isAbsolute: false },
        { name: 'userChainsFrom.walletAddress', isAbsolute: false },
      ]);
    }

    if (query.transactionStatus) {
      queryBuilder.andWhere('transactions.status = :filterStatus', {
        filterStatus: query.transactionStatus,
      });
    }

    if (query.fromDate) {
      queryBuilder.andWhere('transactions.createdAt >= :startDate', {
        startDate: new Date(query.fromDate),
      });
    }

    if (query.toDate) {
      queryBuilder.andWhere('transactions.createdAt <= :endDate', {
        endDate: new Date(query.toDate),
      });
    }

    return await Utils.paginate<Transaction>(queryBuilder, query, true);
  }

  async getUsers(brandId: number, query: GetBrandDetailDto) {
    const queryBuilder = this.userRepository
      .createQueryBuilder('users')
      .leftJoin('user-chains', 'userChains', 'userChains.userId = users.id')
      .leftJoin('user-brands', 'userBrands', 'userBrands.userId = users.id')
      .leftJoin('chains', 'chains', 'chains.id = userChains.chainId')
      .where('userBrands.brandId = :brandId', { brandId: brandId })
      .andWhere('users.status != :status', { status: UserStatus.INACTIVE })
      .andWhere('chains.blockchainId = :blockchainId', {
        blockchainId: process.env.CHAIN_ID,
      })
      .select([
        'users.id AS id',
        'users.email AS email',
        'users.createdAt AS createdAt',
        'users.updatedAt AS updatedAt',
        'users.status AS status',
      ])
      .addSelect('userChains.walletAddress', 'walletAddress');

    if (query.keyword) {
      Utils.applySearch(queryBuilder, query.keyword, [
        { name: 'users.email', isAbsolute: false },
        { name: 'userChains.walletAddress', isAbsolute: false },
      ]);
    }

    if (query.userStatus) {
      queryBuilder.andWhere('users.status = :filterStatus', {
        filterStatus: query.userStatus,
      });
    }

    if (query.fromDate) {
      queryBuilder.andWhere('users.createdAt >= :startDate', {
        startDate: new Date(query.fromDate),
      });
    }

    if (query.toDate) {
      queryBuilder.andWhere('users.createdAt <= :endDate', {
        endDate: new Date(query.toDate),
      });
    }

    return await Utils.paginate<User>(queryBuilder, query, true);
  }

  async updateBlockStatus(
    currentUser: AdminPayload,
    id: number,
    blocked: boolean,
  ) {
    const brand = await this.brandRepository.findOne({
      where: {
        id,
        isDeleted: false,
      },
    });

    if (!brand) {
      throw ApiError('', `Brand with ID ${id} not found`);
    }

    await this.brandRepository.update(id, {
      status: blocked ? BrandStatus.DISABLED : BrandStatus.ACTIVE,
      updatedBy: currentUser.id,
    });

    return {
      message: 'Brand status updated successfully',
    };
  }
}
