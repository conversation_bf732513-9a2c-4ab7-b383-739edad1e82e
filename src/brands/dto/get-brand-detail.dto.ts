import { IsEnum, IsOptional, IsDateString } from 'class-validator';
import { SearchDto } from 'src/common/dto.common';
import {
  TransactionStatus,
  TransactionType,
} from 'src/entities/transaction.entity';
import { UserStatus } from 'src/entities/user.entity';

export enum TypeFilter {
  USER = 'user',
  TRANSACTION = 'transaction',
}

export class GetBrandDetailDto extends SearchDto {
  @IsDateString()
  @IsOptional()
  fromDate?: string;

  @IsDateString()
  @IsOptional()
  toDate?: string;

  @IsEnum(UserStatus)
  @IsOptional()
  userStatus?: UserStatus;

  @IsEnum(TransactionStatus)
  @IsOptional()
  transactionStatus?: TransactionStatus;

  @IsEnum(TypeFilter)
  typeFilter: TypeFilter;

  @IsEnum(TransactionType)
  @IsOptional()
  transactionType?: TransactionType;
}
