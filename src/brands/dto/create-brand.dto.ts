import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

import { Type } from 'class-transformer';
import { IsUrl, ValidateNested } from 'class-validator';

class BrandThemeDto {
  @IsString()
  primaryColor: string;

  @IsString()
  secondaryColor: string;

  @IsString()
  fontFamily: string;
}

export class CreateBrandDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  owner?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsUrl()
  logoUrl: string;

  @IsOptional()
  @IsUrl()
  website?: string;

  @IsOptional()
  @IsUrl()
  iosAppUrl?: string;

  @IsOptional()
  @IsUrl()
  androidAppUrl?: string;

  @ValidateNested()
  @Type(() => BrandThemeDto)
  theme: BrandThemeDto;

  @IsNotEmpty()
  @IsString()
  code: string;
}
