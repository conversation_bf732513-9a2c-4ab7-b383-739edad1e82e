import { IsOptional, IsNotEmpty } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateBrandDto {
  @IsNotEmpty()
  @ApiPropertyOptional({ description: 'Brand apiKey' })
  token: string;

  @IsNotEmpty()
  @ApiPropertyOptional({ description: 'Brand title' })
  title: string;

  @IsOptional()
  @ApiPropertyOptional({ description: 'Brand logo URL' })
  logo?: string;

  @IsOptional()
  @ApiPropertyOptional({ description: 'Brand font family' })
  fontFamily?: string;

  @IsOptional()
  @ApiPropertyOptional({ description: 'Brand primary color' })
  primaryColor?: string;

  @IsOptional()
  @ApiPropertyOptional({ description: 'Brand secondary color' })
  secondaryColor?: string;
}
