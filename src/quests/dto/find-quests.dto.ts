import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, Validate } from 'class-validator';
import { SearchDto } from 'src/common/dto.common';
import { EnumQueryValidate } from 'src/common/validate/enum-query.validate';
import { QuestLevel, QuestStatusByDate } from 'src/entities/quest.entity';
import { UserQuestStatus } from 'src/entities/user-quest.entity';

export class FindQuestsDto extends SearchDto {
  @ApiProperty({
    enum: QuestLevel,
    required: false,
  })
  @IsOptional()
  @Validate(
    EnumQueryValidate,
    [QuestLevel.EASY, QuestLevel.MEDIUM, QuestLevel.HARD],
    {
      message: 'level must be a valid enum value',
    },
  )
  level: string;

  @ApiProperty({
    enum: QuestStatusByDate,
    required: false,
  })
  @IsOptional()
  @Validate(
    EnumQueryValidate,
    [QuestStatusByDate.COMING_SOON, QuestStatusByDate.ON_GOING],
    {
      message: 'dateStatus must be a valid enum value',
    },
  )
  dateStatus: string;

  @ApiProperty({
    enum: UserQuestStatus,
    required: false,
  })
  @IsOptional()
  @Validate(
    EnumQueryValidate,
    [UserQuestStatus.CLAIMED, UserQuestStatus.UNCLAIMED],
    {
      message: 'userClaimStatus must be a valid enum value',
    },
  )
  userClaimStatus: string;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  brandId?: number;
}
