import {
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
} from '@nestjs/swagger';
import { JwtAdminAuthGuard } from 'src/auth/guard/jwt-admin-auth.guard';
import { GetQuestsListDto } from './dto/get-quests-list.dto';
import { QuestsAdminService } from './quests.admin.service';

@Controller('admin/quests')
@UseGuards(JwtAdminAuthGuard)
export class QuestsAdminController {
  constructor(private readonly questsService: QuestsAdminService) {}

  @Get()
  getQuests(@Query() query: GetQuestsListDto) {
    return this.questsService.getQuests(query);
  }

  @Get(':id/quest-detail')
  findOneQuest(@Param('id') id: string) {
    return this.questsService.getQuestsDetail(+id);
  }

  @Get(':id/article-detail')
  findOneArticle(@Param('id') id: string) {
    return this.questsService.getArticleDetails(+id);
  }

  @Post('import-brands')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Import brands CSV' })
  @UseInterceptors(FileInterceptor('file'))
  async importBrandsCSV(@UploadedFile() file: Express.Multer.File) {
    return this.questsService.importBrandsCSV(file);
  }

  @Post('import-quests')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Import quests CSV' })
  @UseInterceptors(FileInterceptor('file'))
  async importQuestsCSV(@UploadedFile() file: Express.Multer.File) {
    return this.questsService.importQuestsCSV(file);
  }

  @Post('import-quest-tasks')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Import quest tasks CSV' })
  @UseInterceptors(FileInterceptor('file'))
  async importQuestTasksCSV(@UploadedFile() file: Express.Multer.File) {
    return this.questsService.importQuestTasksCSV(file);
  }
}
