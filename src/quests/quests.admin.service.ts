import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ApiError } from 'src/common/api';
import { Utils } from 'src/common/utils';
import { ArticleQuestionAnswer } from 'src/entities/article-question-answer.entity';
import { ArticleQuestion } from 'src/entities/article-question.entity';
import { Article } from 'src/entities/article.entity';
import { Brand } from 'src/entities/brand.entity';
import { QuestTask } from 'src/entities/quest-task.entity';
import { Quest } from 'src/entities/quest.entity';
import { GetQuestsListDto, TypeFilter } from './dto/get-quests-list.dto';

@Injectable()
export class QuestsAdminService {
  private readonly logger = new Logger(QuestsAdminService.name);

  constructor(
    @InjectRepository(Quest)
    private readonly questRepository: Repository<Quest>,
    @InjectRepository(Article)
    private readonly articleRepository: Repository<Article>,
    @InjectRepository(ArticleQuestion)
    private readonly articleQuestionRepository: Repository<ArticleQuestion>,
    @InjectRepository(ArticleQuestionAnswer)
    private readonly articleQuestionAnswerRepository: Repository<ArticleQuestionAnswer>,
    @InjectRepository(QuestTask)
    private readonly questTaskRepository: Repository<QuestTask>,
    @InjectRepository(Brand)
    private readonly brandRepository: Repository<Brand>,
  ) {}

  async getQuests(query: GetQuestsListDto) {
    const { typeFilter } = query;
    if (typeFilter === TypeFilter.QUEST) {
      return this.getQuestsList(query);
    } else if (typeFilter === TypeFilter.ARTICLE) {
      return this.getArticlesList(query);
    }
    throw ApiError('TYPE_FILTER_NOT_FOUND', 'Type filter not found');
  }

  async getQuestsList(query: GetQuestsListDto) {
    const { fromDate, toDate, questStatus, keyword, sort } = query;
    const queryBuilder = this.questRepository
      .createQueryBuilder('quest')
      .leftJoin('quest.sponsor', 'sponsor')
      .leftJoin('quest.sponsorAsset', 'sponsorAsset')
      .leftJoin('quest.questBrands', 'questBrand')
      .leftJoin('questBrand.brand', 'brand')
      .select([
        'quest.id AS "questId"',
        'quest.title AS "questTitle"',
        'quest.description AS "questDescription"',
        'quest.status AS "questStatus"',
        'quest.imageUrl AS "imageUrl"',
        'quest.createdAt AS "createdAt"',
        'quest.reward AS "reward"',
        'quest.claimedReward AS "claimedReward"',
        'quest.startDate AS "startDate"',
        'quest.toDate AS "toDate"',
        'sponsor.name AS "sponsorName"',
        'sponsorAsset.totalReward AS "totalReward"',
        'GROUP_CONCAT(DISTINCT brand.name) AS "brandNames"',
      ])
      .groupBy('quest.id');
    if (keyword) {
      Utils.applySearch(queryBuilder, keyword, [
        { name: 'sponsor.name', isAbsolute: false },
        { name: 'quest.title', isAbsolute: false },
        { name: 'brand.name', isAbsolute: false },
      ]);
    }
    if (fromDate) {
      queryBuilder.andWhere('quest.createdAt >= :fromDate', {
        fromDate: new Date(fromDate),
      });
    }
    if (toDate) {
      queryBuilder.andWhere('quest.createdAt <= :toDate', {
        toDate: new Date(toDate),
      });
    }
    if (questStatus) {
      queryBuilder.andWhere('quest.status = :questStatus', { questStatus });
    }

    if (sort) {
      for (const field of ['totalReward']) {
        if (sort[field]) {
          const sortOrder = sort[field].toUpperCase();
          if (sortOrder === 'ASC' || sortOrder === 'DESC') {
            queryBuilder.addOrderBy(field, sortOrder);
          }
          delete sort[field];
        }
      }
    }

    return await Utils.paginate<Quest>(queryBuilder, query, true);
  }

  async getArticlesList(query: GetQuestsListDto) {
    const { fromDate, toDate, articleStatus, keyword, sort } = query;
    const queryBuilder = this.articleRepository
      .createQueryBuilder('article')
      .leftJoin('article.sponsor', 'sponsor')
      .leftJoin('article.sponsorAsset', 'sponsorAsset')
      .leftJoin('article.articleBrands', 'articleBrands')
      .leftJoin('articleBrands.brand', 'brand')
      .select([
        'article.id AS "articleId"',
        'article.title AS "articleTitle"',
        'article.status AS "articleStatus"',
        'article.imageUrl AS "imageUrl"',
        'article.createdAt AS "createdAt"',
        'article.reward AS "reward"',
        'article.claimedReward AS "claimedReward"',
        'sponsor.name AS "sponsorName"',
        'sponsorAsset.totalReward AS "totalReward"',
        'GROUP_CONCAT(DISTINCT brand.name) AS "brandNames"',
      ])

      .groupBy('article.id');

    if (keyword) {
      Utils.applySearch(queryBuilder, keyword, [
        { name: 'sponsor.name', isAbsolute: false },
        { name: 'article.title', isAbsolute: false },
        { name: 'brand.name', isAbsolute: false },
      ]);
    }
    if (fromDate) {
      queryBuilder.andWhere('article.createdAt >= :fromDate', {
        fromDate: new Date(fromDate),
      });
    }
    if (toDate) {
      queryBuilder.andWhere('article.createdAt <= :toDate', {
        toDate: new Date(toDate),
      });
    }
    if (articleStatus) {
      queryBuilder.andWhere('article.status = :articleStatus', {
        articleStatus,
      });
    }

    if (sort) {
      for (const field of ['totalReward']) {
        if (sort[field]) {
          const sortOrder = sort[field].toUpperCase();
          if (sortOrder === 'ASC' || sortOrder === 'DESC') {
            queryBuilder.addOrderBy(field, sortOrder);
          }
          delete sort[field];
        }
      }
    }

    return await Utils.paginate<Article>(queryBuilder, query, true);
  }

  async getQuestsDetail(questId: number) {
    const [quest, questTasks] = await Promise.all([
      this.getQuestInfor(questId),
      this.getQuestTasks(questId),
    ]);
    return {
      quest,
      questTasks,
    };
  }

  async getQuestInfor(questId: number) {
    const queryBuilder = this.questRepository
      .createQueryBuilder('quest')
      .leftJoin('quest.sponsor', 'sponsor')
      .leftJoin('quest.sponsorAsset', 'sponsorAsset')
      .leftJoin('quest.questBrands', 'questsBrand')
      .leftJoin('questsBrand.brand', 'brand')
      .select([
        'quest.id AS "questId"',
        'quest.title AS "questTitle"',
        'quest.imageUrl AS "imageUrl"',
        'quest.description AS "questDescription"',
        'quest.status AS "questStatus"',
        'quest.createdAt AS "createdAt"',
        'quest.reward AS "reward"',
        'quest.claimedReward AS "claimedReward"',
        'quest.startDate AS "startDate"',
        'quest.toDate AS "toDate"',
        'sponsor.name AS "sponsorName"',
        'sponsorAsset.totalReward AS "totalReward"',
        'GROUP_CONCAT(DISTINCT brand.name) AS "brandNames"',
      ])
      .where('quest.id = :questId', { questId })

      .groupBy('quest.id');

    const quest = await queryBuilder.getRawOne();
    if (!quest) {
      throw ApiError('QUEST_ID_NOT_FOUND', 'Quest id not found');
    }
    return quest;
  }

  async getQuestTasks(questId: number) {
    const queryBuilder = this.questTaskRepository
      .createQueryBuilder('questTask')
      .where('questTask.questId = :questId', { questId })
      .leftJoin('questTask.quest', 'quest')
      .select([
        'questTask.id',
        'questTask.type',
        'questTask.data',
        'questTask.description',
      ]);
    return queryBuilder.getMany();
  }

  async getArticleDetails(articleId: number) {
    const [article, articleQuestions] = await Promise.all([
      this.getArticleInfor(articleId),
      this.getArticleQuestions(articleId),
    ]);
    return {
      article,
      articleQuestions,
    };
  }

  async getArticleQuestions(articleId: number) {
    const queryBuilder = this.articleQuestionRepository
      .createQueryBuilder('articleQuestion')
      .leftJoin(
        'articleQuestion.articleQuestionAnswers',
        'articleQuestionAnswers',
      )
      .select([
        'articleQuestion.id',
        'articleQuestion.question',
        'articleQuestionAnswers.answer',
        'articleQuestionAnswers.isCorrect',
      ])
      .where('articleQuestion.articleId = :articleId', { articleId });
    return queryBuilder.getMany();
  }

  async getArticleInfor(articleId: number) {
    const queryBuilder = this.articleRepository
      .createQueryBuilder('article')
      .leftJoin('article.articleBrands', 'articleBrands')
      .leftJoin('articleBrands.brand', 'brand')
      .leftJoin('article.sponsor', 'sponsor')
      .leftJoin('article.sponsorAsset', 'sponsorAsset')
      .select([
        'article.id AS "articleId"',
        'article.title AS "articleTitle"',
        'article.status AS "articleStatus"',
        'article.createdAt AS "createdAt"',
        'article.reward AS "reward"',
        'article.claimedReward AS "claimedReward"',
        'article.content AS "content"',
        'article.imageUrl AS "imageUrl"',
        'article.readTime AS "readTime"',
        'article.category AS "category"',
        'sponsor.name AS "sponsorName"',
        'sponsorAsset.totalReward AS "totalReward"',
        'GROUP_CONCAT(DISTINCT brand.name) AS "brandNames"',
      ])
      .where('article.id = :articleId', { articleId })

      .groupBy('article.id');
    const article = await queryBuilder.getRawOne();
    if (!article) {
      throw ApiError('ARTICLE_ID_NOT_FOUND', 'Article id not found');
    }
    return article;
  }

  async importBrandsCSV(file: Express.Multer.File) {
    try {
      const rows = (await Utils.readCsv(file)) as any[];
      const questBrands: Partial<Brand>[] = rows.map((row) => {
        return {
          name: row['name'],
          owner: row['owner'],
          description: row['description'],
          logoUrl: row['logoUrl'],
          website: row['website'],
          iosAppUrl: row['iosAppUrl'],
          androidAppUrl: row['androidAppUrl'],
          theme: row['theme'],
          contractAddress: row['contractAddress'],
        };
      });

      await this.brandRepository.upsert(questBrands, {
        conflictPaths: ['id'],
        skipUpdateIfNoValuesChanged: true,
      });

      return true;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async importQuestsCSV(file: Express.Multer.File) {
    try {
      const rows = (await Utils.readCsv(file)) as any[];
      const quests: Partial<Quest>[] = rows.map((row) => {
        return {
          title: row['title'],
          description: row['description'],
          imageUrl: row['imageUrl'],
          point: row['point'],
          startDate: row['startDate'],
          toDate: row['toDate'],
          level: row['level'],
          status: row['status'],
          brandId: +row['brandId'],
          sponsorId: +row['sponsorId'],
        };
      });

      await this.questRepository.upsert(quests, {
        conflictPaths: ['id'],
        skipUpdateIfNoValuesChanged: true,
      });

      return true;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async importQuestTasksCSV(file: Express.Multer.File) {
    try {
      const rows = (await Utils.readCsv(file)) as any[];
      const questTasks: Partial<QuestTask>[] = rows.map((row) => {
        return {
          questId: +row['questId'],
          type: row['type'],
          data: row['data'],
          description: row['description'],
          orderIndex: +row['orderIndex'],
        };
      });

      await this.questTaskRepository.upsert(questTasks, {
        conflictPaths: ['id'],
        skipUpdateIfNoValuesChanged: true,
      });

      return true;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
}
