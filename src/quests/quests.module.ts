import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CommonModule } from 'src/common-services/common.module';
import { ArticleQuestionAnswer } from 'src/entities/article-question-answer.entity';
import { ArticleQuestion } from 'src/entities/article-question.entity';
import { Article } from 'src/entities/article.entity';
import { SponsorAsset } from 'src/entities/sponsor-asset.entity';
import { Sponsor } from 'src/entities/sponsor.entity';
import { Transaction } from 'src/entities/transaction.entity';
import { QueueModule } from 'src/queue/queue.module';
import { Brand } from '../entities/brand.entity';
import { QuestTaskUser } from '../entities/quest-task-user.entity';
import { QuestTask } from '../entities/quest-task.entity';
import { Quest } from '../entities/quest.entity';
import { UserQuest } from '../entities/user-quest.entity';
import { QuestsAdminController } from './quests.admin.controller';
import { QuestsAdminService } from './quests.admin.service';
import { QuestsController } from './quests.controller';
import { QuestsService } from './quests.service';
@Module({
  controllers: [QuestsController, QuestsAdminController],
  imports: [
    CommonModule,
    QueueModule,
    TypeOrmModule.forFeature([
      Quest,
      QuestTask,
      Brand,
      QuestTaskUser,
      UserQuest,
      Sponsor,
      SponsorAsset,
      Transaction,
      Article,
      ArticleQuestion,
      ArticleQuestionAnswer,
    ]),
  ],
  providers: [QuestsService, QuestsAdminService],
  exports: [QuestsService, QuestsAdminService],
})
export class QuestsModule {}
