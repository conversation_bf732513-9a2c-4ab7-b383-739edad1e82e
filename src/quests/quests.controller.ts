import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/guard/jwt-auth.guard';
import { User } from 'src/common/decorator/user.decorator';
import { UserPayload } from 'src/entities/user.entity';
import { DoQuestTaskDto } from './dto/do-quest-task.dto';
import { FindQuestsDto } from './dto/find-quests.dto';
import { QuestsService } from './quests.service';

@ApiTags('Quests')
@ApiBearerAuth()
@Controller('quests')
@UseGuards(JwtAuthGuard)
export class QuestsController {
  constructor(private readonly questsService: QuestsService) {}

  @ApiOperation({ summary: 'Find quests' })
  @Get()
  async findQuests(
    @Query() requestData: FindQuestsDto,
    @User() currentUser: UserPayload,
  ) {
    return this.questsService.findQuests(requestData, currentUser);
  }

  @ApiOperation({ summary: 'Get quest detail' })
  @Get(':id')
  async getQuestDetail(
    @Param('id') id: string,
    @User() currentUser: UserPayload,
  ) {
    return this.questsService.getQuestDetail(Number(id), currentUser);
  }

  @ApiOperation({ summary: 'Do quest task' })
  @Post('task')
  async doQuestTask(
    @Body() requestData: DoQuestTaskDto,
    @User() currentUser: UserPayload,
  ) {
    return this.questsService.doQuestTask(requestData, currentUser);
  }
}
