import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ApiError } from 'src/common/api';
import { Utils } from 'src/common/utils';
import { UserPayload } from 'src/entities/user.entity';
import { Brackets, Repository } from 'typeorm';
import { QuestTaskUser } from '../entities/quest-task-user.entity';
import { QuestTask } from '../entities/quest-task.entity';
import {
  Quest,
  QuestStatus,
  QuestStatusByDate,
} from '../entities/quest.entity';
import { UserQuest, UserQuestStatus } from '../entities/user-quest.entity';
import { DoQuestTaskDto } from './dto/do-quest-task.dto';
import { FindQuestsDto } from './dto/find-quests.dto';
import {
  Transaction,
  TransactionStatus,
  TransactionSubType,
  TransactionType,
} from 'src/entities/transaction.entity';
import { QueueService } from 'src/queue/queue.service';
import { ErrorCode } from 'src/common/constants';
import { CommonService } from 'src/common-services/common.service';

@Injectable()
export class QuestsService {
  constructor(
    private commonService: CommonService,
    private queueService: QueueService,
    @InjectRepository(Quest)
    private questRepository: Repository<Quest>,
    @InjectRepository(QuestTask)
    private questTaskRepository: Repository<QuestTask>,
    @InjectRepository(QuestTaskUser)
    private questTaskUserRepository: Repository<QuestTaskUser>,
    @InjectRepository(UserQuest)
    private userQuestRepository: Repository<UserQuest>,
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
  ) {}

  async findQuests(requestData: FindQuestsDto, currentUser: UserPayload) {
    const userClaimStatus = requestData?.userClaimStatus?.split(',');
    const dateStatus = requestData?.dateStatus?.split(',');
    const level = requestData?.level?.split(',');

    const queryBuilder = this.questRepository
      .createQueryBuilder('quest')
      .where('quest.status = :status', { status: QuestStatus.ACTIVE })
      .andWhere('quest.toDate > :currentDate', {
        currentDate: new Date(),
      })
      .leftJoinAndSelect(
        'quest.userQuests',
        'userQuests',
        'userQuests.userId = :userId',
        {
          userId: currentUser.id,
        },
      )
      .leftJoinAndSelect('quest.sponsor', 'sponsor')
      .leftJoinAndSelect('quest.brand', 'brand')
      .select([
        'quest.id',
        'quest.title',
        'quest.status',
        'quest.startDate',
        'quest.toDate',
        'quest.level',
        'quest.point',
        'quest.imageUrl',
        'quest.totalParticipants',
        'quest.createdAt',
        'userQuests',
        'sponsor.name',
        'sponsor.imageUrl',
        'brand.id',
        'brand.name',
        'brand.description',
      ])
      .orderBy('quest.createdAt', 'DESC');

    if (requestData.brandId) {
      queryBuilder.andWhere('quest.brandId = :brandId', {
        brandId: requestData.brandId,
      });
    }

    if (requestData.keyword) {
      Utils.applySearch(queryBuilder, requestData.keyword, [
        { name: 'quest.title', isAbsolute: false },
        { name: 'sponsor.name', isAbsolute: false },
        { name: 'brand.name', isAbsolute: false },
        { name: 'brand.description', isAbsolute: false },
      ]);
    }

    if (requestData.level) {
      queryBuilder.andWhere('quest.level IN (:...levels)', {
        levels: level,
      });
    }

    if (requestData.dateStatus) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          if (dateStatus.includes(QuestStatusByDate.COMING_SOON)) {
            qb.orWhere('quest.startDate > :currentDate', {
              currentDate: new Date(),
            });
          }
          if (dateStatus.includes(QuestStatusByDate.ON_GOING)) {
            qb.orWhere('quest.startDate <= :currentDate', {
              currentDate: new Date(),
            });
          }
        }),
      );
    }

    if (requestData.userClaimStatus) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          if (userClaimStatus.includes(UserQuestStatus.CLAIMED)) {
            qb.orWhere('userQuests.status = :userQuestStatusClaimed', {
              userQuestStatusClaimed: UserQuestStatus.CLAIMED,
            });
          }
          if (userClaimStatus.includes(UserQuestStatus.UNCLAIMED)) {
            qb.orWhere('userQuests.status = :userQuestStatusUnClaimed', {
              userQuestStatusUnClaimed: UserQuestStatus.UNCLAIMED,
            }).orWhere('userQuests.status IS NULL');
          }
        }),
      );
    }

    return Utils.paginate<Quest>(queryBuilder, requestData);
  }

  async getQuestDetail(questId: number, currentUser: UserPayload) {
    const quest = await this.questRepository
      .createQueryBuilder('quest')
      .where('quest.id = :questId', { questId })
      .leftJoinAndSelect('quest.questTasks', 'questTasks')
      .leftJoinAndSelect(
        'questTasks.questTaskUsers',
        'questTaskUsers',
        'questTaskUsers.userId = :userId',
        { userId: currentUser.id },
      )
      .leftJoinAndSelect(
        'quest.userQuests',
        'userQuests',
        'userQuests.userId = :userId',
        { userId: currentUser.id },
      )
      .leftJoinAndSelect('quest.sponsor', 'sponsor')
      .select([
        'quest.id',
        'quest.title',
        'quest.description',
        'quest.status',
        'quest.startDate',
        'quest.toDate',
        'quest.level',
        'quest.point',
        'quest.imageUrl',
        'quest.totalParticipants',
        'questTasks',
        'questTaskUsers',
        'userQuests',
        'sponsor.id',
        'sponsor.name',
        'sponsor.imageUrl',
        'sponsor.description',
        'sponsor.website',
        'sponsor.email',
      ])
      .orderBy('questTasks.orderIndex', 'ASC')
      .getOne();

    // TODO: add select

    if (!quest) {
      throw ApiError('', `Quest with ID ${questId} not found`);
    }

    // // Create wallet
    // const chain = await this.commonService.getChainBySponsorAssetId(
    //   quest.sponsorAssetId,
    // );
    // await this.commonService.createUserChain({ chain, userId: currentUser.id });

    return quest;
  }

  async doQuestTask(requestData: DoQuestTaskDto, currentUser: UserPayload) {
    // Find the quest task first
    const questTask = await this.questTaskRepository
      .createQueryBuilder('questTask')
      .innerJoinAndSelect('questTask.quest', 'quest')
      .where('questTask.id = :questTaskId', {
        questTaskId: requestData.questTaskId,
      })
      .leftJoinAndSelect(
        'questTask.questTaskUsers',
        'questTaskUsers',
        'questTaskUsers.userId = :userId',
        { userId: currentUser.id },
      )
      .getOne();

    if (!questTask) {
      throw ApiError('QUEST_TASK_NOT_FOUND', 'Quest task not found');
    }

    // Check if quest is active
    if (questTask.quest.status !== QuestStatus.ACTIVE) {
      throw ApiError('QUEST_INACTIVE', 'This quest is not active');
    }

    // Check if task is already completed
    if (questTask.questTaskUsers?.length > 0) {
      throw ApiError(
        'TASK_ALREADY_COMPLETED',
        'You have already completed this task',
      );
    }

    // Create quest task user record
    const questTaskUser = await this.questTaskUserRepository.save({
      questTaskId: requestData.questTaskId,
      userId: currentUser.id,
    });

    // Check if all tasks are completed for this quest
    const allTasksCompleted = await this.checkQuestCompletion(
      questTask.quest.id,
      currentUser.id,
    );

    if (allTasksCompleted) {
      await this.userQuestRepository.save({
        questId: questTask.quest.id,
        userId: currentUser.id,
      });
    }

    const countQuestTaskUser = await this.questTaskUserRepository.count({
      relations: {
        questTask: true,
      },
      where: {
        questTask: {
          questId: questTask.quest.id,
        },
        userId: currentUser.id,
      },
    });
    if (countQuestTaskUser === 1) {
      await this.questRepository.update(questTask.quest.id, {
        totalParticipants: () => 'totalParticipants + 1',
      });
    }

    return {
      questTaskUser,
      questCompleted: allTasksCompleted,
    };
  }

  private async checkQuestCompletion(questId: number, userId: number) {
    const quest = await this.questRepository
      .createQueryBuilder('quest')
      .where('quest.id = :questId', { questId })
      .leftJoinAndSelect('quest.questTasks', 'questTasks')
      .leftJoinAndSelect(
        'questTasks.questTaskUsers',
        'questTaskUsers',
        'questTaskUsers.userId = :userId',
        { userId },
      )
      .getOne();

    if (!quest) return false;

    // Check if all tasks have corresponding questTaskUsers entries
    return quest.questTasks.every(
      (task) => task.questTaskUsers && task.questTaskUsers.length > 0,
    );
  }
}
